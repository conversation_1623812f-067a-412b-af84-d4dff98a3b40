# RAG系统数据库服务

services:
  # Neo4j图数据库
  neo4j:
    image: neo4j:latest
    container_name: rag_neo4j
    ports:
      - "17474:7474"  # Web界面
      - "17687:7687"  # Bolt协议
    environment:
      - NEO4J_AUTH=neo4j/your_password
      - NEO4J_dbms_memory_pagecache_size=1G  # 页面缓存大小
      - NEO4J_dbms.memory.heap.initial_size=1G  # 初始堆内存
      - NEO4J_dbms_memory_heap_max__size=1G  # 最大堆内存
      - NEO4J_ACCEPT_LICENSE_AGREEMENT=yes  # 接受许可协议
      - NEO4J_apoc_export_file_enabled=true  # 启用APOC导出功能
      - NEO4J_apoc_import_file_enabled=true  # 启用APOC导入功能
      - NEO4J_apoc_import_file_use__neo4j__config=true  # 使用Neo4j配置
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*  # 允许执行APOC过程
#    volumes:
#      - neo4j_data:/data
#      - neo4j_logs:/logs
#      - neo4j_import:/var/lib/neo4j/import
#      - neo4j_plugins:/plugins
    restart: unless-stopped
    networks:
      - rag_network

  # Redis缓存数据库
  redis:
    image: redis:latest
    container_name: rag_redis
    ports:
      - "16379:6379"
    command: redis-server --appendonly yes --requirepass your_redis_password
#    volumes:
#      - redis_data:/data
    restart: unless-stopped
    networks:
      - rag_network

  # PostgreSQL关系数据库 (可选)
  postgres:
    image: postgres
    container_name: rag_postgres
    ports:
      - "15432:5432"
    environment:
      - POSTGRES_DB=rag_system
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=your_postgres_password
#    volumes:
#      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped
    networks:
      - rag_network

  # Qdrant向量数据库
  qdrant:
    image: qdrant/qdrant:latest
    container_name: rag_qdrant
    ports:
      - "16333:6333"
      - "16334:6334"
#    volumes:
#      - qdrant_data:/qdrant/storage
    restart: unless-stopped
    networks:
      - rag_network

  minio:
    image: minio/minio:latest
    container_name: rag_minio
    ports:
      - "19000:9000"
      - "19001:9001"
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin
    command: server /data --console-address :9001
#    volumes:
#      - minio_data:/data
    restart: unless-stopped
    networks:
      - rag_network

#volumes:
#  neo4j_data:
#    driver: local
#  neo4j_logs:
#    driver: local
#  neo4j_import:
#    driver: local
#  neo4j_plugins:
#    driver: local
#  redis_data:
#    driver: local
#  postgres_data:
#    driver: local
#  qdrant_data:
#    driver: local

networks:
  rag_network:
    driver: bridge
