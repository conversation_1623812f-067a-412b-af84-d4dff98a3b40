2025-07-22 10:00:23 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 10:00:31 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 10:00:31 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 10:00:31 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 10:00:31 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 10:00:31 | INFO     | __main__:lifespan:24 | RAG系统启动中...
2025-07-22 10:00:32 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 10:00:32 | INFO     | __main__:lifespan:28 | PostgreSQL数据库连接成功
2025-07-22 10:00:32 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 10:00:34 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 10:00:34 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 10:00:34 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 10:00:35 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 10:00:35 | INFO     | app.service.document.async_parse_queue:start_workers:70 | 启动了 3 个工作进程
2025-07-22 10:00:35 | INFO     | app.service.document.parse_document:initialize_document_queue:225 | 文档处理队列已启动
2025-07-22 10:02:02 | INFO     | app.service.document.async_parse_queue:add_task:60 | 任务已加入队列: cae8f7e8-a7bc-46d6-a781-9ace9fb8d3a6 (file_hash: 63abda32e7db0048f82841b375747933)
2025-07-22 10:02:02 | INFO     | app.service.document.async_parse_queue:add_task:60 | 任务已加入队列: ba0d06f3-1cd5-4035-82b8-48091969cc98 (file_hash: f6480c3b03f939d84ce3963e381f9a0a)
2025-07-22 10:02:02 | INFO     | app.service.document.async_parse_queue:add_task:60 | 任务已加入队列: 168e10f7-2fe8-4092-afb5-51aca7b09e0a (file_hash: 4e4ae2228f2e9132cc89ed86f8ada5c3)
2025-07-22 10:02:02 | INFO     | app.service.document.async_parse_queue:add_task:60 | 任务已加入队列: cb448af1-c858-460f-b1cd-144c2e300942 (file_hash: a795e7f69478f5a6e2b2b77c82b29100)
2025-07-22 10:02:02 | INFO     | app.service.document.async_parse_queue:_process_task:125 | 工作进程 worker_2 开始处理任务: cb448af1-c858-460f-b1cd-144c2e300942
2025-07-22 10:02:02 | INFO     | app.service.document.async_parse_queue:_process_task:125 | 工作进程 worker_0 开始处理任务: ba0d06f3-1cd5-4035-82b8-48091969cc98
2025-07-22 10:02:02 | INFO     | app.service.document.async_parse_queue:_process_task:125 | 工作进程 worker_1 开始处理任务: cae8f7e8-a7bc-46d6-a781-9ace9fb8d3a6
2025-07-22 10:02:02 | INFO     | app.service.document.document_processor:_parse_document:213 | 开始解析文件: a795e7f69478f5a6e2b2b77c82b29100.pdf
2025-07-22 10:03:55 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 10:04:03 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 10:04:03 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 10:04:03 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 10:04:03 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 10:04:03 | INFO     | __main__:lifespan:24 | RAG系统启动中...
2025-07-22 10:04:04 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 10:04:04 | INFO     | __main__:lifespan:28 | PostgreSQL数据库连接成功
2025-07-22 10:04:04 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 10:04:05 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 10:04:05 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 10:04:05 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 10:04:06 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 10:04:06 | INFO     | app.service.document.async_parse_queue:start_workers:71 | 启动了 3 个工作进程
2025-07-22 10:04:06 | INFO     | app.service.document.parse_document:initialize_document_queue:225 | 文档处理队列已启动
2025-07-22 10:04:06 | INFO     | app.service.document.async_parse_queue:_process_task:126 | 工作进程 worker_0 开始处理任务: 168e10f7-2fe8-4092-afb5-51aca7b09e0a
2025-07-22 10:04:06 | INFO     | app.service.document.document_processor:_parse_document:213 | 开始解析文件: 4e4ae2228f2e9132cc89ed86f8ada5c3.pdf
2025-07-22 10:05:06 | INFO     | app.service.document.document_processor:process_document:122 | 开始分块: 4e4ae2228f2e9132cc89ed86f8ada5c3.pdf
2025-07-22 10:05:06 | INFO     | app.service.document.document_processor:process_document:126 | 共有6 片
2025-07-22 10:05:08 | WARNING  | app.service.document.parse_document:parse_document_service:202 | 文件 63abda32e7db0048f82841b375747933 正在处理中
2025-07-22 10:05:08 | WARNING  | app.service.document.parse_document:parse_document_service:202 | 文件 f6480c3b03f939d84ce3963e381f9a0a 正在处理中
2025-07-22 10:05:08 | WARNING  | app.service.document.parse_document:parse_document_service:202 | 文件 4e4ae2228f2e9132cc89ed86f8ada5c3 正在处理中
2025-07-22 10:05:08 | WARNING  | app.service.document.parse_document:parse_document_service:202 | 文件 a795e7f69478f5a6e2b2b77c82b29100 正在处理中
2025-07-22 10:05:12 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:05:12 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:05:12 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:05:13 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:05:13 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:05:13 | WARNING  | app.service.document.parse_document:parse_document_service:202 | 文件 63abda32e7db0048f82841b375747933 正在处理中
2025-07-22 10:05:13 | WARNING  | app.service.document.parse_document:parse_document_service:202 | 文件 f6480c3b03f939d84ce3963e381f9a0a 正在处理中
2025-07-22 10:05:13 | WARNING  | app.service.document.parse_document:parse_document_service:202 | 文件 4e4ae2228f2e9132cc89ed86f8ada5c3 正在处理中
2025-07-22 10:05:13 | WARNING  | app.service.document.parse_document:parse_document_service:202 | 文件 a795e7f69478f5a6e2b2b77c82b29100 正在处理中
2025-07-22 10:05:14 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:05:14 | INFO     | app.service.document.document_processor:process_document:131 | 向量化开始
2025-07-22 10:05:14 | INFO     | app.database.qdrant_manager:store_vectors:85 | 开始插入 6 个向量，批次大小: 100
2025-07-22 10:05:14 | INFO     | app.database.qdrant_manager:store_vectors:94 | 成功插入批次，大小: 6
2025-07-22 10:05:14 | INFO     | app.database.qdrant_manager:store_vectors:102 | 向量插入完成，总耗时: 0.10s
2025-07-22 10:05:14 | INFO     | app.service.document.document_processor:process_document:135 | 摘要生成开始
2025-07-22 10:05:23 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 1899
2025-07-22 10:05:23 | INFO     | app.service.document.document_processor:_finalize_processing:243 | 摘要生成完成: 4e4ae2228f2e9132cc89ed86f8ada5c3.pdf
2025-07-22 10:05:23 | INFO     | app.service.document.document_processor:process_document:141 | 文档处理完成: 4e4ae2228f2e9132cc89ed86f8ada5c3.pdf
2025-07-22 10:05:23 | INFO     | app.service.document.async_parse_queue:_complete_task:163 | 任务处理成功: 168e10f7-2fe8-4092-afb5-51aca7b09e0a
2025-07-22 10:05:51 | WARNING  | app.service.document.parse_document:parse_document_service:202 | 文件 63abda32e7db0048f82841b375747933 正在处理中
2025-07-22 10:05:51 | WARNING  | app.service.document.parse_document:parse_document_service:202 | 文件 f6480c3b03f939d84ce3963e381f9a0a 正在处理中
2025-07-22 10:05:51 | INFO     | app.service.document.async_parse_queue:add_task:61 | 任务已加入队列: ae7a63f0-3865-4d51-86a7-1e1703db1bfb (file_hash: 2210e708f388f9d17802f579c61dce13)
2025-07-22 10:05:51 | INFO     | app.service.document.async_parse_queue:add_task:61 | 任务已加入队列: b78a46a8-d688-44f5-a969-254836801f1e (file_hash: c723abbb1ed5438774da530d0a49f91b)
2025-07-22 10:05:52 | INFO     | app.service.document.async_parse_queue:_process_task:126 | 工作进程 worker_2 开始处理任务: b78a46a8-d688-44f5-a969-254836801f1e
2025-07-22 10:05:52 | INFO     | app.service.document.async_parse_queue:_process_task:126 | 工作进程 worker_1 开始处理任务: ae7a63f0-3865-4d51-86a7-1e1703db1bfb
2025-07-22 10:05:52 | INFO     | app.service.document.document_processor:_parse_document:213 | 开始解析文件: c723abbb1ed5438774da530d0a49f91b.pdf
2025-07-22 10:06:33 | INFO     | app.service.document.document_processor:process_document:122 | 开始分块: c723abbb1ed5438774da530d0a49f91b.pdf
2025-07-22 10:06:34 | INFO     | app.service.document.document_processor:process_document:126 | 共有12 片
2025-07-22 10:06:34 | WARNING  | app.service.document.parse_document:parse_document_service:202 | 文件 63abda32e7db0048f82841b375747933 正在处理中
2025-07-22 10:06:34 | WARNING  | app.service.document.parse_document:parse_document_service:202 | 文件 f6480c3b03f939d84ce3963e381f9a0a 正在处理中
2025-07-22 10:06:34 | INFO     | app.service.document.document_processor:_parse_document:213 | 开始解析文件: 2210e708f388f9d17802f579c61dce13.pdf
2025-07-22 10:07:24 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:07:24 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:07:24 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:07:24 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:07:24 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:07:24 | WARNING  | app.service.document.parse_document:parse_document_service:202 | 文件 2210e708f388f9d17802f579c61dce13 正在处理中
2025-07-22 10:07:24 | INFO     | app.service.document.document_processor:process_document:122 | 开始分块: 2210e708f388f9d17802f579c61dce13.pdf
2025-07-22 10:07:24 | WARNING  | app.service.document.parse_document:parse_document_service:202 | 文件 63abda32e7db0048f82841b375747933 正在处理中
2025-07-22 10:07:24 | WARNING  | app.service.document.parse_document:parse_document_service:202 | 文件 c723abbb1ed5438774da530d0a49f91b 正在处理中
2025-07-22 10:07:24 | WARNING  | app.service.document.parse_document:parse_document_service:202 | 文件 f6480c3b03f939d84ce3963e381f9a0a 正在处理中
2025-07-22 10:07:24 | INFO     | app.service.document.document_processor:process_document:126 | 共有15 片
2025-07-22 10:07:24 | WARNING  | app.service.document.parse_document:parse_document_service:202 | 文件 2210e708f388f9d17802f579c61dce13 正在处理中
2025-07-22 10:07:24 | WARNING  | app.service.document.parse_document:parse_document_service:202 | 文件 c723abbb1ed5438774da530d0a49f91b 正在处理中
2025-07-22 10:07:25 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:07:35 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:07:35 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:07:35 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:07:35 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:07:35 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:07:35 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:07:35 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:07:35 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:07:35 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:07:36 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:07:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:07:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:07:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:07:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:07:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:07:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:07:39 | INFO     | app.service.document.document_processor:process_document:131 | 向量化开始
2025-07-22 10:07:39 | INFO     | app.database.qdrant_manager:store_vectors:85 | 开始插入 12 个向量，批次大小: 100
2025-07-22 10:07:39 | INFO     | app.database.qdrant_manager:store_vectors:94 | 成功插入批次，大小: 12
2025-07-22 10:07:39 | INFO     | app.database.qdrant_manager:store_vectors:102 | 向量插入完成，总耗时: 0.09s
2025-07-22 10:07:39 | INFO     | app.service.document.document_processor:process_document:135 | 摘要生成开始
2025-07-22 10:07:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:07:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:07:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:07:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:07:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:07:41 | INFO     | app.service.document.document_processor:process_document:131 | 向量化开始
2025-07-22 10:07:41 | INFO     | app.database.qdrant_manager:store_vectors:85 | 开始插入 15 个向量，批次大小: 100
2025-07-22 10:07:41 | INFO     | app.database.qdrant_manager:store_vectors:94 | 成功插入批次，大小: 15
2025-07-22 10:07:41 | INFO     | app.database.qdrant_manager:store_vectors:102 | 向量插入完成，总耗时: 0.10s
2025-07-22 10:07:41 | INFO     | app.service.document.document_processor:process_document:135 | 摘要生成开始
2025-07-22 10:07:54 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 1594
2025-07-22 10:07:54 | INFO     | app.service.document.document_processor:_finalize_processing:243 | 摘要生成完成: 2210e708f388f9d17802f579c61dce13.pdf
2025-07-22 10:07:54 | INFO     | app.service.document.document_processor:process_document:141 | 文档处理完成: 2210e708f388f9d17802f579c61dce13.pdf
2025-07-22 10:07:54 | INFO     | app.service.document.async_parse_queue:_complete_task:163 | 任务处理成功: ae7a63f0-3865-4d51-86a7-1e1703db1bfb
2025-07-22 10:07:59 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 3250
2025-07-22 10:07:59 | INFO     | app.service.document.document_processor:_finalize_processing:243 | 摘要生成完成: c723abbb1ed5438774da530d0a49f91b.pdf
2025-07-22 10:07:59 | INFO     | app.service.document.document_processor:process_document:141 | 文档处理完成: c723abbb1ed5438774da530d0a49f91b.pdf
2025-07-22 10:08:00 | INFO     | app.service.document.async_parse_queue:_complete_task:163 | 任务处理成功: b78a46a8-d688-44f5-a969-254836801f1e
2025-07-22 10:18:16 | INFO     | app.api.knowledge:create_knowledge:42 | 成功创建知识库: aaaa
2025-07-22 10:50:49 | INFO     | app.service.report_generator:generate_report_stream:41 | 开始生成报告: 针对中国政府在三江源地区的环境保护工作，相关机构各人分别提出了哪些观点，请分类概括
2025-07-22 10:51:23 | INFO     | app.service.research_planner:generate_research_plan:88 | 成功生成研究计划，包含8个搜索任务
2025-07-22 10:51:29 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:51:30 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 10:51:30 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:51:30 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 10:51:30 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:51:30 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 10:51:30 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:51:31 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 10:51:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:51:31 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 10:51:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:51:31 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 10:51:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:51:31 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 10:51:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:51:31 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 10:51:31 | ERROR    | app.service.report_generator:generate_report_stream:193 | 报告生成失败: Object of type dict_keys is not JSON serializable
2025-07-22 10:54:24 | INFO     | __main__:lifespan:39 | RAG系统关闭中...
2025-07-22 10:54:24 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_1 被取消
2025-07-22 10:54:24 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_2 被取消
2025-07-22 10:54:24 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_0 被取消
2025-07-22 10:54:39 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 10:55:12 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 10:55:16 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 10:55:16 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 10:55:16 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 10:55:16 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 10:55:16 | INFO     | __main__:lifespan:24 | RAG系统启动中...
2025-07-22 10:55:16 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 10:55:16 | INFO     | __main__:lifespan:28 | PostgreSQL数据库连接成功
2025-07-22 10:55:16 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 10:55:18 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 10:55:18 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 10:55:18 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 10:55:18 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 10:55:18 | INFO     | app.service.document.async_parse_queue:start_workers:71 | 启动了 3 个工作进程
2025-07-22 10:55:18 | INFO     | app.service.document.parse_document:initialize_document_queue:225 | 文档处理队列已启动
2025-07-22 10:55:22 | INFO     | app.service.report_generator:generate_report_stream:42 | 开始生成报告: 针对中国政府在三江源地区的环境保护工作，相关机构各人分别提出了哪些观点，请分类概括
2025-07-22 10:55:55 | INFO     | app.service.research_planner:generate_research_plan:88 | 成功生成研究计划，包含8个搜索任务
2025-07-22 10:56:00 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:56:00 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 10:56:01 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:56:01 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 10:56:01 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:56:01 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 10:56:01 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:56:01 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 10:56:02 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:56:02 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 10:56:02 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:56:02 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 10:56:02 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:56:02 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 10:56:02 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:56:03 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 10:56:03 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 10:56:03 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 10:56:03 | ERROR    | app.service.report_generator:generate_report_stream:199 | 报告生成失败: Object of type dict_keys is not JSON serializable
2025-07-22 11:01:16 | INFO     | __main__:lifespan:39 | RAG系统关闭中...
2025-07-22 11:01:16 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_1 被取消
2025-07-22 11:01:16 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_0 被取消
2025-07-22 11:01:16 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_2 被取消
2025-07-22 11:01:28 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 11:01:32 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 11:01:32 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 11:01:32 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 11:01:32 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 11:01:32 | INFO     | __main__:lifespan:24 | RAG系统启动中...
2025-07-22 11:01:32 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 11:01:32 | INFO     | __main__:lifespan:28 | PostgreSQL数据库连接成功
2025-07-22 11:01:32 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 11:01:44 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 11:01:44 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 11:01:44 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 11:01:44 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 11:01:44 | INFO     | app.service.document.async_parse_queue:start_workers:71 | 启动了 3 个工作进程
2025-07-22 11:01:44 | INFO     | app.service.document.parse_document:initialize_document_queue:225 | 文档处理队列已启动
2025-07-22 11:01:49 | INFO     | app.service.report_generator:generate_report_stream:42 | 开始生成报告: 针对中国政府在三江源地区的环境保护工作，相关机构各人分别提出了哪些观点，请分类概括
2025-07-22 11:02:33 | INFO     | app.service.research_planner:generate_research_plan:88 | 成功生成研究计划，包含7个搜索任务
2025-07-22 11:02:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:02:40 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:02:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:02:41 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:02:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:02:41 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:02:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:02:41 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:02:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:02:41 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:02:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:02:41 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:02:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:02:41 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:02:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:02:42 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:02:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:02:42 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:02:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:02:42 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:02:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:02:42 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:02:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:02:42 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:02:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:02:42 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:02:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:02:42 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:02:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:02:42 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:02:42 | ERROR    | app.service.report_generator:generate_report_stream:199 | 报告生成失败: Object of type dict_keys is not JSON serializable
2025-07-22 11:04:04 | INFO     | __main__:lifespan:39 | RAG系统关闭中...
2025-07-22 11:04:04 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_1 被取消
2025-07-22 11:04:04 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_0 被取消
2025-07-22 11:04:04 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_2 被取消
2025-07-22 11:04:15 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 11:04:19 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 11:04:19 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 11:04:19 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 11:04:19 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 11:04:19 | INFO     | __main__:lifespan:24 | RAG系统启动中...
2025-07-22 11:04:19 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 11:04:19 | INFO     | __main__:lifespan:28 | PostgreSQL数据库连接成功
2025-07-22 11:04:19 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 11:04:22 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 11:04:22 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 11:04:22 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 11:04:22 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 11:04:22 | INFO     | app.service.document.async_parse_queue:start_workers:71 | 启动了 3 个工作进程
2025-07-22 11:04:22 | INFO     | app.service.document.parse_document:initialize_document_queue:225 | 文档处理队列已启动
2025-07-22 11:04:25 | INFO     | app.service.report_generator:generate_report_stream:42 | 开始生成报告: 针对中国政府在三江源地区的环境保护工作，相关机构各人分别提出了哪些观点，请分类概括
2025-07-22 11:04:47 | INFO     | app.service.research_planner:generate_research_plan:88 | 成功生成研究计划，包含5个搜索任务
2025-07-22 11:04:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:04:54 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:04:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:04:54 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:04:55 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:04:55 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:04:55 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:04:56 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:04:56 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:04:56 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:04:56 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:04:56 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:04:56 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:04:56 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:04:57 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:04:57 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:04:57 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:04:57 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:04:57 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:04:57 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:04:57 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:04:57 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:04:57 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:04:57 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:04:57 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:04:57 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:04:57 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:04:57 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:04:57 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:04:57 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:04:57 | ERROR    | app.service.report_generator:generate_report_stream:187 | 报告生成失败: Object of type dict_keys is not JSON serializable
2025-07-22 11:07:13 | INFO     | __main__:lifespan:39 | RAG系统关闭中...
2025-07-22 11:07:13 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_1 被取消
2025-07-22 11:07:13 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_0 被取消
2025-07-22 11:07:13 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_2 被取消
2025-07-22 11:07:25 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 11:08:06 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 11:08:11 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 11:08:11 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 11:08:11 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 11:08:11 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 11:08:11 | INFO     | __main__:lifespan:24 | RAG系统启动中...
2025-07-22 11:08:11 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 11:08:11 | INFO     | __main__:lifespan:28 | PostgreSQL数据库连接成功
2025-07-22 11:08:11 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 11:08:12 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 11:08:12 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 11:08:12 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 11:08:12 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 11:08:12 | INFO     | app.service.document.async_parse_queue:start_workers:71 | 启动了 3 个工作进程
2025-07-22 11:08:12 | INFO     | app.service.document.parse_document:initialize_document_queue:225 | 文档处理队列已启动
2025-07-22 11:08:16 | INFO     | app.service.report_generator:generate_report_stream:42 | 开始生成报告: 针对中国政府在三江源地区的环境保护工作，相关机构各人分别提出了哪些观点，请分类概括
2025-07-22 11:08:45 | INFO     | app.service.research_planner:generate_research_plan:88 | 成功生成研究计划，包含7个搜索任务
2025-07-22 11:08:58 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:08:58 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:08:58 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:08:58 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:08:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:08:59 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:08:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:08:59 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:08:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:08:59 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:08:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:09:00 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:09:00 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:09:00 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:09:00 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:09:00 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:09:00 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:09:00 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:09:00 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:09:00 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:09:00 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:09:00 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:09:00 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:09:00 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:09:00 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:09:00 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:09:00 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:09:00 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:09:00 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:09:00 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:09:00 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:09:00 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:09:00 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:09:00 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:09:00 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:09:00 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:09:00 | ERROR    | app.service.report_generator:generate_report_stream:187 | 报告生成失败: Object of type dict_keys is not JSON serializable
2025-07-22 11:09:58 | INFO     | __main__:lifespan:39 | RAG系统关闭中...
2025-07-22 11:09:58 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_1 被取消
2025-07-22 11:09:58 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_0 被取消
2025-07-22 11:09:58 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_2 被取消
2025-07-22 11:10:12 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 11:10:16 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 11:10:16 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 11:10:16 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 11:10:16 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 11:10:16 | INFO     | __main__:lifespan:24 | RAG系统启动中...
2025-07-22 11:10:16 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 11:10:16 | INFO     | __main__:lifespan:28 | PostgreSQL数据库连接成功
2025-07-22 11:10:16 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 11:10:18 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 11:10:18 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 11:10:18 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 11:10:18 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 11:10:18 | INFO     | app.service.document.async_parse_queue:start_workers:71 | 启动了 3 个工作进程
2025-07-22 11:10:18 | INFO     | app.service.document.parse_document:initialize_document_queue:225 | 文档处理队列已启动
2025-07-22 11:10:25 | INFO     | app.service.report_generator:generate_report_stream:42 | 开始生成报告: 针对中国政府在三江源地区的环境保护工作，相关机构各人分别提出了哪些观点，请分类概括
2025-07-22 11:10:56 | INFO     | app.service.research_planner:generate_research_plan:88 | 成功生成研究计划，包含8个搜索任务
2025-07-22 11:11:01 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:11:01 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:11:01 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:11:01 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:11:01 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:11:01 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:11:04 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:11:04 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:11:07 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:11:07 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:13:56 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_2 被取消
2025-07-22 11:13:56 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_1 被取消
2025-07-22 11:13:56 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_0 被取消
2025-07-22 11:16:53 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 11:16:57 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 11:16:57 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 11:16:57 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 11:16:57 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 11:16:57 | INFO     | __main__:lifespan:24 | RAG系统启动中...
2025-07-22 11:16:57 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 11:16:58 | INFO     | __main__:lifespan:28 | PostgreSQL数据库连接成功
2025-07-22 11:16:58 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 11:17:12 | WARNING  | app.service.model_client:check_health:74 | LLM 模型服务不可用
2025-07-22 11:17:12 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 11:17:12 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 11:17:13 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 11:17:13 | INFO     | app.service.document.async_parse_queue:start_workers:71 | 启动了 3 个工作进程
2025-07-22 11:17:13 | INFO     | app.service.document.parse_document:initialize_document_queue:225 | 文档处理队列已启动
2025-07-22 11:17:16 | INFO     | app.service.report_generator:generate_report_stream:42 | 开始生成报告: 针对中国政府在三江源地区的环境保护工作，相关机构各人分别提出了哪些观点，请分类概括
2025-07-22 11:17:39 | ERROR    | app.service.research_planner:generate_research_plan:92 | 生成研究计划失败: <!DOCTYPE html>
<html>
<head>
<title>Error</title>
<style>
html { color-scheme: light dark; }
body { width: 35em; margin: 0 auto;
font-family: Tahoma, Verdana, Arial, sans-serif; }
</style>
</head>
<body>
<h1>An error occurred.</h1>
<p>Sorry, the page you are looking for is currently unavailable.<br/>
Please try again later.</p>
<p>If you are the system administrator of this resource then you should check
the error log for details.</p>
<p><em>Faithfully yours, nginx.</em></p>
</body>
</html>
2025-07-22 11:17:39 | ERROR    | app.service.report_generator:generate_report_stream:187 | 报告生成失败: <!DOCTYPE html>
<html>
<head>
<title>Error</title>
<style>
html { color-scheme: light dark; }
body { width: 35em; margin: 0 auto;
font-family: Tahoma, Verdana, Arial, sans-serif; }
</style>
</head>
<body>
<h1>An error occurred.</h1>
<p>Sorry, the page you are looking for is currently unavailable.<br/>
Please try again later.</p>
<p>If you are the system administrator of this resource then you should check
the error log for details.</p>
<p><em>Faithfully yours, nginx.</em></p>
</body>
</html>
2025-07-22 11:19:56 | INFO     | __main__:lifespan:39 | RAG系统关闭中...
2025-07-22 11:19:56 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_0 被取消
2025-07-22 11:19:56 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_2 被取消
2025-07-22 11:19:56 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_1 被取消
2025-07-22 11:20:07 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 11:20:11 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 11:20:11 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 11:20:11 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 11:20:11 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 11:20:11 | INFO     | __main__:lifespan:24 | RAG系统启动中...
2025-07-22 11:20:11 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 11:20:11 | INFO     | __main__:lifespan:28 | PostgreSQL数据库连接成功
2025-07-22 11:20:11 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 11:20:21 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 11:20:21 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 11:20:21 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 11:20:21 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 11:20:21 | INFO     | app.service.document.async_parse_queue:start_workers:71 | 启动了 3 个工作进程
2025-07-22 11:20:21 | INFO     | app.service.document.parse_document:initialize_document_queue:225 | 文档处理队列已启动
2025-07-22 11:20:25 | INFO     | app.service.report_generator:generate_report_stream:42 | 开始生成报告: 针对中国政府在三江源地区的环境保护工作，相关机构各人分别提出了哪些观点，请分类概括
2025-07-22 11:22:00 | INFO     | __main__:lifespan:39 | RAG系统关闭中...
2025-07-22 11:22:00 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_0 被取消
2025-07-22 11:22:00 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_2 被取消
2025-07-22 11:22:00 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_1 被取消
2025-07-22 11:22:12 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 11:22:16 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 11:22:16 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 11:22:16 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 11:22:16 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 11:22:16 | INFO     | __main__:lifespan:24 | RAG系统启动中...
2025-07-22 11:22:16 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 11:22:16 | INFO     | __main__:lifespan:28 | PostgreSQL数据库连接成功
2025-07-22 11:22:16 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 11:22:17 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 11:22:17 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 11:22:17 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 11:22:18 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 11:22:18 | INFO     | app.service.document.async_parse_queue:start_workers:71 | 启动了 3 个工作进程
2025-07-22 11:22:18 | INFO     | app.service.document.parse_document:initialize_document_queue:225 | 文档处理队列已启动
2025-07-22 11:22:31 | INFO     | app.service.report_generator:generate_report_stream:42 | 开始生成报告: 针对中国政府在三江源地区的环境保护工作，相关机构各人分别提出了哪些观点，请分类概括
2025-07-22 11:23:17 | INFO     | app.service.research_planner:generate_research_plan:88 | 成功生成研究计划，包含8个搜索任务
2025-07-22 11:24:02 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:24:02 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:24:03 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:24:03 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:24:03 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:24:03 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:24:04 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:24:05 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:24:05 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:24:05 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:24:05 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:24:05 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:24:06 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:24:06 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:24:06 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:24:06 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:24:06 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:24:06 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:24:06 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:24:06 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:24:06 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:24:06 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:24:06 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:24:06 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:24:06 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:24:06 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:24:07 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:24:07 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:24:07 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:24:07 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:24:07 | ERROR    | app.service.report_generator:generate_report_stream:187 | 报告生成失败: Object of type dict_keys is not JSON serializable
2025-07-22 11:26:15 | INFO     | __main__:lifespan:39 | RAG系统关闭中...
2025-07-22 11:26:15 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_0 被取消
2025-07-22 11:26:15 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_2 被取消
2025-07-22 11:26:15 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_1 被取消
2025-07-22 11:26:28 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 11:26:32 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 11:26:32 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 11:26:32 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 11:26:32 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 11:26:32 | INFO     | __main__:lifespan:24 | RAG系统启动中...
2025-07-22 11:26:32 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 11:26:32 | INFO     | __main__:lifespan:28 | PostgreSQL数据库连接成功
2025-07-22 11:26:32 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 11:26:33 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 11:26:33 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 11:26:33 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 11:26:34 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 11:26:34 | INFO     | app.service.document.async_parse_queue:start_workers:71 | 启动了 3 个工作进程
2025-07-22 11:26:34 | INFO     | app.service.document.parse_document:initialize_document_queue:225 | 文档处理队列已启动
2025-07-22 11:26:37 | INFO     | app.service.report_generator:generate_report_stream:42 | 开始生成报告: 针对中国政府在三江源地区的环境保护工作，相关机构各人分别提出了哪些观点，请分类概括
2025-07-22 11:27:17 | INFO     | app.service.research_planner:generate_research_plan:88 | 成功生成研究计划，包含8个搜索任务
2025-07-22 11:27:24 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:27:24 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:27:25 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:27:25 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:27:25 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:27:25 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:27:26 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:27:26 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:27:26 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:27:26 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:27:26 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:27:26 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:27:27 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:27:27 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:27:27 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:27:27 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:27:27 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:27:27 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:27:27 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:27:27 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:27:27 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:27:27 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:27:27 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:27:27 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:27:27 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:27:27 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:27:27 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:27:27 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:27:28 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:27:28 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:27:28 | ERROR    | app.service.report_generator:generate_report_stream:187 | 报告生成失败: Object of type dict_keys is not JSON serializable
2025-07-22 11:28:11 | INFO     | __main__:lifespan:39 | RAG系统关闭中...
2025-07-22 11:28:11 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_1 被取消
2025-07-22 11:28:11 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_0 被取消
2025-07-22 11:28:11 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_2 被取消
2025-07-22 11:29:16 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 11:29:20 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 11:29:20 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 11:29:20 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 11:29:20 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 11:29:20 | INFO     | __main__:lifespan:24 | RAG系统启动中...
2025-07-22 11:29:20 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 11:29:20 | INFO     | __main__:lifespan:28 | PostgreSQL数据库连接成功
2025-07-22 11:29:20 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 11:29:29 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 11:29:29 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 11:29:29 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 11:29:30 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 11:29:30 | INFO     | app.service.document.async_parse_queue:start_workers:71 | 启动了 3 个工作进程
2025-07-22 11:29:30 | INFO     | app.service.document.parse_document:initialize_document_queue:225 | 文档处理队列已启动
2025-07-22 11:29:42 | INFO     | app.service.report_generator:generate_report_stream:42 | 开始生成报告: 针对中国政府在三江源地区的环境保护工作，相关机构各人分别提出了哪些观点，请分类概括
2025-07-22 11:30:19 | INFO     | app.service.research_planner:generate_research_plan:88 | 成功生成研究计划，包含8个搜索任务
2025-07-22 11:30:27 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:30:27 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:30:27 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:30:27 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:30:27 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:30:27 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:30:27 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:30:28 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:30:28 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:30:28 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:30:28 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:30:28 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:30:28 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:30:28 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:30:29 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:30:29 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:30:29 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:30:29 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:30:29 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:30:29 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:30:29 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:30:29 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:30:29 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:30:29 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:30:29 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:30:29 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:30:29 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:30:29 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:30:29 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:30:29 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:30:29 | INFO     | app.service.report_generator:generate_report_stream:107 | 搜索任务 1 完成，找到 9 个相关文档
2025-07-22 11:30:36 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:30:36 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:30:36 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:30:36 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:30:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:30:37 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:30:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:30:37 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:30:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:30:37 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:30:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:30:38 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:30:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:30:38 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:30:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:30:38 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:30:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:30:38 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:30:38 | INFO     | app.service.report_generator:generate_report_stream:107 | 搜索任务 2 完成，找到 8 个相关文档
2025-07-22 11:30:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:30:44 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:30:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:30:44 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:30:46 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:30:46 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:30:48 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:30:48 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:30:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:30:49 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:30:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:30:50 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:30:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:30:50 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:30:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:30:50 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:30:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:30:50 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:30:50 | INFO     | app.service.report_generator:generate_report_stream:107 | 搜索任务 3 完成，找到 7 个相关文档
2025-07-22 11:30:57 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:30:57 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:30:57 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:30:57 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:30:58 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:30:58 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:30:58 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:30:58 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:30:58 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:30:58 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:30:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:30:59 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:30:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:30:59 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:30:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:30:59 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:30:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:30:59 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:30:59 | INFO     | app.service.report_generator:generate_report_stream:107 | 搜索任务 4 完成，找到 8 个相关文档
2025-07-22 11:31:05 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:31:05 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:31:06 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:31:06 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:31:06 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:31:06 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:31:07 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:31:07 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:31:08 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:31:08 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:31:08 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:31:08 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:31:08 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:31:08 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:31:08 | INFO     | app.service.report_generator:generate_report_stream:107 | 搜索任务 5 完成，找到 5 个相关文档
2025-07-22 11:31:48 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:31:48 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:31:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:31:49 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:31:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:31:49 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:31:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:31:50 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:31:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:31:50 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:31:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:31:50 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:31:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:31:50 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:31:50 | INFO     | app.service.report_generator:generate_report_stream:107 | 搜索任务 6 完成，找到 5 个相关文档
2025-07-22 11:31:56 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:31:56 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:31:56 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:31:56 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:31:57 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:31:57 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:31:57 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:31:57 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:31:58 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:31:58 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:31:58 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:31:58 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:31:58 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:31:58 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:31:58 | INFO     | app.service.report_generator:generate_report_stream:107 | 搜索任务 7 完成，找到 8 个相关文档
2025-07-22 11:32:05 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:05 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:32:06 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:06 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:32:06 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:06 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:32:08 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:08 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:32:09 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:09 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:32:09 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:09 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:32:09 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:09 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:32:09 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:09 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:32:09 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:09 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:32:09 | INFO     | app.service.report_generator:generate_report_stream:107 | 搜索任务 8 完成，找到 6 个相关文档
2025-07-22 11:32:20 | INFO     | app.service.research_planner:analyze_content_completeness:150 | 内容分析完成，覆盖度评分: 0.6
2025-07-22 11:32:28 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:28 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:32:29 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:29 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:32:29 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:29 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:32:29 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:29 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:32:29 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:29 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:32:30 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:30 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:32:30 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:30 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:32:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:31 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:32:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:31 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:32:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:31 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:32:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:31 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:32:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:31 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:32:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:31 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:32:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:31 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:32:33 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:33 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:32:33 | INFO     | app.service.report_generator:generate_report_stream:147 | 补充搜索找到 5 个额外文档
2025-07-22 11:32:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:41 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:32:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:41 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:32:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:41 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:32:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:41 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:32:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:42 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:32:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:42 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:32:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:42 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:32:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:42 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:32:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:42 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:32:42 | INFO     | app.service.report_generator:generate_report_stream:147 | 补充搜索找到 2 个额外文档
2025-07-22 11:32:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:50 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:32:51 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:51 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:32:51 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:51 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:32:51 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:51 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:32:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:52 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:32:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:52 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:32:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:53 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:32:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:54 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:32:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:54 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:32:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:54 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:32:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:54 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:32:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:54 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:32:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:54 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:32:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:54 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:32:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:54 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:32:56 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:32:56 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:32:56 | INFO     | app.service.report_generator:generate_report_stream:147 | 补充搜索找到 5 个额外文档
2025-07-22 11:32:57 | ERROR    | app.service.report_generator:generate_report_stream:187 | 报告生成失败: 'full_content'
2025-07-22 11:39:05 | INFO     | __main__:lifespan:39 | RAG系统关闭中...
2025-07-22 11:39:05 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_1 被取消
2025-07-22 11:39:05 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_0 被取消
2025-07-22 11:39:05 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_2 被取消
2025-07-22 11:39:16 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 11:39:20 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 11:39:20 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 11:39:20 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 11:39:20 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 11:39:20 | INFO     | __main__:lifespan:24 | RAG系统启动中...
2025-07-22 11:39:20 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 11:39:20 | INFO     | __main__:lifespan:28 | PostgreSQL数据库连接成功
2025-07-22 11:39:20 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 11:39:24 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 11:39:24 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 11:39:24 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 11:39:24 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 11:39:24 | INFO     | app.service.document.async_parse_queue:start_workers:71 | 启动了 3 个工作进程
2025-07-22 11:39:24 | INFO     | app.service.document.parse_document:initialize_document_queue:225 | 文档处理队列已启动
2025-07-22 11:39:27 | INFO     | app.service.report_generator:generate_report_stream:42 | 开始生成报告: 针对中国政府在三江源地区的环境保护工作，相关机构各人分别提出了哪些观点，请分类概括
2025-07-22 11:40:05 | INFO     | app.service.research_planner:generate_research_plan:88 | 成功生成研究计划，包含7个搜索任务
2025-07-22 11:40:13 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:40:13 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:40:13 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:40:13 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:40:13 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:40:13 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:40:14 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:40:14 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:40:14 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:40:14 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:40:15 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:40:15 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:40:16 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:40:16 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:40:17 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:40:17 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:40:17 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:40:17 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:40:17 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:40:17 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:40:17 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:40:17 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:40:17 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:40:17 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:40:17 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:40:17 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:40:17 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:40:17 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:40:17 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:40:17 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:40:17 | ERROR    | app.service.report_generator:generate_report_stream:187 | 报告生成失败: Object of type dict_keys is not JSON serializable
2025-07-22 11:42:33 | INFO     | __main__:lifespan:39 | RAG系统关闭中...
2025-07-22 11:42:33 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_1 被取消
2025-07-22 11:42:33 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_0 被取消
2025-07-22 11:42:33 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_2 被取消
2025-07-22 11:42:45 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 11:42:49 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 11:42:49 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 11:42:49 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 11:42:49 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 11:42:49 | INFO     | __main__:lifespan:24 | RAG系统启动中...
2025-07-22 11:42:49 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 11:42:49 | INFO     | __main__:lifespan:28 | PostgreSQL数据库连接成功
2025-07-22 11:42:49 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 11:42:51 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 11:42:51 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 11:42:51 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 11:42:52 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 11:42:52 | INFO     | app.service.document.async_parse_queue:start_workers:71 | 启动了 3 个工作进程
2025-07-22 11:42:52 | INFO     | app.service.document.parse_document:initialize_document_queue:225 | 文档处理队列已启动
2025-07-22 11:42:55 | INFO     | app.service.report_generator:generate_report_stream:42 | 开始生成报告: 针对中国政府在三江源地区的环境保护工作，相关机构各人分别提出了哪些观点，请分类概括
2025-07-22 11:43:31 | INFO     | app.service.research_planner:generate_research_plan:88 | 成功生成研究计划，包含7个搜索任务
2025-07-22 11:43:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:43:38 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:43:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:43:39 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:43:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:43:40 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:43:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:43:40 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:43:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:43:40 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:43:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:43:40 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:43:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:43:40 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:43:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:43:40 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:43:40 | ERROR    | app.service.report_generator:generate_report_stream:188 | 报告生成失败: Object of type dict_keys is not JSON serializable
2025-07-22 11:44:15 | INFO     | __main__:lifespan:39 | RAG系统关闭中...
2025-07-22 11:44:15 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_1 被取消
2025-07-22 11:44:15 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_0 被取消
2025-07-22 11:44:15 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_2 被取消
2025-07-22 11:44:26 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 11:44:30 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 11:44:30 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 11:44:30 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 11:44:30 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 11:44:30 | INFO     | __main__:lifespan:24 | RAG系统启动中...
2025-07-22 11:44:30 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 11:44:30 | INFO     | __main__:lifespan:28 | PostgreSQL数据库连接成功
2025-07-22 11:44:30 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 11:44:32 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 11:44:32 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 11:44:32 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 11:44:32 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 11:44:32 | INFO     | app.service.document.async_parse_queue:start_workers:71 | 启动了 3 个工作进程
2025-07-22 11:44:32 | INFO     | app.service.document.parse_document:initialize_document_queue:225 | 文档处理队列已启动
2025-07-22 11:44:34 | INFO     | app.service.report_generator:generate_report_stream:42 | 开始生成报告: 针对中国政府在三江源地区的环境保护工作，相关机构各人分别提出了哪些观点，请分类概括
2025-07-22 11:45:16 | INFO     | app.service.research_planner:generate_research_plan:88 | 成功生成研究计划，包含8个搜索任务
2025-07-22 11:45:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:45:59 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:45:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:45:59 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:46:00 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:46:00 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:46:00 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:46:00 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:46:00 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:46:00 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:46:01 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:46:01 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:46:03 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:46:03 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:46:05 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:46:05 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:46:05 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:46:05 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:46:05 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:46:05 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:46:05 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:46:05 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:46:05 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:46:05 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:46:05 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:46:05 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:46:05 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:46:05 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:46:07 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:46:07 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:46:07 | ERROR    | app.service.report_generator:generate_report_stream:188 | 报告生成失败: Object of type dict_keys is not JSON serializable
2025-07-22 11:48:06 | INFO     | __main__:lifespan:39 | RAG系统关闭中...
2025-07-22 11:48:06 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_1 被取消
2025-07-22 11:48:06 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_0 被取消
2025-07-22 11:48:06 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_2 被取消
2025-07-22 11:48:17 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 11:48:21 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 11:48:21 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 11:48:21 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 11:48:21 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 11:48:21 | INFO     | __main__:lifespan:24 | RAG系统启动中...
2025-07-22 11:48:22 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 11:48:22 | INFO     | __main__:lifespan:28 | PostgreSQL数据库连接成功
2025-07-22 11:48:22 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 11:48:23 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 11:48:23 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 11:48:23 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 11:48:23 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 11:48:23 | INFO     | app.service.document.async_parse_queue:start_workers:71 | 启动了 3 个工作进程
2025-07-22 11:48:23 | INFO     | app.service.document.parse_document:initialize_document_queue:225 | 文档处理队列已启动
2025-07-22 11:48:27 | INFO     | app.service.report_generator:generate_report_stream:42 | 开始生成报告: 针对中国政府在三江源地区的环境保护工作，相关机构各人分别提出了哪些观点，请分类概括
2025-07-22 11:49:07 | INFO     | app.service.research_planner:generate_research_plan:88 | 成功生成研究计划，包含8个搜索任务
2025-07-22 11:49:14 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:14 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:49:15 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:15 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:49:15 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:15 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:49:16 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:16 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:49:16 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:16 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:49:18 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:18 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:49:18 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:18 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:49:19 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:19 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:49:19 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:19 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:49:19 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:19 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:49:19 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:19 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:49:19 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:19 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:49:19 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:19 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:49:19 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:19 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:49:19 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:19 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:49:19 | INFO     | app.service.report_generator:generate_report_stream:107 | 搜索任务 1 完成，找到 9 个相关文档
2025-07-22 11:49:27 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:27 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:49:27 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:27 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:49:27 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:27 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:49:28 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:28 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:49:29 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:29 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:49:29 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:29 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:49:29 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:29 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:49:30 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:30 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:49:30 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:30 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:49:30 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:30 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:49:30 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:30 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:49:30 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:30 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:49:30 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:30 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:49:30 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:30 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:49:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:31 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:49:31 | INFO     | app.service.report_generator:generate_report_stream:107 | 搜索任务 2 完成，找到 8 个相关文档
2025-07-22 11:49:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:39 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:49:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:39 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:49:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:40 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:49:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:40 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:49:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:40 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:49:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:41 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:49:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:41 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:49:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:42 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:49:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:42 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:49:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:42 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:49:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:42 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:49:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:42 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:49:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:42 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:49:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:42 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:49:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:42 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:49:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:42 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:49:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:42 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:49:42 | INFO     | app.service.report_generator:generate_report_stream:107 | 搜索任务 3 完成，找到 9 个相关文档
2025-07-22 11:49:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:50 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:49:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:50 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:49:51 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:51 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:49:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:52 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:49:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:52 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:49:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:53 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:49:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:53 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:49:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:53 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:49:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:53 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:49:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:49:53 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:49:53 | INFO     | app.service.report_generator:generate_report_stream:107 | 搜索任务 4 完成，找到 7 个相关文档
2025-07-22 11:50:00 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:00 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:50:00 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:00 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:50:01 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:01 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:50:01 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:01 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:50:01 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:01 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:50:02 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:02 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:50:02 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:02 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:50:02 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:02 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:50:02 | INFO     | app.service.report_generator:generate_report_stream:107 | 搜索任务 5 完成，找到 8 个相关文档
2025-07-22 11:50:09 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:09 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:50:10 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:10 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:50:10 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:10 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:50:10 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:10 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:50:11 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:11 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:50:11 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:11 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:50:11 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:11 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:50:11 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:11 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:50:11 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:11 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:50:11 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:11 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:50:11 | INFO     | app.service.report_generator:generate_report_stream:107 | 搜索任务 6 完成，找到 10 个相关文档
2025-07-22 11:50:18 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:18 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:50:18 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:18 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:50:19 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:19 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:50:19 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:19 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:50:19 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:19 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:50:19 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:19 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:50:19 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:19 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:50:20 | INFO     | app.service.report_generator:generate_report_stream:107 | 搜索任务 7 完成，找到 6 个相关文档
2025-07-22 11:50:27 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:27 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:50:27 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:27 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:50:27 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:28 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:50:30 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:30 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:50:32 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:32 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:50:33 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:33 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:50:34 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:34 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:50:35 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:35 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:50:35 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:35 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:50:35 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:35 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:50:35 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:35 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:50:35 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:35 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:50:35 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:35 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:50:35 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:35 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:50:35 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:35 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:50:35 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:35 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:50:35 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:35 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:50:35 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:35 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:50:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:37 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:50:37 | INFO     | app.service.report_generator:generate_report_stream:107 | 搜索任务 8 完成，找到 9 个相关文档
2025-07-22 11:50:48 | INFO     | app.service.research_planner:analyze_content_completeness:150 | 内容分析完成，覆盖度评分: 0.6
2025-07-22 11:50:57 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:57 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:50:57 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:58 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:50:58 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:58 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:50:58 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:58 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:50:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:59 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:50:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:59 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:50:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:59 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:50:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:59 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:50:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:59 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:50:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:59 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:50:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:59 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:50:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:59 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:50:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:59 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:50:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:50:59 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:51:00 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:51:00 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:51:00 | INFO     | app.service.report_generator:generate_report_stream:147 | 补充搜索找到 5 个额外文档
2025-07-22 11:51:07 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:51:07 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:51:08 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:51:08 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:51:10 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:51:10 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:51:10 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:51:10 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:51:12 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:51:12 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:51:13 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:51:13 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:51:13 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:51:13 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:51:13 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:51:13 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:51:13 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:51:13 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:51:13 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:51:13 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:51:13 | INFO     | app.service.report_generator:generate_report_stream:147 | 补充搜索找到 3 个额外文档
2025-07-22 11:51:21 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:51:21 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:51:21 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:51:21 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:51:21 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:51:21 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:51:22 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:51:22 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:51:22 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:51:22 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:51:23 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:51:23 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:51:23 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:51:23 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:51:24 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:51:24 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:51:24 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:51:24 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:51:24 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:51:24 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:51:24 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:51:24 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:51:24 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:51:24 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:51:24 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:51:24 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:51:24 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:51:24 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:51:24 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:51:24 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:51:24 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:51:24 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:51:25 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:51:25 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:51:25 | INFO     | app.service.report_generator:generate_report_stream:147 | 补充搜索找到 5 个额外文档
2025-07-22 11:51:27 | ERROR    | app.service.report_generator:generate_report_stream:187 | 报告生成失败: 'full_content'
2025-07-22 11:53:26 | INFO     | __main__:lifespan:39 | RAG系统关闭中...
2025-07-22 11:53:26 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_1 被取消
2025-07-22 11:53:26 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_0 被取消
2025-07-22 11:53:26 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_2 被取消
2025-07-22 11:53:37 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 11:53:42 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 11:53:42 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 11:53:42 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 11:53:42 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 11:53:42 | INFO     | __main__:lifespan:24 | RAG系统启动中...
2025-07-22 11:53:42 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 11:53:42 | INFO     | __main__:lifespan:28 | PostgreSQL数据库连接成功
2025-07-22 11:53:42 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 11:53:43 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 11:53:43 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 11:53:43 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 11:53:44 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 11:53:44 | INFO     | app.service.document.async_parse_queue:start_workers:71 | 启动了 3 个工作进程
2025-07-22 11:53:44 | INFO     | app.service.document.parse_document:initialize_document_queue:225 | 文档处理队列已启动
2025-07-22 11:53:48 | INFO     | app.service.report_generator:generate_report_stream:42 | 开始生成报告: 针对中国政府在三江源地区的环境保护工作，相关机构各人分别提出了哪些观点，请分类概括
2025-07-22 11:54:27 | INFO     | app.service.research_planner:generate_research_plan:88 | 成功生成研究计划，包含8个搜索任务
2025-07-22 11:54:35 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:54:35 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:54:35 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:54:35 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:54:36 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:54:36 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:54:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:54:38 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:54:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:54:38 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:54:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:54:40 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:54:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:54:40 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:54:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:54:40 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:54:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:54:40 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:54:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:54:40 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:54:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:54:40 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:54:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:54:40 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:54:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:54:40 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:54:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:54:40 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:54:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:54:42 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:54:42 | INFO     | app.service.report_generator:generate_report_stream:104 | 搜索任务 1 完成，找到 9 个相关文档
2025-07-22 11:54:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:54:50 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:54:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:54:50 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:54:51 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:54:51 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:54:51 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:54:51 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:54:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:54:53 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:54:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:54:53 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:54:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:54:54 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:54:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:54:54 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:54:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:54:54 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:54:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:54:54 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:54:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:54:54 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:54:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:54:54 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:54:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:54:54 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:54:55 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:54:55 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:54:55 | INFO     | app.service.report_generator:generate_report_stream:104 | 搜索任务 2 完成，找到 7 个相关文档
2025-07-22 11:55:03 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:03 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:55:04 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:04 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:55:04 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:04 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:55:04 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:04 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:55:05 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:05 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:55:05 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:05 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:55:05 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:05 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:55:05 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:06 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:55:06 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:06 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:55:06 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:06 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:55:06 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:06 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:55:06 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:06 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:55:06 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:06 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:55:06 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:06 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:55:06 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:06 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:55:06 | INFO     | app.service.report_generator:generate_report_stream:104 | 搜索任务 3 完成，找到 8 个相关文档
2025-07-22 11:55:14 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:14 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:55:14 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:14 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:55:15 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:15 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:55:17 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:17 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:55:17 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:17 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:55:17 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:18 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:55:18 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:18 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:55:18 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:18 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:55:18 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:18 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:55:18 | INFO     | app.service.report_generator:generate_report_stream:104 | 搜索任务 4 完成，找到 8 个相关文档
2025-07-22 11:55:25 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:26 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:55:26 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:26 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:55:26 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:26 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:55:27 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:27 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:55:27 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:27 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:55:27 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:27 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:55:27 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:27 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:55:27 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:27 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:55:27 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:27 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:55:27 | INFO     | app.service.report_generator:generate_report_stream:104 | 搜索任务 5 完成，找到 6 个相关文档
2025-07-22 11:55:36 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:36 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:55:36 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:36 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:55:36 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:36 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:55:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:37 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:55:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:37 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:55:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:37 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:55:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:38 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:55:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:39 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:55:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:39 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:55:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:39 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:55:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:39 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:55:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:39 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:55:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:39 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:55:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:39 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:55:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:39 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:55:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:39 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:55:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:39 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:55:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:41 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:55:41 | INFO     | app.service.report_generator:generate_report_stream:104 | 搜索任务 6 完成，找到 8 个相关文档
2025-07-22 11:55:48 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:48 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:55:48 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:48 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:55:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:49 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:55:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:49 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:55:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:49 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:55:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:50 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:55:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:52 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:55:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:54 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:55:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:54 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:55:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:54 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:55:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:54 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:55:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:54 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:55:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:54 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:55:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:54 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:55:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:55:54 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:55:54 | INFO     | app.service.report_generator:generate_report_stream:104 | 搜索任务 7 完成，找到 8 个相关文档
2025-07-22 11:56:02 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:02 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:56:03 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:03 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:56:03 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:03 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:56:04 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:04 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 11:56:04 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:04 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:56:04 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:04 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:56:04 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:04 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:56:04 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:04 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:56:04 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:04 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 11:56:04 | INFO     | app.service.report_generator:generate_report_stream:104 | 搜索任务 8 完成，找到 8 个相关文档
2025-07-22 11:56:17 | INFO     | app.service.research_planner:analyze_content_completeness:150 | 内容分析完成，覆盖度评分: 0.6
2025-07-22 11:56:25 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:25 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:56:27 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:27 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:56:28 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:28 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:56:29 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:29 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:56:30 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:30 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:56:30 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:30 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:56:30 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:30 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:56:30 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:31 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:56:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:31 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:56:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:31 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:56:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:31 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:56:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:31 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:56:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:31 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:56:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:31 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:56:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:31 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:56:31 | INFO     | app.service.report_generator:generate_report_stream:144 | 补充搜索找到 5 个额外文档
2025-07-22 11:56:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:38 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:56:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:38 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:56:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:40 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:56:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:41 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:56:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:41 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:56:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:43 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:56:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:44 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:56:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:44 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:56:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:44 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:56:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:44 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:56:44 | INFO     | app.service.report_generator:generate_report_stream:144 | 补充搜索找到 3 个额外文档
2025-07-22 11:56:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:52 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:56:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:53 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:56:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:53 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:56:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:54 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:56:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:54 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:56:55 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:55 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 11:56:57 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:57 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:56:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:59 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:56:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:59 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:56:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:59 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:56:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:59 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:56:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:59 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:56:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:59 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:56:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:59 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:56:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:59 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:56:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 11:56:59 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 11:56:59 | INFO     | app.service.report_generator:generate_report_stream:144 | 补充搜索找到 5 个额外文档
2025-07-22 11:58:37 | INFO     | app.service.report_generator:generate_report_stream:181 | 报告生成完成: 针对中国政府在三江源地区的环境保护工作，相关机构各人分别提出了哪些观点，请分类概括
2025-07-22 13:54:26 | INFO     | __main__:lifespan:39 | RAG系统关闭中...
2025-07-22 13:54:26 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_1 被取消
2025-07-22 13:54:26 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_0 被取消
2025-07-22 13:54:26 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_2 被取消
2025-07-22 13:54:38 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 13:54:42 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 13:54:42 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 13:54:42 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 13:54:42 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 13:54:42 | INFO     | __main__:lifespan:24 | RAG系统启动中...
2025-07-22 13:54:42 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 13:54:42 | INFO     | __main__:lifespan:28 | PostgreSQL数据库连接成功
2025-07-22 13:54:42 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 13:54:44 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 13:54:44 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 13:54:44 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 13:54:45 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 13:54:45 | INFO     | app.service.document.async_parse_queue:start_workers:71 | 启动了 3 个工作进程
2025-07-22 13:54:45 | INFO     | app.service.document.parse_document:initialize_document_queue:225 | 文档处理队列已启动
2025-07-22 13:54:50 | INFO     | app.service.report_generator:generate_report_stream:42 | 开始生成报告: 针对中国政府在三江源地区的环境保护工作，相关机构各人分别提出了哪些观点，请分类概括
2025-07-22 13:55:16 | INFO     | app.service.research_planner:generate_research_plan:88 | 成功生成研究计划，包含5个搜索任务
2025-07-22 13:55:26 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:55:26 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 13:55:26 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:55:26 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 13:55:26 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:55:26 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 13:55:27 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:55:27 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 13:55:27 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:55:27 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 13:55:28 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:55:28 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 13:55:29 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:55:29 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:55:29 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:55:29 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:55:29 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:55:29 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:55:29 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:55:29 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:55:29 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:55:29 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:55:29 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:55:30 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:55:30 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:55:30 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:55:30 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:55:30 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:55:30 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:55:30 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:55:30 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:55:30 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:55:30 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:55:30 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:55:30 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:55:30 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:55:30 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:55:30 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:55:30 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:55:30 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:55:30 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:55:30 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:55:30 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:55:30 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:55:30 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:55:30 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 13:55:30 | INFO     | app.service.report_generator:generate_report_stream:104 | 搜索任务 1 完成，找到 10 个相关文档
2025-07-22 13:56:09 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:09 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 13:56:10 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:10 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 13:56:10 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:10 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 13:56:11 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:11 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 13:56:11 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:11 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:56:11 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:11 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:56:11 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:11 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:56:11 | INFO     | app.service.report_generator:generate_report_stream:104 | 搜索任务 2 完成，找到 7 个相关文档
2025-07-22 13:56:19 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:19 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 13:56:19 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:19 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 13:56:20 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:20 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 13:56:20 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:20 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 13:56:20 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:20 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 13:56:20 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:20 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 13:56:21 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:21 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:56:21 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:21 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:56:21 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:21 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:56:21 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:21 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:56:21 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:21 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:56:21 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:21 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:56:21 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:21 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:56:21 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:21 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:56:21 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:21 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:56:21 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:21 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 13:56:21 | INFO     | app.service.report_generator:generate_report_stream:104 | 搜索任务 3 完成，找到 9 个相关文档
2025-07-22 13:56:28 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:28 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 13:56:28 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:28 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 13:56:28 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:29 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 13:56:29 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:29 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 13:56:29 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:29 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 13:56:30 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:30 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 13:56:30 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:30 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:56:30 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:30 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:56:30 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:31 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:56:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:31 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:56:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:31 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:56:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:31 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:56:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:31 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:56:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:31 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:56:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:31 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:56:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:31 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 13:56:31 | INFO     | app.service.report_generator:generate_report_stream:104 | 搜索任务 4 完成，找到 10 个相关文档
2025-07-22 13:56:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:38 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 13:56:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:38 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 13:56:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:39 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 13:56:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:41 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 13:56:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:41 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 13:56:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:42 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 13:56:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:42 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:56:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:42 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:56:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:42 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:56:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:42 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:56:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:42 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:56:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:43 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:56:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:43 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:56:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:43 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:56:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:43 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:56:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:43 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 13:56:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:43 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 13:56:43 | INFO     | app.service.report_generator:generate_report_stream:104 | 搜索任务 5 完成，找到 8 个相关文档
2025-07-22 13:56:53 | INFO     | app.service.research_planner:analyze_content_completeness:150 | 内容分析完成，覆盖度评分: 0.6
2025-07-22 13:56:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:56:59 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 13:57:00 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:57:00 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 13:57:00 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:57:00 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 13:57:00 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:57:00 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 13:57:01 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:57:01 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 13:57:02 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:57:02 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 13:57:04 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:57:04 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 13:57:04 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:57:04 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 13:57:04 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:57:04 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 13:57:04 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:57:04 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 13:57:04 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:57:04 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 13:57:04 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:57:04 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 13:57:04 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:57:04 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 13:57:04 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:57:04 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 13:57:04 | INFO     | app.service.report_generator:generate_report_stream:144 | 补充搜索找到 5 个额外文档
2025-07-22 13:57:11 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:57:11 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 13:57:11 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:57:11 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 13:57:12 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:57:12 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 13:57:12 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:57:12 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 13:57:12 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:57:12 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 13:57:13 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:57:13 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 13:57:13 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:57:13 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 13:57:13 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:57:13 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 13:57:13 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:57:13 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 13:57:13 | INFO     | app.service.report_generator:generate_report_stream:144 | 补充搜索找到 2 个额外文档
2025-07-22 13:57:20 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:57:20 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 13:57:21 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:57:21 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 13:57:21 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:57:21 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 13:57:21 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:57:21 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 13:57:21 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:57:21 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 13:57:23 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:57:23 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 13:57:24 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:57:24 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 13:57:24 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:57:24 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 13:57:24 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:57:24 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 13:57:24 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:57:24 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 13:57:24 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:57:24 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 13:57:24 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:57:24 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 13:57:24 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:57:24 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 13:57:24 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:57:24 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 13:57:25 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 13:57:25 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 13:57:25 | INFO     | app.service.report_generator:generate_report_stream:144 | 补充搜索找到 5 个额外文档
2025-07-22 13:58:46 | INFO     | app.service.report_generator:generate_report_stream:181 | 报告生成完成: 针对中国政府在三江源地区的环境保护工作，相关机构各人分别提出了哪些观点，请分类概括
2025-07-22 13:59:34 | INFO     | __main__:lifespan:39 | RAG系统关闭中...
2025-07-22 13:59:34 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_1 被取消
2025-07-22 13:59:34 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_0 被取消
2025-07-22 13:59:34 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_2 被取消
2025-07-22 13:59:45 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 13:59:49 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 13:59:49 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 13:59:49 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 13:59:49 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 13:59:49 | INFO     | __main__:lifespan:24 | RAG系统启动中...
2025-07-22 13:59:49 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 13:59:49 | INFO     | __main__:lifespan:28 | PostgreSQL数据库连接成功
2025-07-22 13:59:49 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 13:59:51 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 13:59:51 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 13:59:51 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 13:59:51 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 13:59:51 | INFO     | app.service.document.async_parse_queue:start_workers:71 | 启动了 3 个工作进程
2025-07-22 13:59:51 | INFO     | app.service.document.parse_document:initialize_document_queue:225 | 文档处理队列已启动
2025-07-22 14:01:49 | INFO     | app.service.report_generator:generate_report_stream:42 | 开始生成报告: 针对中国政府在三江源地区的环境保护工作，相关机构各人分别提出了哪些观点，请分类概括
2025-07-22 14:05:47 | INFO     | __main__:lifespan:39 | RAG系统关闭中...
2025-07-22 14:05:47 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_2 被取消
2025-07-22 14:05:47 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_0 被取消
2025-07-22 14:05:47 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_1 被取消
2025-07-22 14:05:59 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 14:06:03 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 14:06:03 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 14:06:03 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 14:06:03 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 14:06:03 | INFO     | __main__:lifespan:24 | RAG系统启动中...
2025-07-22 14:06:03 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 14:06:03 | INFO     | __main__:lifespan:28 | PostgreSQL数据库连接成功
2025-07-22 14:06:03 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 14:06:04 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 14:06:04 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 14:06:04 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 14:06:05 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 14:06:05 | INFO     | app.service.document.async_parse_queue:start_workers:71 | 启动了 3 个工作进程
2025-07-22 14:06:05 | INFO     | app.service.document.parse_document:initialize_document_queue:225 | 文档处理队列已启动
2025-07-22 14:06:09 | INFO     | app.service.report_generator:generate_report_stream:42 | 开始生成报告: 针对中国政府在三江源地区的环境保护工作，相关机构各人分别提出了哪些观点，请分类概括
2025-07-22 14:07:13 | INFO     | app.api.knowledge:create_knowledge:44 | 成功创建知识库: 乌兹
2025-07-22 14:09:20 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: Biography of the President of the Republic of Uzbekistan.pdf
2025-07-22 14:09:20 | INFO     | app.service.document.document_upload:extract_document_metadata:56 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmpeb8ue6gz.pdf
2025-07-22 14:09:20 | WARNING  | app.service.document.document_upload:extract_document_metadata:83 | 不支持的文件类型: .pdf
2025-07-22 14:09:20 | INFO     | app.service.document.document_upload:extract_document_metadata:89 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmpeb8ue6gz.pdf
2025-07-22 14:09:20 | INFO     | app.service.document.document_upload:upload_document_service:183 | 成功提取元数据: Biography of the President of the Republic of Uzbekistan.pdf
2025-07-22 14:09:20 | INFO     | app.database.minio_manager:upload_file:49 | 存储桶 99e948c642c4fed4005fb114438e4965 不存在，已自动创建
2025-07-22 14:09:20 | INFO     | app.database.minio_manager:upload_file:53 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmpeb8ue6gz.pdf -> 99e948c642c4fed4005fb114438e4965/d3ffacb717d9296e625fde86b5178741.pdf
2025-07-22 14:09:20 | INFO     | app.service.document.async_parse_queue:add_task:61 | 任务已加入队列: 912903af-5b51-4d0f-b9c1-a44fedcd2c69 (file_hash: d3ffacb717d9296e625fde86b5178741)
2025-07-22 14:09:20 | INFO     | app.service.document.document_upload:upload_document_service:206 | 成功提交任务: 912903af-5b51-4d0f-b9c1-a44fedcd2c69
2025-07-22 14:09:21 | INFO     | app.service.document.async_parse_queue:_process_task:126 | 工作进程 worker_2 开始处理任务: 912903af-5b51-4d0f-b9c1-a44fedcd2c69
2025-07-22 14:09:21 | INFO     | app.service.document.document_processor:_parse_document:213 | 开始解析文件: d3ffacb717d9296e625fde86b5178741.pdf
2025-07-22 14:10:19 | INFO     | app.service.document.document_processor:process_document:122 | 开始分块: d3ffacb717d9296e625fde86b5178741.pdf
2025-07-22 14:10:19 | INFO     | app.service.document.document_processor:process_document:126 | 共有2 片
2025-07-22 14:10:25 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:10:25 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:10:25 | INFO     | app.service.document.document_processor:process_document:131 | 向量化开始
2025-07-22 14:10:26 | INFO     | app.database.qdrant_manager:_create_collection_if_not_exists:63 | 创建Qdrant集合: 99e948c642c4fed4005fb114438e4965
2025-07-22 14:10:26 | INFO     | app.database.qdrant_manager:store_vectors:85 | 开始插入 2 个向量，批次大小: 100
2025-07-22 14:10:26 | INFO     | app.database.qdrant_manager:store_vectors:94 | 成功插入批次，大小: 2
2025-07-22 14:10:26 | INFO     | app.database.qdrant_manager:store_vectors:102 | 向量插入完成，总耗时: 0.22s
2025-07-22 14:10:26 | INFO     | app.service.document.document_processor:process_document:135 | 摘要生成开始
2025-07-22 14:11:57 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 874
2025-07-22 14:11:57 | INFO     | app.service.document.document_processor:_finalize_processing:243 | 摘要生成完成: d3ffacb717d9296e625fde86b5178741.pdf
2025-07-22 14:11:57 | INFO     | app.service.document.document_processor:process_document:141 | 文档处理完成: d3ffacb717d9296e625fde86b5178741.pdf
2025-07-22 14:11:57 | INFO     | app.service.document.async_parse_queue:_complete_task:163 | 任务处理成功: 912903af-5b51-4d0f-b9c1-a44fedcd2c69
2025-07-22 14:12:53 | INFO     | app.service.research_planner:generate_research_plan:88 | 成功生成研究计划，包含7个搜索任务
2025-07-22 14:12:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:12:59 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 14:12:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:12:59 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 14:12:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:12:59 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 14:12:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:00 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 14:13:00 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:00 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 14:13:00 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:00 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 14:13:00 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:00 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 14:13:01 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:01 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 14:13:01 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:01 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 14:13:01 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:01 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 14:13:01 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:01 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 14:13:01 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:01 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 14:13:01 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:01 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 14:13:01 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:01 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 14:13:01 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:01 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 14:13:01 | INFO     | app.service.report_generator:generate_report_stream:104 | 搜索任务 1 完成，找到 10 个相关文档
2025-07-22 14:13:09 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:09 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 14:13:09 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:09 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 14:13:10 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:10 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 14:13:10 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:10 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 14:13:11 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:11 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 14:13:11 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:11 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 14:13:11 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:11 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 14:13:11 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:11 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 14:13:11 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:11 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 14:13:11 | INFO     | app.service.report_generator:generate_report_stream:104 | 搜索任务 2 完成，找到 6 个相关文档
2025-07-22 14:13:16 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:16 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 14:13:17 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:17 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 14:13:17 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:17 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 14:13:19 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:19 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 14:13:20 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:20 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 14:13:21 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:21 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 14:13:21 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:21 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 14:13:21 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:21 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 14:13:21 | INFO     | app.service.report_generator:generate_report_stream:104 | 搜索任务 3 完成，找到 7 个相关文档
2025-07-22 14:13:27 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:27 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 14:13:27 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:27 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 14:13:27 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:27 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 14:13:28 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:28 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 14:13:28 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:28 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 14:13:28 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:28 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 14:13:29 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:29 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 14:13:29 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:29 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 14:13:29 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:29 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 14:13:29 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:29 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 14:13:29 | INFO     | app.service.report_generator:generate_report_stream:104 | 搜索任务 4 完成，找到 7 个相关文档
2025-07-22 14:13:36 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:36 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 14:13:36 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:36 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 14:13:36 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:36 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 14:13:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:37 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 14:13:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:38 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 14:13:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:38 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 14:13:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:38 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 14:13:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:38 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 14:13:38 | INFO     | app.service.report_generator:generate_report_stream:104 | 搜索任务 5 完成，找到 6 个相关文档
2025-07-22 14:13:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:44 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 14:13:46 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:46 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 14:13:46 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:46 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 14:13:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:47 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 14:13:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:47 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 14:13:48 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:48 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 14:13:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:49 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 14:13:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:50 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 14:13:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:50 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 14:13:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:50 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 14:13:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:50 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 14:13:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:50 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 14:13:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:50 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 14:13:51 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:51 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 14:13:51 | INFO     | app.service.report_generator:generate_report_stream:104 | 搜索任务 6 完成，找到 9 个相关文档
2025-07-22 14:13:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:59 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 14:13:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:13:59 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 14:14:00 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:14:00 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 14:14:00 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:14:00 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 14:14:02 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:14:02 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 14:14:02 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:14:02 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 14:14:02 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:14:02 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 14:14:02 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:14:02 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 14:14:02 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:14:02 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 14:14:02 | INFO     | app.service.report_generator:generate_report_stream:104 | 搜索任务 7 完成，找到 7 个相关文档
2025-07-22 14:14:17 | INFO     | app.service.research_planner:analyze_content_completeness:150 | 内容分析完成，覆盖度评分: 0.6
2025-07-22 14:14:24 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:14:24 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 14:14:24 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:14:24 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 14:14:24 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:14:24 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 14:14:24 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:14:24 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 14:14:25 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:14:25 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 14:14:26 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:14:26 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 14:14:26 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:14:26 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 14:14:26 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:14:26 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 14:14:26 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:14:26 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 14:14:26 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:14:26 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 14:14:26 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:14:26 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 14:14:26 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:14:26 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 14:14:26 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:14:26 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 14:14:26 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:14:26 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 14:14:26 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:14:26 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 14:14:26 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:14:26 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 14:14:27 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:14:27 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 14:14:27 | INFO     | app.service.report_generator:generate_report_stream:144 | 补充搜索找到 5 个额外文档
2025-07-22 14:14:33 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:14:33 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 14:14:33 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:14:33 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 14:14:33 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:14:33 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 14:14:34 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:14:34 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 14:14:36 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:14:36 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 14:14:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:14:38 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 14:14:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:14:38 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 14:14:38 | INFO     | app.service.report_generator:generate_report_stream:144 | 补充搜索找到 2 个额外文档
2025-07-22 14:14:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:14:44 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 14:14:45 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:14:45 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 14:14:45 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:14:45 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 2 个结果
2025-07-22 14:14:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:14:47 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 14:14:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:14:49 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 14:14:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:14:49 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 14:14:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 14:14:49 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 1 个结果
2025-07-22 14:14:49 | INFO     | app.service.report_generator:generate_report_stream:144 | 补充搜索找到 4 个额外文档
2025-07-22 14:16:21 | INFO     | app.service.report_generator:generate_report_stream:181 | 报告生成完成: 针对中国政府在三江源地区的环境保护工作，相关机构各人分别提出了哪些观点，请分类概括
2025-07-22 14:22:52 | INFO     | __main__:lifespan:39 | RAG系统关闭中...
2025-07-22 14:22:52 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_0 被取消
2025-07-22 14:22:52 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_2 被取消
2025-07-22 14:22:52 | INFO     | app.service.document.async_parse_queue:_worker_loop:85 | 工作进程 worker_1 被取消
2025-07-22 14:37:35 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 14:37:39 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 14:37:39 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 14:37:39 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 14:37:39 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 14:37:39 | INFO     | __main__:lifespan:24 | RAG系统启动中...
2025-07-22 14:37:39 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 14:37:39 | INFO     | __main__:lifespan:28 | PostgreSQL数据库连接成功
2025-07-22 14:37:39 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 14:37:41 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 14:37:41 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 14:37:41 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 14:37:41 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 14:37:41 | INFO     | app.service.document.async_parse_queue:start_workers:69 | 启动了 3 个工作进程
2025-07-22 14:37:41 | INFO     | app.service.document.parse_document:initialize_document_queue:225 | 文档处理队列已启动
2025-07-22 14:37:41 | ERROR    | app.service.document.async_parse_queue:_worker_loop:86 | 工作进程 worker_0 异常: object list can't be used in 'await' expression
2025-07-22 14:37:41 | ERROR    | app.service.document.async_parse_queue:_worker_loop:86 | 工作进程 worker_1 异常: object list can't be used in 'await' expression
2025-07-22 14:37:41 | ERROR    | app.service.document.async_parse_queue:_worker_loop:86 | 工作进程 worker_2 异常: object list can't be used in 'await' expression
2025-07-22 14:37:51 | ERROR    | app.service.document.async_parse_queue:_worker_loop:86 | 工作进程 worker_0 异常: object list can't be used in 'await' expression
2025-07-22 14:37:51 | ERROR    | app.service.document.async_parse_queue:_worker_loop:86 | 工作进程 worker_1 异常: object list can't be used in 'await' expression
2025-07-22 14:37:51 | ERROR    | app.service.document.async_parse_queue:_worker_loop:86 | 工作进程 worker_2 异常: object list can't be used in 'await' expression
2025-07-22 14:38:01 | ERROR    | app.service.document.async_parse_queue:_worker_loop:86 | 工作进程 worker_0 异常: object list can't be used in 'await' expression
2025-07-22 14:38:01 | ERROR    | app.service.document.async_parse_queue:_worker_loop:86 | 工作进程 worker_1 异常: object list can't be used in 'await' expression
2025-07-22 14:38:01 | ERROR    | app.service.document.async_parse_queue:_worker_loop:86 | 工作进程 worker_2 异常: object list can't be used in 'await' expression
2025-07-22 14:38:11 | ERROR    | app.service.document.async_parse_queue:_worker_loop:86 | 工作进程 worker_0 异常: object list can't be used in 'await' expression
2025-07-22 14:38:11 | ERROR    | app.service.document.async_parse_queue:_worker_loop:86 | 工作进程 worker_1 异常: object list can't be used in 'await' expression
2025-07-22 14:38:11 | ERROR    | app.service.document.async_parse_queue:_worker_loop:86 | 工作进程 worker_2 异常: object list can't be used in 'await' expression
2025-07-22 14:38:21 | ERROR    | app.service.document.async_parse_queue:_worker_loop:86 | 工作进程 worker_1 异常: object list can't be used in 'await' expression
2025-07-22 14:38:21 | ERROR    | app.service.document.async_parse_queue:_worker_loop:86 | 工作进程 worker_0 异常: object list can't be used in 'await' expression
2025-07-22 14:38:21 | ERROR    | app.service.document.async_parse_queue:_worker_loop:86 | 工作进程 worker_2 异常: object list can't be used in 'await' expression
2025-07-22 14:38:31 | ERROR    | app.service.document.async_parse_queue:_worker_loop:86 | 工作进程 worker_0 异常: object list can't be used in 'await' expression
2025-07-22 14:38:31 | ERROR    | app.service.document.async_parse_queue:_worker_loop:86 | 工作进程 worker_2 异常: object list can't be used in 'await' expression
2025-07-22 14:38:31 | ERROR    | app.service.document.async_parse_queue:_worker_loop:86 | 工作进程 worker_1 异常: object list can't be used in 'await' expression
2025-07-22 14:38:41 | INFO     | __main__:lifespan:39 | RAG系统关闭中...
2025-07-22 14:38:53 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 14:38:57 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 14:38:57 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 14:38:57 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 14:38:57 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 14:38:57 | INFO     | __main__:lifespan:24 | RAG系统启动中...
2025-07-22 14:38:57 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 14:38:57 | INFO     | __main__:lifespan:28 | PostgreSQL数据库连接成功
2025-07-22 14:38:57 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 14:38:59 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 14:38:59 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 14:38:59 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 14:39:00 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 14:39:00 | INFO     | app.service.document.async_parse_queue:start_workers:69 | 启动了 3 个工作进程
2025-07-22 14:39:00 | INFO     | app.service.document.parse_document:initialize_document_queue:225 | 文档处理队列已启动
2025-07-22 14:39:00 | ERROR    | app.service.document.async_parse_queue:_worker_loop:86 | 工作进程 worker_0 异常: object list can't be used in 'await' expression
2025-07-22 14:39:00 | ERROR    | app.service.document.async_parse_queue:_worker_loop:86 | 工作进程 worker_1 异常: object list can't be used in 'await' expression
2025-07-22 14:39:00 | ERROR    | app.service.document.async_parse_queue:_worker_loop:86 | 工作进程 worker_2 异常: object list can't be used in 'await' expression
2025-07-22 14:39:01 | INFO     | __main__:lifespan:39 | RAG系统关闭中...
2025-07-22 14:51:10 | INFO     | app.service.document.async_parse_queue:__init__:187 | 队列监听线程初始化完成
2025-07-22 14:51:18 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 14:51:23 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 14:51:23 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 14:51:23 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 14:51:23 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 14:51:23 | INFO     | __main__:lifespan:27 | RAG系统启动中...
2025-07-22 14:51:23 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 14:51:23 | INFO     | __main__:lifespan:31 | PostgreSQL数据库连接成功
2025-07-22 14:51:23 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 14:51:24 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 14:51:24 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 14:51:24 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 14:51:24 | ERROR    | __main__:lifespan:43 | 系统初始化失败: asyncio.run() cannot be called from a running event loop
2025-07-22 14:52:10 | INFO     | app.service.document.async_parse_queue:__init__:187 | 队列监听线程初始化完成
2025-07-22 14:52:18 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 14:52:22 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 14:52:22 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 14:52:22 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 14:52:22 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 14:52:23 | INFO     | __main__:lifespan:27 | RAG系统启动中...
2025-07-22 14:52:23 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 14:52:23 | INFO     | __main__:lifespan:31 | PostgreSQL数据库连接成功
2025-07-22 14:52:23 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 14:52:26 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 14:52:26 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 14:52:26 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 14:52:26 | INFO     | app.service.document.async_parse_queue:run:196 | 队列监听线程启动，开始监听队列...
2025-07-22 14:52:26 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 14:52:26 | INFO     | __main__:lifespan:42 | 队列监听线程已启动
2025-07-22 14:53:15 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: central-asia-is-a-key-vector-of-uzbekistans-foreign-policy.pdf
2025-07-22 14:53:15 | INFO     | app.service.document.document_upload:extract_document_metadata:57 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmpt75cl4wx.pdf
2025-07-22 14:53:15 | WARNING  | app.service.document.document_upload:extract_document_metadata:84 | 不支持的文件类型: .pdf
2025-07-22 14:53:15 | INFO     | app.service.document.document_upload:extract_document_metadata:90 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmpt75cl4wx.pdf
2025-07-22 14:53:15 | INFO     | app.service.document.document_upload:upload_document_service:184 | 成功提取元数据: central-asia-is-a-key-vector-of-uzbekistans-foreign-policy.pdf
2025-07-22 14:53:15 | INFO     | app.database.minio_manager:upload_file:53 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmpt75cl4wx.pdf -> 99e948c642c4fed4005fb114438e4965/17224950eff21079a7dc90f5478c0338.pdf
2025-07-22 14:53:15 | ERROR    | app.service.document.async_parse_queue:run:217 | 任务提交失败: 'str' object has no attribute 'task_type'
2025-07-22 14:55:09 | INFO     | __main__:lifespan:47 | RAG系统关闭中...
2025-07-22 14:55:09 | INFO     | app.service.document.async_parse_queue:stop:250 | 停止监听线程请求已发送
2025-07-22 14:55:10 | INFO     | app.service.document.async_parse_queue:run:221 | 队列监听线程已停止
2025-07-22 14:55:10 | INFO     | __main__:lifespan:68 | 所有任务处理完成，应用程序退出
2025-07-22 14:55:13 | INFO     | app.service.document.async_parse_queue:__init__:187 | 队列监听线程初始化完成
2025-07-22 14:55:22 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 14:55:27 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 14:55:27 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 14:55:27 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 14:55:27 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 14:55:27 | INFO     | __main__:lifespan:27 | RAG系统启动中...
2025-07-22 14:55:27 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 14:55:27 | INFO     | __main__:lifespan:31 | PostgreSQL数据库连接成功
2025-07-22 14:55:27 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 14:55:29 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 14:55:29 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 14:55:29 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 14:55:29 | INFO     | app.service.document.async_parse_queue:run:196 | 队列监听线程启动，开始监听队列...
2025-07-22 14:55:29 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 14:55:29 | INFO     | __main__:lifespan:42 | 队列监听线程已启动
2025-07-22 14:55:33 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: PF-37 21.02.2024.docx
2025-07-22 14:55:33 | INFO     | app.service.document.document_upload:extract_document_metadata:57 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmpfeaeskvl.docx
2025-07-22 14:55:33 | ERROR    | app.service.document.document_upload:_extract_word_metadata:113 | Word文档元数据提取失败: 'CoreProperties' object has no attribute 'company'
2025-07-22 14:55:33 | INFO     | app.service.document.document_upload:extract_document_metadata:90 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmpfeaeskvl.docx
2025-07-22 14:55:33 | INFO     | app.service.document.document_upload:upload_document_service:184 | 成功提取元数据: PF-37 21.02.2024.docx
2025-07-22 14:55:33 | INFO     | app.database.minio_manager:upload_file:53 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmpfeaeskvl.docx -> 99e948c642c4fed4005fb114438e4965/d673f1bec5d7572105ca8dc80da42e26.docx
2025-07-22 14:55:33 | INFO     | app.service.document.async_parse_queue:run:203 | 获取到任务: d673f1bec5d7572105ca8dc80da42e26
2025-07-22 14:55:33 | INFO     | app.service.document.async_parse_queue:handle_task_result:229 | 任务处理成功: <coroutine object DocumentProcessor.process_document at 0x000001A0D916D440>
2025-07-22 15:02:30 | INFO     | __main__:lifespan:47 | RAG系统关闭中...
2025-07-22 15:02:30 | INFO     | app.service.document.async_parse_queue:stop:250 | 停止监听线程请求已发送
2025-07-22 15:02:30 | INFO     | app.service.document.async_parse_queue:run:221 | 队列监听线程已停止
2025-07-22 15:02:41 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 15:02:45 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 15:02:45 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 15:02:45 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 15:02:45 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 15:02:46 | INFO     | __main__:lifespan:24 | RAG系统启动中...
2025-07-22 15:02:46 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 15:02:46 | INFO     | __main__:lifespan:28 | PostgreSQL数据库连接成功
2025-07-22 15:02:46 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 15:02:47 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 15:02:47 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 15:02:47 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 15:02:48 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 15:02:48 | INFO     | app.service.document.async_parse_queue:start_workers:228 | 启动了线程池，最大并发数: 3
2025-07-22 15:02:48 | INFO     | app.service.document.parse_document:initialize_document_queue:225 | 文档处理队列已启动
2025-07-22 15:02:56 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: PF-37 21.02.2024.docx
2025-07-22 15:03:05 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: the-multidimensional-foreign-policy-of-new-uzbekistan.pdf
2025-07-22 15:03:05 | INFO     | app.service.document.document_upload:extract_document_metadata:56 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmp_cdngltd.pdf
2025-07-22 15:03:05 | WARNING  | app.service.document.document_upload:extract_document_metadata:83 | 不支持的文件类型: .pdf
2025-07-22 15:03:05 | INFO     | app.service.document.document_upload:extract_document_metadata:89 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmp_cdngltd.pdf
2025-07-22 15:03:05 | INFO     | app.service.document.document_upload:upload_document_service:183 | 成功提取元数据: the-multidimensional-foreign-policy-of-new-uzbekistan.pdf
2025-07-22 15:03:05 | INFO     | app.database.minio_manager:upload_file:53 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmp_cdngltd.pdf -> 99e948c642c4fed4005fb114438e4965/5ee5b529f7790fd92dbf99b6c59659cc.pdf
2025-07-22 15:03:05 | INFO     | app.service.document.async_parse_queue:add_task:220 | 任务已加入队列: 013efce4-11d2-43ab-83f3-3db101069afb (file_hash: 5ee5b529f7790fd92dbf99b6c59659cc)
2025-07-22 15:03:05 | INFO     | app.service.document.document_upload:upload_document_service:206 | 成功提交任务: 013efce4-11d2-43ab-83f3-3db101069afb
2025-07-22 15:03:06 | INFO     | app.service.document.async_parse_queue:_process_task_sync:304 | 线程池开始处理任务: 013efce4-11d2-43ab-83f3-3db101069afb
2025-07-22 15:03:06 | ERROR    | app.service.document.document_processor:process_document:145 | 文档处理失败 5ee5b529f7790fd92dbf99b6c59659cc: cannot perform operation: another operation is in progress
2025-07-22 15:03:06 | ERROR    | app.service.document.async_parse_queue:_process_task_sync:321 | 线程池任务执行异常 013efce4-11d2-43ab-83f3-3db101069afb: Task <Task pending name='Task-19' coro=<DocumentProcessor.process_document() running at D:\项目\星图\知识库\app\service\document\document_processor.py:146> cb=[_run_until_complete_cb() at D:\miniconda3\envs\zhishiku\Lib\asyncio\base_events.py:181]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at D:\miniconda3\envs\zhishiku\Lib\asyncio\futures.py:389]> attached to a different loop
2025-07-22 15:03:06 | ERROR    | app.service.document.async_parse_queue:_complete_task:371 | 任务处理失败: 013efce4-11d2-43ab-83f3-3db101069afb, 错误: None
2025-07-22 15:04:36 | INFO     | __main__:lifespan:39 | RAG系统关闭中...
2025-07-22 15:04:36 | INFO     | app.service.document.async_parse_queue:_task_scheduler:260 | 任务调度器被取消
2025-07-22 15:04:47 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 15:04:51 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 15:04:51 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 15:04:51 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 15:04:51 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 15:04:52 | INFO     | __main__:lifespan:24 | RAG系统启动中...
2025-07-22 15:04:52 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 15:04:52 | INFO     | __main__:lifespan:28 | PostgreSQL数据库连接成功
2025-07-22 15:04:52 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 15:04:53 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 15:04:53 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 15:04:53 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 15:04:53 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 15:04:53 | INFO     | app.service.document.async_parse_queue:start_workers:228 | 启动了线程池，最大并发数: 3
2025-07-22 15:04:53 | INFO     | app.service.document.parse_document:initialize_document_queue:225 | 文档处理队列已启动
2025-07-22 15:05:01 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: uzbekistan-i-kitaj-strategiceskoe-partnerstvo-v-prodvizenii-zelenogo-razvitia.pdf
2025-07-22 15:05:01 | INFO     | app.service.document.document_upload:extract_document_metadata:56 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmp84e22j7o.pdf
2025-07-22 15:05:01 | WARNING  | app.service.document.document_upload:extract_document_metadata:83 | 不支持的文件类型: .pdf
2025-07-22 15:05:01 | INFO     | app.service.document.document_upload:extract_document_metadata:89 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmp84e22j7o.pdf
2025-07-22 15:05:01 | INFO     | app.service.document.document_upload:upload_document_service:183 | 成功提取元数据: uzbekistan-i-kitaj-strategiceskoe-partnerstvo-v-prodvizenii-zelenogo-razvitia.pdf
2025-07-22 15:05:01 | INFO     | app.database.minio_manager:upload_file:53 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmp84e22j7o.pdf -> 99e948c642c4fed4005fb114438e4965/279e472ff8f1e2672398020a32630604.pdf
2025-07-22 15:05:01 | INFO     | app.service.document.async_parse_queue:add_task:220 | 任务已加入队列: 07025b9d-d6be-4558-b7b4-f3f17acfb760 (file_hash: 279e472ff8f1e2672398020a32630604)
2025-07-22 15:05:01 | INFO     | app.service.document.document_upload:upload_document_service:206 | 成功提交任务: 07025b9d-d6be-4558-b7b4-f3f17acfb760
2025-07-22 15:05:02 | INFO     | app.service.document.async_parse_queue:_process_task_sync:304 | 线程池开始处理任务: 07025b9d-d6be-4558-b7b4-f3f17acfb760
2025-07-22 15:05:03 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 15:05:03 | ERROR    | app.service.document.document_processor:process_document:145 | 文档处理失败 279e472ff8f1e2672398020a32630604: cannot perform operation: another operation is in progress
2025-07-22 15:05:03 | ERROR    | app.service.document.async_parse_queue:_run_task_in_thread:334 | 线程任务执行失败: Task <Task pending name='Task-15' coro=<AsyncDocumentQueueWork._run_task_in_thread() running at D:\项目\星图\知识库\app\service\document\async_parse_queue.py:332> cb=[_run_until_complete_cb() at D:\miniconda3\envs\zhishiku\Lib\asyncio\base_events.py:181]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at D:\miniconda3\envs\zhishiku\Lib\asyncio\futures.py:389]> attached to a different loop
2025-07-22 15:05:03 | INFO     | app.service.document.async_parse_queue:_process_task_sync:317 | 线程池任务处理完成: 07025b9d-d6be-4558-b7b4-f3f17acfb760, 结果: False
2025-07-22 15:05:03 | ERROR    | app.service.document.async_parse_queue:_complete_task:384 | 任务处理失败: 07025b9d-d6be-4558-b7b4-f3f17acfb760, 错误: None
2025-07-22 15:10:11 | INFO     | __main__:lifespan:39 | RAG系统关闭中...
2025-07-22 15:10:11 | INFO     | app.service.document.async_parse_queue:_task_scheduler:260 | 任务调度器被取消
2025-07-22 15:10:23 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 15:10:27 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 15:10:27 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 15:10:27 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 15:10:27 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 15:10:28 | INFO     | __main__:lifespan:24 | RAG系统启动中...
2025-07-22 15:10:28 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 15:10:28 | INFO     | __main__:lifespan:28 | PostgreSQL数据库连接成功
2025-07-22 15:10:28 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 15:10:30 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 15:10:30 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 15:10:30 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 15:10:30 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 15:10:30 | INFO     | app.service.document.async_parse_queue:initialize:71 | AsyncDocumentQueue 初始化完成
2025-07-22 15:10:30 | INFO     | app.service.document.async_parse_queue:start_workers:107 | 启动了 3 个工作进程
2025-07-22 15:10:30 | INFO     | app.service.document.parse_document:initialize_document_queue:225 | 文档处理队列已启动
2025-07-22 15:10:35 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: uzbekistan-i-kitaj-strategiceskoe-partnerstvo-v-prodvizenii-zelenogo-razvitia.pdf
2025-07-22 15:10:42 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: ПФ-60 28.01.2022.docx
2025-07-22 15:10:42 | INFO     | app.service.document.document_upload:extract_document_metadata:56 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmpat4ubsqv.docx
2025-07-22 15:10:42 | ERROR    | app.service.document.document_upload:_extract_word_metadata:112 | Word文档元数据提取失败: 'CoreProperties' object has no attribute 'company'
2025-07-22 15:10:42 | INFO     | app.service.document.document_upload:extract_document_metadata:89 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmpat4ubsqv.docx
2025-07-22 15:10:42 | INFO     | app.service.document.document_upload:upload_document_service:183 | 成功提取元数据: ПФ-60 28.01.2022.docx
2025-07-22 15:10:42 | INFO     | app.database.minio_manager:upload_file:53 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmpat4ubsqv.docx -> 99e948c642c4fed4005fb114438e4965/ac58b2194caad5efd4a573a10912d4a2.docx
2025-07-22 15:10:42 | INFO     | app.service.document.async_parse_queue:add_task:94 | 任务已加入队列: f9b364f2-91a5-4903-9544-07e350293fb0 (file_hash: ac58b2194caad5efd4a573a10912d4a2, priority: 0)
2025-07-22 15:10:42 | INFO     | app.service.document.document_upload:upload_document_service:206 | 成功提交任务: f9b364f2-91a5-4903-9544-07e350293fb0
2025-07-22 15:10:42 | INFO     | app.service.document.async_parse_queue:_process_task:160 | 工作进程 worker_2 开始处理任务: f9b364f2-91a5-4903-9544-07e350293fb0
2025-07-22 15:10:42 | INFO     | app.service.document.document_processor:_parse_document:213 | 开始解析文件: ac58b2194caad5efd4a573a10912d4a2.docx
2025-07-22 15:10:55 | INFO     | app.service.document.document_processor:process_document:122 | 开始分块: ac58b2194caad5efd4a573a10912d4a2.docx
2025-07-22 15:15:07 | INFO     | app.service.document.document_processor:process_document:126 | 共有43 片
2025-07-22 15:15:19 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 15:15:56 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 15:15:56 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 15:15:56 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 15:15:56 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 15:16:07 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 15:16:34 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 15:16:34 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 15:16:34 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 15:16:34 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 15:16:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 15:17:20 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 15:17:20 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 15:17:20 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 15:17:20 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 15:17:33 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 15:17:33 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 15:18:05 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 15:18:05 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 15:18:05 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 15:18:15 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 15:18:51 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 15:18:51 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 15:18:51 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 15:18:51 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 15:19:00 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 15:19:36 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 15:19:36 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 15:19:36 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 15:19:36 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 15:19:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 15:20:12 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 15:20:12 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 15:20:12 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 15:20:12 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 15:20:24 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 15:21:01 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 15:21:01 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 15:21:01 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 15:21:01 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 15:21:12 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 15:21:27 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 15:21:27 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 15:21:27 | INFO     | app.service.document.document_processor:process_document:131 | 向量化开始
2025-07-22 15:21:27 | INFO     | app.database.qdrant_manager:store_vectors:85 | 开始插入 43 个向量，批次大小: 100
2025-07-22 15:21:27 | INFO     | app.database.qdrant_manager:store_vectors:94 | 成功插入批次，大小: 43
2025-07-22 15:21:27 | INFO     | app.database.qdrant_manager:store_vectors:102 | 向量插入完成，总耗时: 0.22s
2025-07-22 15:21:27 | INFO     | app.service.document.document_processor:process_document:135 | 摘要生成开始
2025-07-22 15:22:34 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 3685
2025-07-22 15:22:36 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 3792
2025-07-22 15:22:47 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 4576
2025-07-22 15:22:59 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 3570
2025-07-22 15:23:05 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 3724
2025-07-22 15:23:15 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 3061
2025-07-22 15:23:20 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 3168
2025-07-22 15:24:42 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 5389
2025-07-22 15:24:44 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 3627
2025-07-22 15:24:47 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 4640
2025-07-22 15:25:08 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 4448
2025-07-22 15:25:17 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 4566
2025-07-22 15:25:26 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 4599
2025-07-22 15:26:32 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 4590
2025-07-22 15:26:47 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 4465
2025-07-22 15:26:53 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 4718
2025-07-22 15:26:53 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 4543
2025-07-22 15:27:02 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 4588
2025-07-22 15:27:12 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 5253
2025-07-22 15:27:29 | ERROR    | app.service.model_client:generate_summary:202 | 摘要生成失败: <!DOCTYPE html>
<html>
<head>
<title>Error</title>
<style>
html { color-scheme: light dark; }
body { width: 35em; margin: 0 auto;
font-family: Tahoma, Verdana, Arial, sans-serif; }
</style>
</head>
<body>
<h1>An error occurred.</h1>
<p>Sorry, the page you are looking for is currently unavailable.<br/>
Please try again later.</p>
<p>If you are the system administrator of this resource then you should check
the error log for details.</p>
<p><em>Faithfully yours, nginx.</em></p>
</body>
</html>
2025-07-22 15:27:29 | ERROR    | app.utils.exceptions:wrapper:85 | generate_summary RAG系统异常: 摘要生成失败: <!DOCTYPE html>
<html>
<head>
<title>Error</title>
<style>
html { color-scheme: light dark; }
body { width: 35em; margin: 0 auto;
font-family: Tahoma, Verdana, Arial, sans-serif; }
</style>
</head>
<body>
<h1>An error occurred.</h1>
<p>Sorry, the page you are looking for is currently unavailable.<br/>
Please try again later.</p>
<p>If you are the system administrator of this resource then you should check
the error log for details.</p>
<p><em>Faithfully yours, nginx.</em></p>
</body>
</html>
2025-07-22 15:27:29 | ERROR    | app.service.document.document_processor:process_document:145 | 文档处理失败 ac58b2194caad5efd4a573a10912d4a2: 摘要生成失败: <!DOCTYPE html>
<html>
<head>
<title>Error</title>
<style>
html { color-scheme: light dark; }
body { width: 35em; margin: 0 auto;
font-family: Tahoma, Verdana, Arial, sans-serif; }
</style>
</head>
<body>
<h1>An error occurred.</h1>
<p>Sorry, the page you are looking for is currently unavailable.<br/>
Please try again later.</p>
<p>If you are the system administrator of this resource then you should check
the error log for details.</p>
<p><em>Faithfully yours, nginx.</em></p>
</body>
</html>
2025-07-22 15:27:29 | ERROR    | app.service.document.async_parse_queue:_complete_task:191 | 任务处理失败: f9b364f2-91a5-4903-9544-07e350293fb0, 错误: None
2025-07-22 15:31:21 | INFO     | __main__:lifespan:39 | RAG系统关闭中...
2025-07-22 15:31:21 | INFO     | app.service.document.async_parse_queue:_worker_loop:124 | 工作进程 worker_1 被取消
2025-07-22 15:31:21 | INFO     | app.service.document.async_parse_queue:_worker_loop:124 | 工作进程 worker_0 被取消
2025-07-22 15:31:21 | INFO     | app.service.document.async_parse_queue:_worker_loop:124 | 工作进程 worker_2 被取消
2025-07-22 15:31:34 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 15:31:39 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 15:31:39 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 15:31:39 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 15:31:39 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 15:32:35 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 15:32:40 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 15:32:40 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 15:32:40 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 15:32:40 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 15:32:40 | INFO     | __main__:lifespan:24 | RAG系统启动中...
2025-07-22 15:32:40 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 15:32:40 | INFO     | __main__:lifespan:28 | PostgreSQL数据库连接成功
2025-07-22 15:32:40 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 15:32:43 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 15:32:43 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 15:32:43 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 15:32:44 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 15:32:44 | INFO     | app.service.document.async_parse_queue:initialize:49 | AsyncDocumentQueue 初始化完成
2025-07-22 15:32:44 | INFO     | app.service.document.async_parse_queue:initialize:49 | AsyncDocumentQueue 初始化完成
2025-07-22 15:32:44 | INFO     | app.service.document.async_parse_queue:start_workers:95 | 启动了 3 个工作进程
2025-07-22 15:32:44 | INFO     | app.service.document.parse_document:initialize_document_queue:225 | 文档处理队列已启动
2025-07-22 15:32:44 | INFO     | app.service.document.async_parse_queue:_worker_loop:99 | 工作进程 worker_0 开始运行
2025-07-22 15:32:44 | INFO     | app.service.document.async_parse_queue:_worker_loop:99 | 工作进程 worker_1 开始运行
2025-07-22 15:32:44 | INFO     | app.service.document.async_parse_queue:_worker_loop:99 | 工作进程 worker_2 开始运行
2025-07-22 15:32:47 | INFO     | app.database.qdrant_manager:delete_collection:176 | 成功删除Qdrant集合: 99e948c642c4fed4005fb114438e4965
2025-07-22 15:32:47 | INFO     | app.api.knowledge:delete_knowledge:82 | 成功删除知识库: 乌兹 (ID: 4)
2025-07-22 15:35:18 | INFO     | __main__:lifespan:39 | RAG系统关闭中...
2025-07-22 15:35:18 | INFO     | app.service.document.async_parse_queue:_worker_loop:119 | 工作进程 worker_2 被取消
2025-07-22 15:35:18 | INFO     | app.service.document.async_parse_queue:_worker_loop:125 | 工作进程 worker_2 已停止
2025-07-22 15:35:18 | INFO     | app.service.document.async_parse_queue:_worker_loop:119 | 工作进程 worker_0 被取消
2025-07-22 15:35:18 | INFO     | app.service.document.async_parse_queue:_worker_loop:125 | 工作进程 worker_0 已停止
2025-07-22 15:35:18 | INFO     | app.service.document.async_parse_queue:_worker_loop:119 | 工作进程 worker_1 被取消
2025-07-22 15:35:18 | INFO     | app.service.document.async_parse_queue:_worker_loop:125 | 工作进程 worker_1 已停止
2025-07-22 15:35:29 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 15:35:33 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 15:35:33 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 15:35:33 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 15:35:33 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 15:35:33 | INFO     | __main__:lifespan:24 | RAG系统启动中...
2025-07-22 15:35:33 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 15:35:33 | INFO     | __main__:lifespan:28 | PostgreSQL数据库连接成功
2025-07-22 15:35:33 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 15:35:35 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 15:35:35 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 15:35:35 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 15:35:35 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 15:35:35 | INFO     | app.service.document.async_parse_queue:initialize:49 | AsyncDocumentQueue 初始化完成
2025-07-22 15:35:35 | INFO     | app.service.document.async_parse_queue:initialize:49 | AsyncDocumentQueue 初始化完成
2025-07-22 15:35:35 | INFO     | app.service.document.async_parse_queue:start_workers:95 | 启动了 3 个工作进程
2025-07-22 15:35:35 | INFO     | app.service.document.parse_document:initialize_document_queue:225 | 文档处理队列已启动
2025-07-22 15:35:35 | INFO     | app.service.document.async_parse_queue:_worker_loop:99 | 工作进程 worker_0 开始运行
2025-07-22 15:35:35 | INFO     | app.service.document.async_parse_queue:_worker_loop:99 | 工作进程 worker_1 开始运行
2025-07-22 15:35:35 | INFO     | app.service.document.async_parse_queue:_worker_loop:99 | 工作进程 worker_2 开始运行
2025-07-22 15:35:39 | INFO     | app.database.minio_manager:delete_bucket:114 | 存储桶 99e948c642c4fed4005fb114438e4965 中删除了 6 个对象，0 个失败
2025-07-22 15:35:39 | INFO     | app.database.minio_manager:delete_bucket:118 | 成功删除存储桶: 99e948c642c4fed4005fb114438e4965
2025-07-22 15:35:39 | WARNING  | app.database.qdrant_manager:delete_collection:171 | 集合不存在: 99e948c642c4fed4005fb114438e4965
2025-07-22 15:49:09 | INFO     | app.api.knowledge:create_knowledge:47 | 成功创建知识库: 乌兹
2025-07-22 15:49:23 | WARNING  | app.database.minio_manager:delete_bucket:96 | 存储桶不存在: 74b87337454200d4d33f80c4663dc5e5
2025-07-22 15:49:23 | WARNING  | app.database.qdrant_manager:delete_collection:171 | 集合不存在: 74b87337454200d4d33f80c4663dc5e5
2025-07-22 15:49:29 | INFO     | __main__:lifespan:39 | RAG系统关闭中...
2025-07-22 15:49:29 | INFO     | app.service.document.async_parse_queue:_worker_loop:119 | 工作进程 worker_1 被取消
2025-07-22 15:49:29 | INFO     | app.service.document.async_parse_queue:_worker_loop:125 | 工作进程 worker_1 已停止
2025-07-22 15:49:29 | INFO     | app.service.document.async_parse_queue:_worker_loop:119 | 工作进程 worker_0 被取消
2025-07-22 15:49:29 | INFO     | app.service.document.async_parse_queue:_worker_loop:125 | 工作进程 worker_0 已停止
2025-07-22 15:49:29 | INFO     | app.service.document.async_parse_queue:_worker_loop:119 | 工作进程 worker_2 被取消
2025-07-22 15:49:29 | INFO     | app.service.document.async_parse_queue:_worker_loop:125 | 工作进程 worker_2 已停止
2025-07-22 15:49:40 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 15:49:44 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 15:49:44 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 15:49:44 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 15:49:44 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 15:49:44 | INFO     | __main__:lifespan:24 | RAG系统启动中...
2025-07-22 15:49:44 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 15:49:44 | INFO     | __main__:lifespan:28 | PostgreSQL数据库连接成功
2025-07-22 15:49:44 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 15:49:50 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 15:49:50 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 15:49:50 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 15:49:51 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 15:49:51 | INFO     | app.service.document.async_parse_queue:initialize:49 | AsyncDocumentQueue 初始化完成
2025-07-22 15:49:51 | INFO     | app.service.document.async_parse_queue:initialize:49 | AsyncDocumentQueue 初始化完成
2025-07-22 15:49:51 | INFO     | app.service.document.async_parse_queue:start_workers:95 | 启动了 3 个工作进程
2025-07-22 15:49:51 | INFO     | app.service.document.parse_document:initialize_document_queue:225 | 文档处理队列已启动
2025-07-22 15:49:51 | INFO     | app.service.document.async_parse_queue:_worker_loop:99 | 工作进程 worker_0 开始运行
2025-07-22 15:49:51 | INFO     | app.service.document.async_parse_queue:_worker_loop:99 | 工作进程 worker_1 开始运行
2025-07-22 15:49:51 | INFO     | app.service.document.async_parse_queue:_worker_loop:99 | 工作进程 worker_2 开始运行
2025-07-22 15:49:53 | WARNING  | app.database.minio_manager:delete_bucket:96 | 存储桶不存在: 74b87337454200d4d33f80c4663dc5e5
2025-07-22 15:49:53 | WARNING  | app.database.qdrant_manager:delete_collection:171 | 集合不存在: 74b87337454200d4d33f80c4663dc5e5
2025-07-22 15:49:53 | INFO     | app.api.knowledge:delete_knowledge:82 | 成功删除知识库: aaaa (ID: 3)
2025-07-22 15:50:20 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: Biography of the President of the Republic of Uzbekistan.pdf
2025-07-22 15:50:20 | INFO     | app.service.document.document_upload:extract_document_metadata:56 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmpe5753myh.pdf
2025-07-22 15:50:20 | WARNING  | app.service.document.document_upload:extract_document_metadata:83 | 不支持的文件类型: .pdf
2025-07-22 15:50:20 | INFO     | app.service.document.document_upload:extract_document_metadata:89 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmpe5753myh.pdf
2025-07-22 15:50:20 | INFO     | app.service.document.document_upload:upload_document_service:183 | 成功提取元数据: Biography of the President of the Republic of Uzbekistan.pdf
2025-07-22 15:50:20 | INFO     | app.database.minio_manager:upload_file:49 | 存储桶 99e948c642c4fed4005fb114438e4965 不存在，已自动创建
2025-07-22 15:50:20 | INFO     | app.database.minio_manager:upload_file:53 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmpe5753myh.pdf -> 99e948c642c4fed4005fb114438e4965/d3ffacb717d9296e625fde86b5178741.pdf
2025-07-22 15:50:28 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: central-asia-is-a-key-vector-of-uzbekistans-foreign-policy.pdf
2025-07-22 15:50:28 | INFO     | app.service.document.document_upload:extract_document_metadata:56 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmpftd4duhl.pdf
2025-07-22 15:50:28 | WARNING  | app.service.document.document_upload:extract_document_metadata:83 | 不支持的文件类型: .pdf
2025-07-22 15:50:28 | INFO     | app.service.document.document_upload:extract_document_metadata:89 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmpftd4duhl.pdf
2025-07-22 15:50:28 | INFO     | app.service.document.document_upload:upload_document_service:183 | 成功提取元数据: central-asia-is-a-key-vector-of-uzbekistans-foreign-policy.pdf
2025-07-22 15:50:28 | INFO     | app.database.minio_manager:upload_file:53 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmpftd4duhl.pdf -> 99e948c642c4fed4005fb114438e4965/17224950eff21079a7dc90f5478c0338.pdf
2025-07-22 15:50:34 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: PF-37 21.02.2024.docx
2025-07-22 15:50:34 | INFO     | app.service.document.document_upload:extract_document_metadata:56 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmphr2q1jp2.docx
2025-07-22 15:50:35 | ERROR    | app.service.document.document_upload:_extract_word_metadata:112 | Word文档元数据提取失败: 'CoreProperties' object has no attribute 'company'
2025-07-22 15:50:35 | INFO     | app.service.document.document_upload:extract_document_metadata:89 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmphr2q1jp2.docx
2025-07-22 15:50:35 | INFO     | app.service.document.document_upload:upload_document_service:183 | 成功提取元数据: PF-37 21.02.2024.docx
2025-07-22 15:50:35 | INFO     | app.database.minio_manager:upload_file:53 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmphr2q1jp2.docx -> 99e948c642c4fed4005fb114438e4965/d673f1bec5d7572105ca8dc80da42e26.docx
2025-07-22 15:51:45 | INFO     | __main__:lifespan:39 | RAG系统关闭中...
2025-07-22 15:51:45 | INFO     | app.service.document.async_parse_queue:_worker_loop:119 | 工作进程 worker_0 被取消
2025-07-22 15:51:45 | INFO     | app.service.document.async_parse_queue:_worker_loop:125 | 工作进程 worker_0 已停止
2025-07-22 15:51:45 | INFO     | app.service.document.async_parse_queue:_worker_loop:119 | 工作进程 worker_2 被取消
2025-07-22 15:51:45 | INFO     | app.service.document.async_parse_queue:_worker_loop:125 | 工作进程 worker_2 已停止
2025-07-22 15:51:45 | INFO     | app.service.document.async_parse_queue:_worker_loop:119 | 工作进程 worker_1 被取消
2025-07-22 15:51:45 | INFO     | app.service.document.async_parse_queue:_worker_loop:125 | 工作进程 worker_1 已停止
2025-07-22 15:51:56 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 15:52:00 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 15:52:00 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 15:52:00 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 15:52:00 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 15:52:00 | INFO     | __main__:lifespan:24 | RAG系统启动中...
2025-07-22 15:52:00 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 15:52:00 | INFO     | __main__:lifespan:28 | PostgreSQL数据库连接成功
2025-07-22 15:52:00 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 15:52:03 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 15:52:03 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 15:52:03 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 15:52:04 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 15:52:04 | INFO     | app.service.document.async_parse_queue:initialize:49 | AsyncDocumentQueue 初始化完成
2025-07-22 15:52:04 | INFO     | app.service.document.async_parse_queue:initialize:49 | AsyncDocumentQueue 初始化完成
2025-07-22 15:52:04 | INFO     | app.service.document.async_parse_queue:start_workers:95 | 启动了 3 个工作进程
2025-07-22 15:52:04 | INFO     | app.service.document.parse_document:initialize_document_queue:225 | 文档处理队列已启动
2025-07-22 15:52:04 | INFO     | app.service.document.async_parse_queue:_worker_loop:99 | 工作进程 worker_0 开始运行
2025-07-22 15:52:04 | INFO     | app.service.document.async_parse_queue:_worker_loop:99 | 工作进程 worker_1 开始运行
2025-07-22 15:52:04 | INFO     | app.service.document.async_parse_queue:_worker_loop:99 | 工作进程 worker_2 开始运行
2025-07-22 15:52:14 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: ПФ-60 28.01.2022.docx
2025-07-22 15:52:14 | INFO     | app.service.document.document_upload:extract_document_metadata:56 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmpnlf24j9z.docx
2025-07-22 15:52:14 | ERROR    | app.service.document.document_upload:_extract_word_metadata:112 | Word文档元数据提取失败: 'CoreProperties' object has no attribute 'company'
2025-07-22 15:52:14 | INFO     | app.service.document.document_upload:extract_document_metadata:89 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmpnlf24j9z.docx
2025-07-22 15:52:14 | INFO     | app.service.document.document_upload:upload_document_service:183 | 成功提取元数据: ПФ-60 28.01.2022.docx
2025-07-22 15:52:14 | INFO     | app.database.minio_manager:upload_file:53 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmpnlf24j9z.docx -> 99e948c642c4fed4005fb114438e4965/ac58b2194caad5efd4a573a10912d4a2.docx
2025-07-22 15:55:45 | INFO     | __main__:lifespan:39 | RAG系统关闭中...
2025-07-22 15:55:45 | INFO     | app.service.document.async_parse_queue:_worker_loop:119 | 工作进程 worker_1 被取消
2025-07-22 15:55:45 | INFO     | app.service.document.async_parse_queue:_worker_loop:125 | 工作进程 worker_1 已停止
2025-07-22 15:55:45 | INFO     | app.service.document.async_parse_queue:_worker_loop:119 | 工作进程 worker_2 被取消
2025-07-22 15:55:45 | INFO     | app.service.document.async_parse_queue:_worker_loop:125 | 工作进程 worker_2 已停止
2025-07-22 15:55:45 | INFO     | app.service.document.async_parse_queue:_worker_loop:119 | 工作进程 worker_0 被取消
2025-07-22 15:55:45 | INFO     | app.service.document.async_parse_queue:_worker_loop:125 | 工作进程 worker_0 已停止
2025-07-22 15:55:58 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 15:56:02 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 15:56:02 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 15:56:02 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 15:56:02 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 15:56:03 | INFO     | __main__:lifespan:24 | RAG系统启动中...
2025-07-22 15:56:03 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 15:56:03 | INFO     | __main__:lifespan:28 | PostgreSQL数据库连接成功
2025-07-22 15:56:03 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 15:56:06 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 15:56:06 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 15:56:06 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 15:56:06 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 15:56:06 | INFO     | app.service.document.async_parse_queue:initialize:49 | AsyncDocumentQueue 初始化完成
2025-07-22 15:56:06 | INFO     | app.service.document.async_parse_queue:initialize:49 | AsyncDocumentQueue 初始化完成
2025-07-22 15:56:06 | INFO     | app.service.document.async_parse_queue:start_workers:95 | 启动了 3 个工作进程
2025-07-22 15:56:06 | INFO     | app.service.document.parse_document:initialize_document_queue:225 | 文档处理队列已启动
2025-07-22 15:56:06 | INFO     | app.service.document.async_parse_queue:_worker_loop:99 | 工作进程 worker_0 开始运行
2025-07-22 15:56:06 | INFO     | app.service.document.async_parse_queue:_worker_loop:99 | 工作进程 worker_1 开始运行
2025-07-22 15:56:06 | INFO     | app.service.document.async_parse_queue:_worker_loop:99 | 工作进程 worker_2 开始运行
2025-07-22 15:56:08 | INFO     | app.database.minio_manager:delete_bucket:114 | 存储桶 99e948c642c4fed4005fb114438e4965 中删除了 4 个对象，0 个失败
2025-07-22 15:56:08 | INFO     | app.database.minio_manager:delete_bucket:118 | 成功删除存储桶: 99e948c642c4fed4005fb114438e4965
2025-07-22 15:56:08 | WARNING  | app.database.qdrant_manager:delete_collection:171 | 集合不存在: 99e948c642c4fed4005fb114438e4965
2025-07-22 15:56:08 | INFO     | app.api.knowledge:delete_knowledge:82 | 成功删除知识库: 乌兹 (ID: 5)
2025-07-22 15:56:40 | INFO     | app.api.knowledge:create_knowledge:47 | 成功创建知识库: 乌兹
2025-07-22 15:57:00 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: PF-37 21.02.2024.docx
2025-07-22 15:57:00 | INFO     | app.service.document.document_upload:extract_document_metadata:56 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmp38wusnag.docx
2025-07-22 15:57:00 | INFO     | app.service.document.document_upload:extract_document_metadata:89 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmp38wusnag.docx
2025-07-22 15:57:00 | INFO     | app.service.document.document_upload:upload_document_service:182 | 成功提取元数据: PF-37 21.02.2024.docx
2025-07-22 15:57:00 | INFO     | app.database.minio_manager:upload_file:49 | 存储桶 99e948c642c4fed4005fb114438e4965 不存在，已自动创建
2025-07-22 15:57:00 | INFO     | app.database.minio_manager:upload_file:53 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmp38wusnag.docx -> 99e948c642c4fed4005fb114438e4965/d673f1bec5d7572105ca8dc80da42e26.docx
2025-07-22 15:57:33 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: Biography of the President of the Republic of Uzbekistan.pdf
2025-07-22 15:57:33 | INFO     | app.service.document.document_upload:extract_document_metadata:56 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmpmtt9ujmn.pdf
2025-07-22 15:57:33 | WARNING  | app.service.document.document_upload:extract_document_metadata:83 | 不支持的文件类型: .pdf
2025-07-22 15:57:33 | INFO     | app.service.document.document_upload:extract_document_metadata:89 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmpmtt9ujmn.pdf
2025-07-22 15:57:33 | INFO     | app.service.document.document_upload:upload_document_service:182 | 成功提取元数据: Biography of the President of the Republic of Uzbekistan.pdf
2025-07-22 15:57:33 | INFO     | app.database.minio_manager:upload_file:53 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmpmtt9ujmn.pdf -> 99e948c642c4fed4005fb114438e4965/d3ffacb717d9296e625fde86b5178741.pdf
2025-07-22 15:57:39 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: central-asia-is-a-key-vector-of-uzbekistans-foreign-policy.pdf
2025-07-22 15:57:39 | INFO     | app.service.document.document_upload:extract_document_metadata:56 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmp7nf1fjvm.pdf
2025-07-22 15:57:39 | WARNING  | app.service.document.document_upload:extract_document_metadata:83 | 不支持的文件类型: .pdf
2025-07-22 15:57:39 | INFO     | app.service.document.document_upload:extract_document_metadata:89 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmp7nf1fjvm.pdf
2025-07-22 15:57:39 | INFO     | app.service.document.document_upload:upload_document_service:182 | 成功提取元数据: central-asia-is-a-key-vector-of-uzbekistans-foreign-policy.pdf
2025-07-22 15:57:39 | INFO     | app.database.minio_manager:upload_file:53 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmp7nf1fjvm.pdf -> 99e948c642c4fed4005fb114438e4965/17224950eff21079a7dc90f5478c0338.pdf
2025-07-22 15:57:45 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: the-multidimensional-foreign-policy-of-new-uzbekistan.pdf
2025-07-22 15:57:45 | INFO     | app.service.document.document_upload:extract_document_metadata:56 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmpg77_axce.pdf
2025-07-22 15:57:45 | WARNING  | app.service.document.document_upload:extract_document_metadata:83 | 不支持的文件类型: .pdf
2025-07-22 15:57:45 | INFO     | app.service.document.document_upload:extract_document_metadata:89 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmpg77_axce.pdf
2025-07-22 15:57:45 | INFO     | app.service.document.document_upload:upload_document_service:182 | 成功提取元数据: the-multidimensional-foreign-policy-of-new-uzbekistan.pdf
2025-07-22 15:57:45 | INFO     | app.database.minio_manager:upload_file:53 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmpg77_axce.pdf -> 99e948c642c4fed4005fb114438e4965/5ee5b529f7790fd92dbf99b6c59659cc.pdf
2025-07-22 15:57:50 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: uzbekistan-i-kitaj-strategiceskoe-partnerstvo-v-prodvizenii-zelenogo-razvitia.pdf
2025-07-22 15:57:50 | INFO     | app.service.document.document_upload:extract_document_metadata:56 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmpj0icxklr.pdf
2025-07-22 15:57:50 | WARNING  | app.service.document.document_upload:extract_document_metadata:83 | 不支持的文件类型: .pdf
2025-07-22 15:57:50 | INFO     | app.service.document.document_upload:extract_document_metadata:89 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmpj0icxklr.pdf
2025-07-22 15:57:50 | INFO     | app.service.document.document_upload:upload_document_service:182 | 成功提取元数据: uzbekistan-i-kitaj-strategiceskoe-partnerstvo-v-prodvizenii-zelenogo-razvitia.pdf
2025-07-22 15:57:50 | INFO     | app.database.minio_manager:upload_file:53 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmpj0icxklr.pdf -> 99e948c642c4fed4005fb114438e4965/279e472ff8f1e2672398020a32630604.pdf
2025-07-22 15:57:56 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: ПФ-60 28.01.2022.docx
2025-07-22 15:57:56 | INFO     | app.service.document.document_upload:extract_document_metadata:56 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmphjumjccd.docx
2025-07-22 15:57:56 | INFO     | app.service.document.document_upload:extract_document_metadata:89 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmphjumjccd.docx
2025-07-22 15:57:56 | INFO     | app.service.document.document_upload:upload_document_service:182 | 成功提取元数据: ПФ-60 28.01.2022.docx
2025-07-22 15:57:56 | INFO     | app.database.minio_manager:upload_file:53 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmphjumjccd.docx -> 99e948c642c4fed4005fb114438e4965/ac58b2194caad5efd4a573a10912d4a2.docx
2025-07-22 15:58:03 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: 对外投资合作国别指南（乌兹别克斯坦）.pdf
2025-07-22 15:58:03 | INFO     | app.service.document.document_upload:extract_document_metadata:56 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmpz1yuswty.pdf
2025-07-22 15:58:03 | WARNING  | app.service.document.document_upload:extract_document_metadata:83 | 不支持的文件类型: .pdf
2025-07-22 15:58:03 | INFO     | app.service.document.document_upload:extract_document_metadata:89 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmpz1yuswty.pdf
2025-07-22 15:58:03 | INFO     | app.service.document.document_upload:upload_document_service:182 | 成功提取元数据: 对外投资合作国别指南（乌兹别克斯坦）.pdf
2025-07-22 15:58:03 | INFO     | app.database.minio_manager:upload_file:53 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmpz1yuswty.pdf -> 99e948c642c4fed4005fb114438e4965/2d6cc1c63af8ef20abf6177fca49e097.pdf
2025-07-22 15:58:10 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: 营商环境指南（乌兹别克斯坦）.pdf
2025-07-22 15:58:10 | INFO     | app.service.document.document_upload:extract_document_metadata:56 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmp66lracqw.pdf
2025-07-22 15:58:10 | WARNING  | app.service.document.document_upload:extract_document_metadata:83 | 不支持的文件类型: .pdf
2025-07-22 15:58:10 | INFO     | app.service.document.document_upload:extract_document_metadata:89 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmp66lracqw.pdf
2025-07-22 15:58:10 | INFO     | app.service.document.document_upload:upload_document_service:182 | 成功提取元数据: 营商环境指南（乌兹别克斯坦）.pdf
2025-07-22 15:58:10 | INFO     | app.database.minio_manager:upload_file:53 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmp66lracqw.pdf -> 99e948c642c4fed4005fb114438e4965/4f0e3d3f79ca4f09321c61ce679af449.pdf
2025-07-22 15:58:16 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: 中国居民投资税收指南（乌兹别克斯坦）.pdf
2025-07-22 15:58:16 | INFO     | app.service.document.document_upload:extract_document_metadata:56 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmpib28h9mi.pdf
2025-07-22 15:58:16 | WARNING  | app.service.document.document_upload:extract_document_metadata:83 | 不支持的文件类型: .pdf
2025-07-22 15:58:16 | INFO     | app.service.document.document_upload:extract_document_metadata:89 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmpib28h9mi.pdf
2025-07-22 15:58:16 | INFO     | app.service.document.document_upload:upload_document_service:182 | 成功提取元数据: 中国居民投资税收指南（乌兹别克斯坦）.pdf
2025-07-22 15:58:16 | INFO     | app.database.minio_manager:upload_file:53 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmpib28h9mi.pdf -> 99e948c642c4fed4005fb114438e4965/12134a1bc2e7a7906cf3b09afbb066ca.pdf
2025-07-22 15:58:23 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: 中华人民共和国和乌兹别克斯坦共和国关于新时代全天候全面战略伙伴关系的联合声明.pdf
2025-07-22 15:58:23 | INFO     | app.service.document.document_upload:extract_document_metadata:56 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmpnbvmllby.pdf
2025-07-22 15:58:23 | WARNING  | app.service.document.document_upload:extract_document_metadata:83 | 不支持的文件类型: .pdf
2025-07-22 15:58:23 | INFO     | app.service.document.document_upload:extract_document_metadata:89 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmpnbvmllby.pdf
2025-07-22 15:58:23 | INFO     | app.service.document.document_upload:upload_document_service:182 | 成功提取元数据: 中华人民共和国和乌兹别克斯坦共和国关于新时代全天候全面战略伙伴关系的联合声明.pdf
2025-07-22 15:58:23 | INFO     | app.database.minio_manager:upload_file:53 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmpnbvmllby.pdf -> 99e948c642c4fed4005fb114438e4965/0f2e292a160aa2820ea3c077dc668d80.pdf
2025-07-22 16:17:28 | INFO     | __main__:lifespan:39 | RAG系统关闭中...
2025-07-22 16:17:28 | INFO     | app.service.document.async_parse_queue:_worker_loop:119 | 工作进程 worker_1 被取消
2025-07-22 16:17:28 | INFO     | app.service.document.async_parse_queue:_worker_loop:125 | 工作进程 worker_1 已停止
2025-07-22 16:17:29 | INFO     | app.service.document.async_parse_queue:_worker_loop:119 | 工作进程 worker_0 被取消
2025-07-22 16:17:29 | INFO     | app.service.document.async_parse_queue:_worker_loop:125 | 工作进程 worker_0 已停止
2025-07-22 16:17:29 | INFO     | app.service.document.async_parse_queue:_worker_loop:119 | 工作进程 worker_2 被取消
2025-07-22 16:17:29 | INFO     | app.service.document.async_parse_queue:_worker_loop:125 | 工作进程 worker_2 已停止
2025-07-22 16:17:41 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 16:17:45 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 16:17:45 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 16:17:45 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 16:17:45 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 16:17:45 | INFO     | __main__:lifespan:26 | RAG系统启动中...
2025-07-22 16:17:45 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 16:17:45 | INFO     | __main__:lifespan:30 | PostgreSQL数据库连接成功
2025-07-22 16:17:45 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 16:17:47 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 16:17:47 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 16:17:47 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 16:17:48 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 16:17:48 | INFO     | app.service.document.async_parse_queue:initialize:49 | AsyncDocumentQueue 初始化完成
2025-07-22 16:17:48 | INFO     | app.service.document.async_parse_queue:initialize:49 | AsyncDocumentQueue 初始化完成
2025-07-22 16:17:48 | INFO     | app.service.document.async_parse_queue:start_workers:95 | 启动了 3 个工作进程
2025-07-22 16:17:48 | INFO     | app.service.document.parse_document:initialize_document_queue:244 | 文档处理队列已启动
2025-07-22 16:17:48 | INFO     | app.service.document.parse_task:start_workers:164 | Starting 2 worker coroutines
2025-07-22 16:17:48 | INFO     | __main__:lifespan:41 | 定期清理任务已启动
2025-07-22 16:17:48 | INFO     | app.service.document.async_parse_queue:_worker_loop:99 | 工作进程 worker_0 开始运行
2025-07-22 16:17:48 | INFO     | app.service.document.async_parse_queue:_worker_loop:99 | 工作进程 worker_1 开始运行
2025-07-22 16:17:48 | INFO     | app.service.document.async_parse_queue:_worker_loop:99 | 工作进程 worker_2 开始运行
2025-07-22 16:17:48 | INFO     | app.service.document.parse_task:_worker:43 | Worker started
2025-07-22 16:17:48 | INFO     | app.service.document.parse_task:_worker:43 | Worker started
2025-07-22 16:18:33 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 16:18:33 | INFO     | app.service.document.parse_task:create_task:22 | Creating new task with ID: 3d5d8e41-4962-4d49-9499-889fef8fc55d
2025-07-22 16:18:33 | INFO     | app.service.document.parse_task:_worker:54 | Processing task: 3d5d8e41-4962-4d49-9499-889fef8fc55d
2025-07-22 16:18:33 | INFO     | app.service.document.parse_task:_worker:71 | Task 3d5d8e41-4962-4d49-9499-889fef8fc55d completed successfully
2025-07-22 16:18:34 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 16:18:34 | INFO     | app.service.document.parse_task:create_task:22 | Creating new task with ID: f745fabc-b645-431e-85e7-edc13917c846
2025-07-22 16:18:34 | INFO     | app.service.document.parse_task:_worker:54 | Processing task: f745fabc-b645-431e-85e7-edc13917c846
2025-07-22 16:18:34 | INFO     | app.service.document.parse_task:_worker:71 | Task f745fabc-b645-431e-85e7-edc13917c846 completed successfully
2025-07-22 16:18:34 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 16:18:34 | INFO     | app.service.document.parse_task:create_task:22 | Creating new task with ID: 5ada539a-53cb-4ed2-9dcb-ad499ff147fc
2025-07-22 16:18:34 | INFO     | app.service.document.parse_task:_worker:54 | Processing task: 5ada539a-53cb-4ed2-9dcb-ad499ff147fc
2025-07-22 16:18:34 | INFO     | app.service.document.parse_task:_worker:71 | Task 5ada539a-53cb-4ed2-9dcb-ad499ff147fc completed successfully
2025-07-22 16:18:34 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 16:18:34 | INFO     | app.service.document.parse_task:create_task:22 | Creating new task with ID: 764959c2-ea7b-425b-a1e3-025bb5829274
2025-07-22 16:18:34 | INFO     | app.service.document.parse_task:_worker:54 | Processing task: 764959c2-ea7b-425b-a1e3-025bb5829274
2025-07-22 16:18:34 | INFO     | app.service.document.parse_task:_worker:71 | Task 764959c2-ea7b-425b-a1e3-025bb5829274 completed successfully
2025-07-22 16:26:28 | WARNING  | app.service.document.parse_document:parse_document_service:216 | 文件 d673f1bec5d7572105ca8dc80da42e26 正在处理中
2025-07-22 16:26:28 | WARNING  | app.service.document.parse_document:parse_document_service:216 | 文件 d3ffacb717d9296e625fde86b5178741 正在处理中
2025-07-22 16:26:28 | WARNING  | app.service.document.parse_document:parse_document_service:216 | 文件 17224950eff21079a7dc90f5478c0338 正在处理中
2025-07-22 16:26:28 | WARNING  | app.service.document.parse_document:parse_document_service:216 | 文件 5ee5b529f7790fd92dbf99b6c59659cc 正在处理中
2025-07-22 16:26:54 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 16:26:54 | INFO     | app.service.document.parse_task:create_task:22 | Creating new task with ID: 3f37c753-8f3c-4007-8e7d-81171bf1251d
2025-07-22 16:26:54 | INFO     | app.service.document.parse_task:_worker:54 | Processing task: 3f37c753-8f3c-4007-8e7d-81171bf1251d
2025-07-22 16:26:54 | INFO     | app.service.document.parse_task:_worker:71 | Task 3f37c753-8f3c-4007-8e7d-81171bf1251d completed successfully
2025-07-22 16:26:55 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 16:26:55 | INFO     | app.service.document.parse_task:create_task:22 | Creating new task with ID: 70f44eeb-24e8-4003-8e18-fe8de756e5e9
2025-07-22 16:26:55 | INFO     | app.service.document.parse_task:_worker:54 | Processing task: 70f44eeb-24e8-4003-8e18-fe8de756e5e9
2025-07-22 16:26:55 | INFO     | app.service.document.parse_task:_worker:71 | Task 70f44eeb-24e8-4003-8e18-fe8de756e5e9 completed successfully
2025-07-22 16:26:55 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 16:26:55 | INFO     | app.service.document.parse_task:create_task:22 | Creating new task with ID: c988d205-3ba1-4de6-9d06-a4f9423bd07b
2025-07-22 16:26:55 | INFO     | app.service.document.parse_task:_worker:54 | Processing task: c988d205-3ba1-4de6-9d06-a4f9423bd07b
2025-07-22 16:26:55 | INFO     | app.service.document.parse_task:_worker:71 | Task c988d205-3ba1-4de6-9d06-a4f9423bd07b completed successfully
2025-07-22 16:26:55 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 16:26:55 | INFO     | app.service.document.parse_task:create_task:22 | Creating new task with ID: 135f4317-7079-4b8d-895b-3018fae6b2ab
2025-07-22 16:26:55 | INFO     | app.service.document.parse_task:_worker:54 | Processing task: 135f4317-7079-4b8d-895b-3018fae6b2ab
2025-07-22 16:26:55 | INFO     | app.service.document.parse_task:_worker:71 | Task 135f4317-7079-4b8d-895b-3018fae6b2ab completed successfully
2025-07-22 16:26:59 | INFO     | __main__:lifespan:46 | RAG系统关闭中...
2025-07-22 16:26:59 | INFO     | __main__:lifespan:52 | 定期清理任务已取消
2025-07-22 16:26:59 | INFO     | app.service.document.async_parse_queue:_worker_loop:119 | 工作进程 worker_1 被取消
2025-07-22 16:26:59 | INFO     | app.service.document.async_parse_queue:_worker_loop:125 | 工作进程 worker_1 已停止
2025-07-22 16:26:59 | INFO     | app.service.document.async_parse_queue:_worker_loop:119 | 工作进程 worker_2 被取消
2025-07-22 16:26:59 | INFO     | app.service.document.async_parse_queue:_worker_loop:125 | 工作进程 worker_2 已停止
2025-07-22 16:26:59 | INFO     | app.service.document.async_parse_queue:_worker_loop:119 | 工作进程 worker_0 被取消
2025-07-22 16:26:59 | INFO     | app.service.document.async_parse_queue:_worker_loop:125 | 工作进程 worker_0 已停止
2025-07-22 16:27:12 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 16:27:17 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 16:27:17 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 16:27:17 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 16:27:17 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 16:27:17 | INFO     | __main__:lifespan:25 | RAG系统启动中...
2025-07-22 16:27:17 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 16:27:17 | INFO     | __main__:lifespan:29 | PostgreSQL数据库连接成功
2025-07-22 16:27:17 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 16:27:21 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 16:27:21 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 16:27:21 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 16:27:21 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 16:27:21 | INFO     | app.service.document.async_parse_queue:initialize:49 | AsyncDocumentQueue 初始化完成
2025-07-22 16:27:21 | INFO     | app.service.document.async_parse_queue:initialize:49 | AsyncDocumentQueue 初始化完成
2025-07-22 16:27:21 | INFO     | app.service.document.async_parse_queue:start_workers:95 | 启动了 3 个工作进程
2025-07-22 16:27:21 | INFO     | app.service.document.parse_document:initialize_document_queue:242 | 文档处理队列已启动
2025-07-22 16:27:21 | INFO     | app.service.document.async_parse_queue:_worker_loop:99 | 工作进程 worker_0 开始运行
2025-07-22 16:27:21 | INFO     | app.service.document.async_parse_queue:_worker_loop:99 | 工作进程 worker_1 开始运行
2025-07-22 16:27:21 | INFO     | app.service.document.async_parse_queue:_worker_loop:99 | 工作进程 worker_2 开始运行
2025-07-22 16:27:26 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 16:27:26 | INFO     | app.service.document.document_processor:_parse_document:213 | 开始解析文件: d673f1bec5d7572105ca8dc80da42e26.docx
2025-07-22 16:27:30 | INFO     | app.service.document.document_processor:process_document:122 | 开始分块: d673f1bec5d7572105ca8dc80da42e26.docx
2025-07-22 16:27:57 | INFO     | app.service.document.document_processor:process_document:126 | 共有32 片
2025-07-22 16:28:08 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 16:28:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 16:28:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 16:28:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 16:28:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 16:28:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:01:49 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 17:01:53 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 17:01:53 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 17:01:53 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 17:01:53 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 17:01:53 | INFO     | __main__:lifespan:26 | RAG系统启动中...
2025-07-22 17:01:53 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 17:01:53 | INFO     | __main__:lifespan:30 | PostgreSQL数据库连接成功
2025-07-22 17:01:53 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 17:02:01 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 17:02:01 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 17:02:01 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 17:02:01 | INFO     | app.service.document.document_processor:initialize:97 | 文档处理器初始化完成
2025-07-22 17:02:01 | INFO     | app.service.document.async_parse_queue:initialize:49 | AsyncDocumentQueue 初始化完成
2025-07-22 17:02:01 | INFO     | app.service.document.async_parse_queue:initialize:49 | AsyncDocumentQueue 初始化完成
2025-07-22 17:02:01 | INFO     | app.service.document.async_parse_queue:start_workers:95 | 启动了 3 个工作进程
2025-07-22 17:02:01 | INFO     | app.service.document.parse_document:initialize_document_queue:240 | 文档处理队列已启动
2025-07-22 17:02:01 | INFO     | app.service.document.async_parse_queue:_worker_loop:99 | 工作进程 worker_0 开始运行
2025-07-22 17:02:01 | INFO     | app.service.document.async_parse_queue:_worker_loop:99 | 工作进程 worker_1 开始运行
2025-07-22 17:02:01 | INFO     | app.service.document.async_parse_queue:_worker_loop:99 | 工作进程 worker_2 开始运行
2025-07-22 17:02:21 | WARNING  | app.service.document.parse_document:parse_document_service:216 | 文件 d673f1bec5d7572105ca8dc80da42e26 正在处理中
2025-07-22 17:02:22 | INFO     | app.service.document.document_processor:_parse_document:214 | 开始解析文件: d3ffacb717d9296e625fde86b5178741.pdf
2025-07-22 17:03:17 | INFO     | app.service.document.document_processor:_parse_document:214 | 开始解析文件: 17224950eff21079a7dc90f5478c0338.pdf
2025-07-22 17:03:42 | WARNING  | app.service.document.parse_document:parse_document_service:216 | 文件 d673f1bec5d7572105ca8dc80da42e26 正在处理中
2025-07-22 17:03:42 | INFO     | app.service.document.document_processor:process_document:123 | 开始分块: d3ffacb717d9296e625fde86b5178741.pdf
2025-07-22 17:03:42 | ERROR    | app.service.document.document_processor:process_document:146 | 文档处理失败 d3ffacb717d9296e625fde86b5178741: 'NoneType' object has no attribute 'chunk'
2025-07-22 17:03:42 | WARNING  | app.service.document.parse_document:parse_document_service:216 | 文件 d3ffacb717d9296e625fde86b5178741 正在处理中
2025-07-22 17:03:42 | WARNING  | app.service.document.parse_document:parse_document_service:216 | 文件 17224950eff21079a7dc90f5478c0338 正在处理中
2025-07-22 17:03:42 | INFO     | app.service.document.document_processor:_parse_document:214 | 开始解析文件: 5ee5b529f7790fd92dbf99b6c59659cc.pdf
2025-07-22 17:04:06 | WARNING  | app.service.document.parse_document:parse_document_service:216 | 文件 5ee5b529f7790fd92dbf99b6c59659cc 正在处理中
2025-07-22 17:04:06 | INFO     | app.service.document.document_processor:process_document:123 | 开始分块: 17224950eff21079a7dc90f5478c0338.pdf
2025-07-22 17:04:06 | ERROR    | app.service.document.document_processor:process_document:146 | 文档处理失败 17224950eff21079a7dc90f5478c0338: 'NoneType' object has no attribute 'chunk'
2025-07-22 17:04:06 | INFO     | app.service.document.document_processor:process_document:123 | 开始分块: 5ee5b529f7790fd92dbf99b6c59659cc.pdf
2025-07-22 17:04:06 | ERROR    | app.service.document.document_processor:process_document:146 | 文档处理失败 5ee5b529f7790fd92dbf99b6c59659cc: 'NoneType' object has no attribute 'chunk'
2025-07-22 17:05:52 | INFO     | __main__:lifespan:42 | RAG系统关闭中...
2025-07-22 17:05:52 | INFO     | app.service.document.async_parse_queue:_worker_loop:119 | 工作进程 worker_0 被取消
2025-07-22 17:05:52 | INFO     | app.service.document.async_parse_queue:_worker_loop:125 | 工作进程 worker_0 已停止
2025-07-22 17:05:52 | INFO     | app.service.document.async_parse_queue:_worker_loop:119 | 工作进程 worker_1 被取消
2025-07-22 17:05:52 | INFO     | app.service.document.async_parse_queue:_worker_loop:125 | 工作进程 worker_1 已停止
2025-07-22 17:05:52 | INFO     | app.service.document.async_parse_queue:_worker_loop:119 | 工作进程 worker_2 被取消
2025-07-22 17:05:52 | INFO     | app.service.document.async_parse_queue:_worker_loop:125 | 工作进程 worker_2 已停止
2025-07-22 17:06:05 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 17:06:10 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 17:06:10 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 17:06:10 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 17:06:10 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 17:06:10 | INFO     | __main__:lifespan:25 | RAG系统启动中...
2025-07-22 17:06:10 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 17:06:10 | INFO     | __main__:lifespan:29 | PostgreSQL数据库连接成功
2025-07-22 17:06:10 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 17:06:14 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 17:06:14 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 17:06:14 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 17:06:14 | INFO     | app.service.document.document_processor:initialize:97 | 文档处理器初始化完成
2025-07-22 17:06:14 | INFO     | app.service.document.async_parse_queue:initialize:49 | AsyncDocumentQueue 初始化完成
2025-07-22 17:06:14 | INFO     | app.service.document.async_parse_queue:initialize:49 | AsyncDocumentQueue 初始化完成
2025-07-22 17:06:14 | INFO     | app.service.document.async_parse_queue:start_workers:95 | 启动了 3 个工作进程
2025-07-22 17:06:14 | INFO     | app.service.document.parse_document:initialize_document_queue:238 | 文档处理队列已启动
2025-07-22 17:06:14 | INFO     | app.service.document.async_parse_queue:_worker_loop:99 | 工作进程 worker_0 开始运行
2025-07-22 17:06:14 | INFO     | app.service.document.async_parse_queue:_worker_loop:99 | 工作进程 worker_1 开始运行
2025-07-22 17:06:14 | INFO     | app.service.document.async_parse_queue:_worker_loop:99 | 工作进程 worker_2 开始运行
2025-07-22 17:06:19 | INFO     | app.service.document.async_parse_queue:add_task:70 | 任务已加入队列: 4ca98f5a-d8d3-4798-9ab7-057037b3c5dc (file_hash: d673f1bec5d7572105ca8dc80da42e26)
2025-07-22 17:06:19 | INFO     | app.service.document.async_parse_queue:_process_task:143 | 工作进程 worker_0 开始处理任务: 4ca98f5a-d8d3-4798-9ab7-057037b3c5dc
2025-07-22 17:06:19 | INFO     | app.service.document.async_parse_queue:add_task:70 | 任务已加入队列: f2dbb2f6-8167-466b-9a0a-39673b2ee78a (file_hash: d3ffacb717d9296e625fde86b5178741)
2025-07-22 17:06:19 | INFO     | app.service.document.async_parse_queue:_process_task:143 | 工作进程 worker_2 开始处理任务: f2dbb2f6-8167-466b-9a0a-39673b2ee78a
2025-07-22 17:06:19 | INFO     | app.service.document.async_parse_queue:add_task:70 | 任务已加入队列: ce891a42-ed30-4d33-aa0e-585aab3590e5 (file_hash: 17224950eff21079a7dc90f5478c0338)
2025-07-22 17:06:19 | INFO     | app.service.document.async_parse_queue:_process_task:143 | 工作进程 worker_1 开始处理任务: ce891a42-ed30-4d33-aa0e-585aab3590e5
2025-07-22 17:06:19 | INFO     | app.service.document.async_parse_queue:add_task:70 | 任务已加入队列: 5d98abf9-ab10-43d1-b9d8-e85dac318934 (file_hash: 5ee5b529f7790fd92dbf99b6c59659cc)
2025-07-22 17:06:19 | INFO     | app.service.document.document_processor:_parse_document:214 | 开始解析文件: d673f1bec5d7572105ca8dc80da42e26.docx
2025-07-22 17:06:24 | INFO     | app.service.document.document_processor:_parse_document:214 | 开始解析文件: d3ffacb717d9296e625fde86b5178741.pdf
2025-07-22 17:07:20 | WARNING  | app.service.document.parse_document:parse_document_service:215 | 文件 d673f1bec5d7572105ca8dc80da42e26 正在处理中
2025-07-22 17:07:20 | INFO     | app.service.document.document_processor:process_document:123 | 开始分块: d673f1bec5d7572105ca8dc80da42e26.docx
2025-07-22 17:07:51 | INFO     | app.service.document.document_processor:process_document:123 | 开始分块: d3ffacb717d9296e625fde86b5178741.pdf
2025-07-22 17:07:51 | WARNING  | app.service.document.parse_document:parse_document_service:215 | 文件 d3ffacb717d9296e625fde86b5178741 正在处理中
2025-07-22 17:07:51 | INFO     | app.service.document.document_processor:process_document:127 | 共有32 片
2025-07-22 17:07:51 | INFO     | app.service.document.document_processor:_parse_document:214 | 开始解析文件: 17224950eff21079a7dc90f5478c0338.pdf
2025-07-22 17:08:16 | WARNING  | app.service.document.parse_document:parse_document_service:215 | 文件 17224950eff21079a7dc90f5478c0338 正在处理中
2025-07-22 17:08:16 | INFO     | app.service.document.document_processor:process_document:127 | 共有2 片
2025-07-22 17:08:16 | WARNING  | app.service.document.parse_document:parse_document_service:215 | 文件 5ee5b529f7790fd92dbf99b6c59659cc 正在处理中
2025-07-22 17:08:16 | INFO     | app.service.document.document_processor:process_document:123 | 开始分块: 17224950eff21079a7dc90f5478c0338.pdf
2025-07-22 17:08:16 | INFO     | app.service.document.document_processor:process_document:127 | 共有6 片
2025-07-22 17:08:21 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:08:58 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:08:58 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:08:58 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:08:58 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:08:58 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:08:58 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:08:58 | INFO     | app.service.document.document_processor:process_document:132 | 向量化开始
2025-07-22 17:08:58 | INFO     | app.database.qdrant_manager:_create_collection_if_not_exists:63 | 创建Qdrant集合: 99e948c642c4fed4005fb114438e4965
2025-07-22 17:08:58 | INFO     | app.database.qdrant_manager:store_vectors:85 | 开始插入 2 个向量，批次大小: 100
2025-07-22 17:08:58 | INFO     | app.database.qdrant_manager:store_vectors:94 | 成功插入批次，大小: 2
2025-07-22 17:08:58 | INFO     | app.database.qdrant_manager:store_vectors:102 | 向量插入完成，总耗时: 0.16s
2025-07-22 17:08:58 | INFO     | app.service.document.document_processor:process_document:136 | 摘要生成开始
2025-07-22 17:09:08 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:09:08 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:09:08 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:09:08 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:09:08 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:09:11 | ERROR    | app.service.model_client:generate_summary:202 | 摘要生成失败: <!DOCTYPE html>
<html>
<head>
<title>Error</title>
<style>
html { color-scheme: light dark; }
body { width: 35em; margin: 0 auto;
font-family: Tahoma, Verdana, Arial, sans-serif; }
</style>
</head>
<body>
<h1>An error occurred.</h1>
<p>Sorry, the page you are looking for is currently unavailable.<br/>
Please try again later.</p>
<p>If you are the system administrator of this resource then you should check
the error log for details.</p>
<p><em>Faithfully yours, nginx.</em></p>
</body>
</html>
2025-07-22 17:09:11 | ERROR    | app.utils.exceptions:wrapper:85 | generate_summary RAG系统异常: 摘要生成失败: <!DOCTYPE html>
<html>
<head>
<title>Error</title>
<style>
html { color-scheme: light dark; }
body { width: 35em; margin: 0 auto;
font-family: Tahoma, Verdana, Arial, sans-serif; }
</style>
</head>
<body>
<h1>An error occurred.</h1>
<p>Sorry, the page you are looking for is currently unavailable.<br/>
Please try again later.</p>
<p>If you are the system administrator of this resource then you should check
the error log for details.</p>
<p><em>Faithfully yours, nginx.</em></p>
</body>
</html>
2025-07-22 17:09:11 | ERROR    | app.service.document.document_processor:process_document:146 | 文档处理失败 d3ffacb717d9296e625fde86b5178741: 摘要生成失败: <!DOCTYPE html>
<html>
<head>
<title>Error</title>
<style>
html { color-scheme: light dark; }
body { width: 35em; margin: 0 auto;
font-family: Tahoma, Verdana, Arial, sans-serif; }
</style>
</head>
<body>
<h1>An error occurred.</h1>
<p>Sorry, the page you are looking for is currently unavailable.<br/>
Please try again later.</p>
<p>If you are the system administrator of this resource then you should check
the error log for details.</p>
<p><em>Faithfully yours, nginx.</em></p>
</body>
</html>
2025-07-22 17:09:11 | ERROR    | app.service.document.async_parse_queue:_complete_task:173 | 任务处理失败: f2dbb2f6-8167-466b-9a0a-39673b2ee78a, 错误: None
2025-07-22 17:09:11 | INFO     | app.service.document.async_parse_queue:_process_task:143 | 工作进程 worker_2 开始处理任务: 5d98abf9-ab10-43d1-b9d8-e85dac318934
2025-07-22 17:09:11 | INFO     | app.service.document.document_processor:_parse_document:214 | 开始解析文件: 5ee5b529f7790fd92dbf99b6c59659cc.pdf
2025-07-22 17:09:34 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:09:34 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:09:34 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:09:34 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:09:34 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:09:34 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:09:34 | INFO     | app.service.document.document_processor:process_document:123 | 开始分块: 5ee5b529f7790fd92dbf99b6c59659cc.pdf
2025-07-22 17:09:34 | INFO     | app.service.document.document_processor:process_document:127 | 共有9 片
2025-07-22 17:09:34 | INFO     | app.service.document.document_processor:process_document:132 | 向量化开始
2025-07-22 17:09:34 | INFO     | app.database.qdrant_manager:store_vectors:85 | 开始插入 6 个向量，批次大小: 100
2025-07-22 17:09:34 | INFO     | app.database.qdrant_manager:store_vectors:94 | 成功插入批次，大小: 6
2025-07-22 17:09:34 | INFO     | app.database.qdrant_manager:store_vectors:102 | 向量插入完成，总耗时: 0.06s
2025-07-22 17:09:34 | INFO     | app.service.document.document_processor:process_document:136 | 摘要生成开始
2025-07-22 17:09:36 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:09:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:09:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:09:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:09:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:09:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:09:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:09:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:09:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:09:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:09:42 | ERROR    | app.service.model_client:generate_summary:202 | 摘要生成失败: <!DOCTYPE html>
<html>
<head>
<title>Error</title>
<style>
html { color-scheme: light dark; }
body { width: 35em; margin: 0 auto;
font-family: Tahoma, Verdana, Arial, sans-serif; }
</style>
</head>
<body>
<h1>An error occurred.</h1>
<p>Sorry, the page you are looking for is currently unavailable.<br/>
Please try again later.</p>
<p>If you are the system administrator of this resource then you should check
the error log for details.</p>
<p><em>Faithfully yours, nginx.</em></p>
</body>
</html>
2025-07-22 17:09:42 | ERROR    | app.utils.exceptions:wrapper:85 | generate_summary RAG系统异常: 摘要生成失败: <!DOCTYPE html>
<html>
<head>
<title>Error</title>
<style>
html { color-scheme: light dark; }
body { width: 35em; margin: 0 auto;
font-family: Tahoma, Verdana, Arial, sans-serif; }
</style>
</head>
<body>
<h1>An error occurred.</h1>
<p>Sorry, the page you are looking for is currently unavailable.<br/>
Please try again later.</p>
<p>If you are the system administrator of this resource then you should check
the error log for details.</p>
<p><em>Faithfully yours, nginx.</em></p>
</body>
</html>
2025-07-22 17:09:42 | ERROR    | app.service.document.document_processor:process_document:146 | 文档处理失败 17224950eff21079a7dc90f5478c0338: 摘要生成失败: <!DOCTYPE html>
<html>
<head>
<title>Error</title>
<style>
html { color-scheme: light dark; }
body { width: 35em; margin: 0 auto;
font-family: Tahoma, Verdana, Arial, sans-serif; }
</style>
</head>
<body>
<h1>An error occurred.</h1>
<p>Sorry, the page you are looking for is currently unavailable.<br/>
Please try again later.</p>
<p>If you are the system administrator of this resource then you should check
the error log for details.</p>
<p><em>Faithfully yours, nginx.</em></p>
</body>
</html>
2025-07-22 17:09:42 | ERROR    | app.service.document.async_parse_queue:_complete_task:173 | 任务处理失败: ce891a42-ed30-4d33-aa0e-585aab3590e5, 错误: None
2025-07-22 17:09:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:09:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:09:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:09:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:09:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:09:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:09:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:09:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:09:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:09:48 | INFO     | app.service.document.document_processor:process_document:132 | 向量化开始
2025-07-22 17:09:48 | INFO     | app.database.qdrant_manager:store_vectors:85 | 开始插入 9 个向量，批次大小: 100
2025-07-22 17:09:48 | INFO     | app.database.qdrant_manager:store_vectors:94 | 成功插入批次，大小: 9
2025-07-22 17:09:48 | INFO     | app.database.qdrant_manager:store_vectors:102 | 向量插入完成，总耗时: 0.06s
2025-07-22 17:09:48 | INFO     | app.service.document.document_processor:process_document:136 | 摘要生成开始
2025-07-22 17:09:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:09:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:09:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:09:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:09:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:09:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:09:55 | ERROR    | app.service.model_client:generate_summary:202 | 摘要生成失败: <!DOCTYPE html>
<html>
<head>
<title>Error</title>
<style>
html { color-scheme: light dark; }
body { width: 35em; margin: 0 auto;
font-family: Tahoma, Verdana, Arial, sans-serif; }
</style>
</head>
<body>
<h1>An error occurred.</h1>
<p>Sorry, the page you are looking for is currently unavailable.<br/>
Please try again later.</p>
<p>If you are the system administrator of this resource then you should check
the error log for details.</p>
<p><em>Faithfully yours, nginx.</em></p>
</body>
</html>
2025-07-22 17:09:55 | ERROR    | app.utils.exceptions:wrapper:85 | generate_summary RAG系统异常: 摘要生成失败: <!DOCTYPE html>
<html>
<head>
<title>Error</title>
<style>
html { color-scheme: light dark; }
body { width: 35em; margin: 0 auto;
font-family: Tahoma, Verdana, Arial, sans-serif; }
</style>
</head>
<body>
<h1>An error occurred.</h1>
<p>Sorry, the page you are looking for is currently unavailable.<br/>
Please try again later.</p>
<p>If you are the system administrator of this resource then you should check
the error log for details.</p>
<p><em>Faithfully yours, nginx.</em></p>
</body>
</html>
2025-07-22 17:09:55 | ERROR    | app.service.document.document_processor:process_document:146 | 文档处理失败 5ee5b529f7790fd92dbf99b6c59659cc: 摘要生成失败: <!DOCTYPE html>
<html>
<head>
<title>Error</title>
<style>
html { color-scheme: light dark; }
body { width: 35em; margin: 0 auto;
font-family: Tahoma, Verdana, Arial, sans-serif; }
</style>
</head>
<body>
<h1>An error occurred.</h1>
<p>Sorry, the page you are looking for is currently unavailable.<br/>
Please try again later.</p>
<p>If you are the system administrator of this resource then you should check
the error log for details.</p>
<p><em>Faithfully yours, nginx.</em></p>
</body>
</html>
2025-07-22 17:09:55 | ERROR    | app.service.document.async_parse_queue:_complete_task:173 | 任务处理失败: 5d98abf9-ab10-43d1-b9d8-e85dac318934, 错误: None
2025-07-22 17:09:57 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:09:57 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:09:57 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:09:57 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:09:58 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:09:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:09:59 | INFO     | app.service.document.document_processor:process_document:132 | 向量化开始
2025-07-22 17:09:59 | INFO     | app.database.qdrant_manager:store_vectors:85 | 开始插入 32 个向量，批次大小: 100
2025-07-22 17:09:59 | INFO     | app.database.qdrant_manager:store_vectors:94 | 成功插入批次，大小: 32
2025-07-22 17:09:59 | INFO     | app.database.qdrant_manager:store_vectors:102 | 向量插入完成，总耗时: 0.17s
2025-07-22 17:09:59 | INFO     | app.service.document.document_processor:process_document:136 | 摘要生成开始
2025-07-22 17:10:07 | ERROR    | app.service.model_client:generate_summary:202 | 摘要生成失败: <!DOCTYPE html>
<html>
<head>
<title>Error</title>
<style>
html { color-scheme: light dark; }
body { width: 35em; margin: 0 auto;
font-family: Tahoma, Verdana, Arial, sans-serif; }
</style>
</head>
<body>
<h1>An error occurred.</h1>
<p>Sorry, the page you are looking for is currently unavailable.<br/>
Please try again later.</p>
<p>If you are the system administrator of this resource then you should check
the error log for details.</p>
<p><em>Faithfully yours, nginx.</em></p>
</body>
</html>
2025-07-22 17:10:07 | ERROR    | app.utils.exceptions:wrapper:85 | generate_summary RAG系统异常: 摘要生成失败: <!DOCTYPE html>
<html>
<head>
<title>Error</title>
<style>
html { color-scheme: light dark; }
body { width: 35em; margin: 0 auto;
font-family: Tahoma, Verdana, Arial, sans-serif; }
</style>
</head>
<body>
<h1>An error occurred.</h1>
<p>Sorry, the page you are looking for is currently unavailable.<br/>
Please try again later.</p>
<p>If you are the system administrator of this resource then you should check
the error log for details.</p>
<p><em>Faithfully yours, nginx.</em></p>
</body>
</html>
2025-07-22 17:10:07 | ERROR    | app.service.document.document_processor:process_document:146 | 文档处理失败 d673f1bec5d7572105ca8dc80da42e26: 摘要生成失败: <!DOCTYPE html>
<html>
<head>
<title>Error</title>
<style>
html { color-scheme: light dark; }
body { width: 35em; margin: 0 auto;
font-family: Tahoma, Verdana, Arial, sans-serif; }
</style>
</head>
<body>
<h1>An error occurred.</h1>
<p>Sorry, the page you are looking for is currently unavailable.<br/>
Please try again later.</p>
<p>If you are the system administrator of this resource then you should check
the error log for details.</p>
<p><em>Faithfully yours, nginx.</em></p>
</body>
</html>
2025-07-22 17:10:07 | ERROR    | app.service.document.async_parse_queue:_complete_task:173 | 任务处理失败: 4ca98f5a-d8d3-4798-9ab7-057037b3c5dc, 错误: None
2025-07-22 17:12:54 | INFO     | __main__:lifespan:40 | RAG系统关闭中...
2025-07-22 17:12:54 | INFO     | app.service.document.async_parse_queue:_worker_loop:119 | 工作进程 worker_1 被取消
2025-07-22 17:12:54 | INFO     | app.service.document.async_parse_queue:_worker_loop:125 | 工作进程 worker_1 已停止
2025-07-22 17:12:54 | INFO     | app.service.document.async_parse_queue:_worker_loop:119 | 工作进程 worker_0 被取消
2025-07-22 17:12:54 | INFO     | app.service.document.async_parse_queue:_worker_loop:125 | 工作进程 worker_0 已停止
2025-07-22 17:12:54 | INFO     | app.service.document.async_parse_queue:_worker_loop:119 | 工作进程 worker_2 被取消
2025-07-22 17:12:54 | INFO     | app.service.document.async_parse_queue:_worker_loop:125 | 工作进程 worker_2 已停止
2025-07-22 17:13:05 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 17:13:09 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 17:13:09 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 17:13:09 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 17:13:09 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 17:13:09 | INFO     | __main__:lifespan:25 | RAG系统启动中...
2025-07-22 17:13:09 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 17:13:09 | INFO     | __main__:lifespan:29 | PostgreSQL数据库连接成功
2025-07-22 17:13:09 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 17:13:11 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 17:13:11 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 17:13:11 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 17:13:11 | INFO     | app.service.document.document_processor:initialize:97 | 文档处理器初始化完成
2025-07-22 17:13:11 | INFO     | app.service.document.async_parse_queue:initialize:49 | AsyncDocumentQueue 初始化完成
2025-07-22 17:13:11 | INFO     | app.service.document.async_parse_queue:initialize:49 | AsyncDocumentQueue 初始化完成
2025-07-22 17:13:11 | INFO     | app.service.document.async_parse_queue:start_workers:95 | 启动了 3 个工作进程
2025-07-22 17:13:11 | INFO     | app.service.document.parse_document:initialize_document_queue:238 | 文档处理队列已启动
2025-07-22 17:13:11 | INFO     | app.service.document.async_parse_queue:_worker_loop:99 | 工作进程 worker_0 开始运行
2025-07-22 17:13:11 | INFO     | app.service.document.async_parse_queue:_worker_loop:99 | 工作进程 worker_1 开始运行
2025-07-22 17:13:11 | INFO     | app.service.document.async_parse_queue:_worker_loop:99 | 工作进程 worker_2 开始运行
2025-07-22 17:13:15 | WARNING  | app.service.document.parse_document:parse_document_service:215 | 文件 d673f1bec5d7572105ca8dc80da42e26 正在处理中
2025-07-22 17:13:15 | WARNING  | app.service.document.parse_document:parse_document_service:215 | 文件 d3ffacb717d9296e625fde86b5178741 正在处理中
2025-07-22 17:13:15 | WARNING  | app.service.document.parse_document:parse_document_service:215 | 文件 17224950eff21079a7dc90f5478c0338 正在处理中
2025-07-22 17:13:15 | WARNING  | app.service.document.parse_document:parse_document_service:215 | 文件 5ee5b529f7790fd92dbf99b6c59659cc 正在处理中
2025-07-22 17:13:18 | WARNING  | app.service.document.parse_document:parse_document_service:215 | 文件 d673f1bec5d7572105ca8dc80da42e26 正在处理中
2025-07-22 17:13:18 | WARNING  | app.service.document.parse_document:parse_document_service:215 | 文件 d3ffacb717d9296e625fde86b5178741 正在处理中
2025-07-22 17:13:18 | WARNING  | app.service.document.parse_document:parse_document_service:215 | 文件 17224950eff21079a7dc90f5478c0338 正在处理中
2025-07-22 17:13:18 | WARNING  | app.service.document.parse_document:parse_document_service:215 | 文件 5ee5b529f7790fd92dbf99b6c59659cc 正在处理中
2025-07-22 17:15:07 | INFO     | __main__:lifespan:40 | RAG系统关闭中...
2025-07-22 17:15:07 | INFO     | app.service.document.async_parse_queue:_worker_loop:119 | 工作进程 worker_0 被取消
2025-07-22 17:15:07 | INFO     | app.service.document.async_parse_queue:_worker_loop:125 | 工作进程 worker_0 已停止
2025-07-22 17:15:07 | INFO     | app.service.document.async_parse_queue:_worker_loop:119 | 工作进程 worker_2 被取消
2025-07-22 17:15:07 | INFO     | app.service.document.async_parse_queue:_worker_loop:125 | 工作进程 worker_2 已停止
2025-07-22 17:15:07 | INFO     | app.service.document.async_parse_queue:_worker_loop:119 | 工作进程 worker_1 被取消
2025-07-22 17:15:07 | INFO     | app.service.document.async_parse_queue:_worker_loop:125 | 工作进程 worker_1 已停止
2025-07-22 17:15:18 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 17:15:22 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 17:15:22 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 17:15:22 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 17:15:22 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 17:15:23 | INFO     | __main__:lifespan:25 | RAG系统启动中...
2025-07-22 17:15:23 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 17:15:23 | INFO     | __main__:lifespan:29 | PostgreSQL数据库连接成功
2025-07-22 17:15:23 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 17:15:35 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 17:15:35 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 17:15:35 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 17:15:35 | INFO     | app.service.document.document_processor:initialize:97 | 文档处理器初始化完成
2025-07-22 17:15:35 | INFO     | app.service.document.async_parse_queue:initialize:49 | AsyncDocumentQueue 初始化完成
2025-07-22 17:15:35 | INFO     | app.service.document.async_parse_queue:initialize:49 | AsyncDocumentQueue 初始化完成
2025-07-22 17:15:35 | INFO     | app.service.document.async_parse_queue:start_workers:96 | 启动了 3 个工作进程
2025-07-22 17:15:35 | INFO     | app.service.document.parse_document:initialize_document_queue:238 | 文档处理队列已启动
2025-07-22 17:15:35 | INFO     | app.service.document.async_parse_queue:_worker_loop:100 | 工作进程 worker_0 开始运行
2025-07-22 17:15:35 | INFO     | app.service.document.async_parse_queue:_worker_loop:100 | 工作进程 worker_1 开始运行
2025-07-22 17:15:35 | INFO     | app.service.document.async_parse_queue:_worker_loop:100 | 工作进程 worker_2 开始运行
2025-07-22 17:15:48 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: d486a1e8-66b1-4377-ab6b-5c011d3fd3c2 (file_hash: d673f1bec5d7572105ca8dc80da42e26)
2025-07-22 17:15:48 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: d546f343-5d70-4789-bcf7-b425f07253c0 (file_hash: d3ffacb717d9296e625fde86b5178741)
2025-07-22 17:15:48 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: f8fa6b13-bcb4-40d5-b506-b33830c887ab (file_hash: 17224950eff21079a7dc90f5478c0338)
2025-07-22 17:15:48 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: ff0b38a0-8a77-4011-b604-a62cf82aaaed (file_hash: 5ee5b529f7790fd92dbf99b6c59659cc)
2025-07-22 17:15:51 | WARNING  | app.service.document.parse_document:parse_document_service:215 | 文件 d673f1bec5d7572105ca8dc80da42e26 正在处理中
2025-07-22 17:15:51 | WARNING  | app.service.document.parse_document:parse_document_service:215 | 文件 d3ffacb717d9296e625fde86b5178741 正在处理中
2025-07-22 17:15:51 | WARNING  | app.service.document.parse_document:parse_document_service:215 | 文件 17224950eff21079a7dc90f5478c0338 正在处理中
2025-07-22 17:15:51 | WARNING  | app.service.document.parse_document:parse_document_service:215 | 文件 5ee5b529f7790fd92dbf99b6c59659cc 正在处理中
2025-07-22 17:16:22 | INFO     | __main__:lifespan:40 | RAG系统关闭中...
2025-07-22 17:16:22 | INFO     | app.service.document.async_parse_queue:_worker_loop:120 | 工作进程 worker_1 被取消
2025-07-22 17:16:22 | INFO     | app.service.document.async_parse_queue:_worker_loop:126 | 工作进程 worker_1 已停止
2025-07-22 17:16:22 | INFO     | app.service.document.async_parse_queue:_worker_loop:120 | 工作进程 worker_0 被取消
2025-07-22 17:16:22 | INFO     | app.service.document.async_parse_queue:_worker_loop:126 | 工作进程 worker_0 已停止
2025-07-22 17:16:22 | INFO     | app.service.document.async_parse_queue:_worker_loop:120 | 工作进程 worker_2 被取消
2025-07-22 17:16:22 | INFO     | app.service.document.async_parse_queue:_worker_loop:126 | 工作进程 worker_2 已停止
2025-07-22 17:17:14 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 17:17:19 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 17:17:19 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 17:17:19 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 17:17:19 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 17:17:19 | INFO     | __main__:lifespan:25 | RAG系统启动中...
2025-07-22 17:17:19 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 17:17:19 | INFO     | __main__:lifespan:29 | PostgreSQL数据库连接成功
2025-07-22 17:17:19 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 17:17:21 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 17:17:21 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 17:17:21 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 17:17:22 | INFO     | app.service.document.document_processor:initialize:97 | 文档处理器初始化完成
2025-07-22 17:17:22 | INFO     | app.service.document.async_parse_queue:initialize:49 | AsyncDocumentQueue 初始化完成
2025-07-22 17:17:22 | INFO     | app.service.document.async_parse_queue:initialize:49 | AsyncDocumentQueue 初始化完成
2025-07-22 17:17:22 | INFO     | app.service.document.async_parse_queue:start_workers:96 | 启动了 3 个工作进程
2025-07-22 17:17:22 | INFO     | app.service.document.parse_document:initialize_document_queue:238 | 文档处理队列已启动
2025-07-22 17:17:22 | INFO     | app.service.document.async_parse_queue:_worker_loop:100 | 工作进程 worker_0 开始运行
2025-07-22 17:17:22 | INFO     | app.service.document.async_parse_queue:_worker_loop:100 | 工作进程 worker_1 开始运行
2025-07-22 17:17:22 | INFO     | app.service.document.async_parse_queue:_worker_loop:100 | 工作进程 worker_2 开始运行
2025-07-22 17:17:35 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: 3a5cf996-b517-4b46-9b2d-481ba9ac63c6 (file_hash: d673f1bec5d7572105ca8dc80da42e26)
2025-07-22 17:17:35 | INFO     | app.service.document.async_parse_queue:_process_task:144 | 工作进程 worker_1 开始处理任务: 3a5cf996-b517-4b46-9b2d-481ba9ac63c6
2025-07-22 17:17:35 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: c784a4ca-6355-4602-9e47-321fee63af31 (file_hash: d3ffacb717d9296e625fde86b5178741)
2025-07-22 17:17:35 | INFO     | app.service.document.async_parse_queue:_process_task:144 | 工作进程 worker_2 开始处理任务: c784a4ca-6355-4602-9e47-321fee63af31
2025-07-22 17:17:35 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: 727bc813-81f6-452a-a6aa-90c4b01c9878 (file_hash: 17224950eff21079a7dc90f5478c0338)
2025-07-22 17:17:35 | INFO     | app.service.document.async_parse_queue:_process_task:144 | 工作进程 worker_0 开始处理任务: 727bc813-81f6-452a-a6aa-90c4b01c9878
2025-07-22 17:17:35 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: cbf80f74-df51-4818-bf3c-f1b212c009b5 (file_hash: 5ee5b529f7790fd92dbf99b6c59659cc)
2025-07-22 17:17:35 | INFO     | app.service.document.document_processor:_parse_document:214 | 开始解析文件: d673f1bec5d7572105ca8dc80da42e26.docx
2025-07-22 17:17:39 | WARNING  | app.service.document.parse_document:parse_document_service:215 | 文件 d673f1bec5d7572105ca8dc80da42e26 正在处理中
2025-07-22 17:17:39 | WARNING  | app.service.document.parse_document:parse_document_service:215 | 文件 d3ffacb717d9296e625fde86b5178741 正在处理中
2025-07-22 17:17:39 | INFO     | app.service.document.document_processor:process_document:123 | 开始分块: d673f1bec5d7572105ca8dc80da42e26.docx
2025-07-22 17:27:19 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 17:27:23 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 17:27:23 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 17:27:23 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 17:27:23 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 17:27:23 | INFO     | __main__:lifespan:25 | RAG系统启动中...
2025-07-22 17:27:23 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 17:27:23 | INFO     | __main__:lifespan:29 | PostgreSQL数据库连接成功
2025-07-22 17:27:23 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 17:27:26 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 17:27:26 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 17:27:26 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 17:27:26 | INFO     | app.service.document.document_processor:initialize:97 | 文档处理器初始化完成
2025-07-22 17:27:26 | INFO     | app.service.document.async_parse_queue:initialize:50 | AsyncDocumentQueue 初始化完成
2025-07-22 17:27:26 | INFO     | app.service.document.async_parse_queue:initialize:50 | AsyncDocumentQueue 初始化完成
2025-07-22 17:27:26 | INFO     | app.service.document.async_parse_queue:start_workers:97 | 启动了 3 个工作进程
2025-07-22 17:27:26 | INFO     | app.service.document.parse_document:initialize_document_queue:238 | 文档处理队列已启动
2025-07-22 17:27:26 | INFO     | app.service.document.async_parse_queue:_worker_loop:101 | 工作进程 worker_0 开始运行
2025-07-22 17:27:26 | INFO     | app.service.document.async_parse_queue:_worker_loop:101 | 工作进程 worker_1 开始运行
2025-07-22 17:27:26 | INFO     | app.service.document.async_parse_queue:_worker_loop:101 | 工作进程 worker_2 开始运行
2025-07-22 17:27:33 | INFO     | app.service.intelligent_chat_service:_assess_knowledge_needs:182 | 知识需求评估: False
2025-07-22 17:27:33 | ERROR    | app.service.intelligent_chat_service:chat_stream:133 | 对话处理失败: 'full_content'
2025-07-22 17:28:24 | INFO     | __main__:lifespan:40 | RAG系统关闭中...
2025-07-22 17:28:24 | INFO     | app.service.document.async_parse_queue:_worker_loop:121 | 工作进程 worker_2 被取消
2025-07-22 17:28:24 | INFO     | app.service.document.async_parse_queue:_worker_loop:127 | 工作进程 worker_2 已停止
2025-07-22 17:28:24 | INFO     | app.service.document.async_parse_queue:_worker_loop:121 | 工作进程 worker_0 被取消
2025-07-22 17:28:24 | INFO     | app.service.document.async_parse_queue:_worker_loop:127 | 工作进程 worker_0 已停止
2025-07-22 17:28:24 | INFO     | app.service.document.async_parse_queue:_worker_loop:121 | 工作进程 worker_1 被取消
2025-07-22 17:28:24 | INFO     | app.service.document.async_parse_queue:_worker_loop:127 | 工作进程 worker_1 已停止
2025-07-22 17:28:36 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 17:28:40 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 17:28:40 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 17:28:40 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 17:28:40 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 17:28:40 | INFO     | __main__:lifespan:25 | RAG系统启动中...
2025-07-22 17:28:40 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 17:28:40 | INFO     | __main__:lifespan:29 | PostgreSQL数据库连接成功
2025-07-22 17:28:40 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 17:28:42 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 17:28:42 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 17:28:42 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 17:28:42 | INFO     | app.service.document.document_processor:initialize:97 | 文档处理器初始化完成
2025-07-22 17:28:42 | INFO     | app.service.document.async_parse_queue:initialize:50 | AsyncDocumentQueue 初始化完成
2025-07-22 17:28:42 | INFO     | app.service.document.async_parse_queue:initialize:50 | AsyncDocumentQueue 初始化完成
2025-07-22 17:28:42 | INFO     | app.service.document.async_parse_queue:start_workers:97 | 启动了 3 个工作进程
2025-07-22 17:28:42 | INFO     | app.service.document.parse_document:initialize_document_queue:238 | 文档处理队列已启动
2025-07-22 17:28:42 | INFO     | app.service.document.async_parse_queue:_worker_loop:101 | 工作进程 worker_0 开始运行
2025-07-22 17:28:42 | INFO     | app.service.document.async_parse_queue:_worker_loop:101 | 工作进程 worker_1 开始运行
2025-07-22 17:28:42 | INFO     | app.service.document.async_parse_queue:_worker_loop:101 | 工作进程 worker_2 开始运行
2025-07-22 17:28:48 | INFO     | app.service.intelligent_chat_service:_assess_knowledge_needs:182 | 知识需求评估: False
2025-07-22 17:31:59 | INFO     | app.service.intelligent_chat_service:_assess_knowledge_needs:182 | 知识需求评估: True
2025-07-22 17:32:02 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:32:02 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 17:32:02 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:32:02 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 5 个结果
2025-07-22 17:32:02 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:32:02 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 17:32:02 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:32:02 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 17:32:02 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:32:02 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 17:32:02 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:32:02 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 3 个结果
2025-07-22 17:32:02 | INFO     | app.service.intelligent_chat_service:_search_knowledge_base:212 | 检索完成，找到 6 个结果
2025-07-22 17:36:18 | INFO     | __main__:lifespan:40 | RAG系统关闭中...
2025-07-22 17:36:18 | INFO     | app.service.document.async_parse_queue:_worker_loop:121 | 工作进程 worker_0 被取消
2025-07-22 17:36:18 | INFO     | app.service.document.async_parse_queue:_worker_loop:127 | 工作进程 worker_0 已停止
2025-07-22 17:36:18 | INFO     | app.service.document.async_parse_queue:_worker_loop:121 | 工作进程 worker_1 被取消
2025-07-22 17:36:18 | INFO     | app.service.document.async_parse_queue:_worker_loop:127 | 工作进程 worker_1 已停止
2025-07-22 17:36:18 | INFO     | app.service.document.async_parse_queue:_worker_loop:121 | 工作进程 worker_2 被取消
2025-07-22 17:36:18 | INFO     | app.service.document.async_parse_queue:_worker_loop:127 | 工作进程 worker_2 已停止
2025-07-22 17:36:44 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 17:36:48 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 17:36:48 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 17:36:48 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 17:36:48 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 17:39:58 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 17:40:02 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 17:40:02 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 17:40:02 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 17:40:02 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 17:40:02 | INFO     | app.main:lifespan:25 | RAG系统启动中...
2025-07-22 17:40:02 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 17:40:02 | INFO     | app.main:lifespan:29 | PostgreSQL数据库连接成功
2025-07-22 17:40:02 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 17:40:05 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 17:40:05 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 17:40:05 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 17:40:06 | INFO     | app.service.document.document_processor:initialize:97 | 文档处理器初始化完成
2025-07-22 17:40:06 | INFO     | app.service.document.async_parse_queue:initialize:50 | AsyncDocumentQueue 初始化完成
2025-07-22 17:40:06 | INFO     | app.service.document.async_parse_queue:initialize:50 | AsyncDocumentQueue 初始化完成
2025-07-22 17:40:06 | INFO     | app.service.document.async_parse_queue:start_workers:97 | 启动了 3 个工作进程
2025-07-22 17:40:06 | INFO     | app.service.document.parse_document:initialize_document_queue:238 | 文档处理队列已启动
2025-07-22 17:40:06 | INFO     | app.service.document.async_parse_queue:_worker_loop:101 | 工作进程 worker_0 开始运行
2025-07-22 17:40:06 | INFO     | app.service.document.async_parse_queue:_worker_loop:101 | 工作进程 worker_1 开始运行
2025-07-22 17:40:06 | INFO     | app.service.document.async_parse_queue:_worker_loop:101 | 工作进程 worker_2 开始运行
2025-07-22 17:40:06 | INFO     | app.service.document.async_parse_queue:_worker_loop:121 | 工作进程 worker_1 被取消
2025-07-22 17:40:06 | INFO     | app.service.document.async_parse_queue:_worker_loop:127 | 工作进程 worker_1 已停止
2025-07-22 17:40:06 | INFO     | app.service.document.async_parse_queue:_worker_loop:121 | 工作进程 worker_2 被取消
2025-07-22 17:40:06 | INFO     | app.service.document.async_parse_queue:_worker_loop:127 | 工作进程 worker_2 已停止
2025-07-22 17:40:06 | INFO     | app.service.document.async_parse_queue:_worker_loop:121 | 工作进程 worker_0 被取消
2025-07-22 17:40:06 | INFO     | app.service.document.async_parse_queue:_worker_loop:127 | 工作进程 worker_0 已停止
2025-07-22 17:40:44 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 17:40:44 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 17:40:44 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 17:40:50 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 17:40:50 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 17:40:50 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 17:40:50 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 17:40:50 | INFO     | app.main:lifespan:25 | RAG系统启动中...
2025-07-22 17:40:50 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 17:40:50 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 17:40:50 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 17:40:50 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 17:40:50 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 17:40:50 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 17:40:50 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 17:40:50 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 17:40:50 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 17:40:50 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 17:40:50 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 17:40:50 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 17:40:50 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 17:40:50 | INFO     | app.main:lifespan:29 | PostgreSQL数据库连接成功
2025-07-22 17:40:50 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 17:40:50 | INFO     | app.main:lifespan:25 | RAG系统启动中...
2025-07-22 17:40:50 | INFO     | app.main:lifespan:25 | RAG系统启动中...
2025-07-22 17:40:50 | INFO     | app.main:lifespan:25 | RAG系统启动中...
2025-07-22 17:40:50 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 17:40:50 | INFO     | app.main:lifespan:29 | PostgreSQL数据库连接成功
2025-07-22 17:40:50 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 17:40:50 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 17:40:50 | INFO     | app.main:lifespan:29 | PostgreSQL数据库连接成功
2025-07-22 17:40:50 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 17:40:50 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 17:40:50 | INFO     | app.main:lifespan:29 | PostgreSQL数据库连接成功
2025-07-22 17:40:50 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 17:40:52 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 17:40:52 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 17:40:52 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 17:40:53 | INFO     | app.service.document.document_processor:initialize:97 | 文档处理器初始化完成
2025-07-22 17:40:53 | INFO     | app.service.document.async_parse_queue:initialize:50 | AsyncDocumentQueue 初始化完成
2025-07-22 17:40:53 | INFO     | app.service.document.async_parse_queue:initialize:50 | AsyncDocumentQueue 初始化完成
2025-07-22 17:40:53 | INFO     | app.service.document.async_parse_queue:start_workers:97 | 启动了 3 个工作进程
2025-07-22 17:40:53 | INFO     | app.service.document.parse_document:initialize_document_queue:238 | 文档处理队列已启动
2025-07-22 17:40:53 | INFO     | app.service.document.async_parse_queue:_worker_loop:101 | 工作进程 worker_0 开始运行
2025-07-22 17:40:53 | INFO     | app.service.document.async_parse_queue:_worker_loop:101 | 工作进程 worker_1 开始运行
2025-07-22 17:40:53 | INFO     | app.service.document.async_parse_queue:_worker_loop:101 | 工作进程 worker_2 开始运行
2025-07-22 17:40:53 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 17:40:53 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 17:40:53 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 17:40:53 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 17:40:53 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 17:40:53 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 17:40:53 | INFO     | app.service.document.document_processor:initialize:97 | 文档处理器初始化完成
2025-07-22 17:40:53 | INFO     | app.service.document.async_parse_queue:initialize:50 | AsyncDocumentQueue 初始化完成
2025-07-22 17:40:53 | INFO     | app.service.document.async_parse_queue:initialize:50 | AsyncDocumentQueue 初始化完成
2025-07-22 17:40:53 | INFO     | app.service.document.async_parse_queue:start_workers:97 | 启动了 3 个工作进程
2025-07-22 17:40:53 | INFO     | app.service.document.parse_document:initialize_document_queue:238 | 文档处理队列已启动
2025-07-22 17:40:53 | INFO     | app.service.document.async_parse_queue:_worker_loop:101 | 工作进程 worker_0 开始运行
2025-07-22 17:40:53 | INFO     | app.service.document.async_parse_queue:_worker_loop:101 | 工作进程 worker_1 开始运行
2025-07-22 17:40:53 | INFO     | app.service.document.async_parse_queue:_worker_loop:101 | 工作进程 worker_2 开始运行
2025-07-22 17:40:53 | INFO     | app.service.document.async_parse_queue:_worker_loop:121 | 工作进程 worker_0 被取消
2025-07-22 17:40:53 | INFO     | app.service.document.async_parse_queue:_worker_loop:127 | 工作进程 worker_0 已停止
2025-07-22 17:40:53 | INFO     | app.service.document.async_parse_queue:_worker_loop:121 | 工作进程 worker_2 被取消
2025-07-22 17:40:53 | INFO     | app.service.document.async_parse_queue:_worker_loop:127 | 工作进程 worker_2 已停止
2025-07-22 17:40:53 | INFO     | app.service.document.async_parse_queue:_worker_loop:121 | 工作进程 worker_1 被取消
2025-07-22 17:40:53 | INFO     | app.service.document.async_parse_queue:_worker_loop:127 | 工作进程 worker_1 已停止
2025-07-22 17:40:54 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 17:40:54 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 17:40:54 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 17:40:54 | INFO     | app.service.document.document_processor:initialize:97 | 文档处理器初始化完成
2025-07-22 17:40:54 | INFO     | app.service.document.async_parse_queue:initialize:50 | AsyncDocumentQueue 初始化完成
2025-07-22 17:40:54 | INFO     | app.service.document.async_parse_queue:initialize:50 | AsyncDocumentQueue 初始化完成
2025-07-22 17:40:54 | INFO     | app.service.document.async_parse_queue:start_workers:97 | 启动了 3 个工作进程
2025-07-22 17:40:54 | INFO     | app.service.document.parse_document:initialize_document_queue:238 | 文档处理队列已启动
2025-07-22 17:40:54 | INFO     | app.service.document.async_parse_queue:_worker_loop:101 | 工作进程 worker_0 开始运行
2025-07-22 17:40:54 | INFO     | app.service.document.async_parse_queue:_worker_loop:101 | 工作进程 worker_1 开始运行
2025-07-22 17:40:54 | INFO     | app.service.document.async_parse_queue:_worker_loop:101 | 工作进程 worker_2 开始运行
2025-07-22 17:40:54 | INFO     | app.service.document.async_parse_queue:_worker_loop:121 | 工作进程 worker_1 被取消
2025-07-22 17:40:54 | INFO     | app.service.document.async_parse_queue:_worker_loop:127 | 工作进程 worker_1 已停止
2025-07-22 17:40:54 | INFO     | app.service.document.async_parse_queue:_worker_loop:121 | 工作进程 worker_2 被取消
2025-07-22 17:40:54 | INFO     | app.service.document.async_parse_queue:_worker_loop:127 | 工作进程 worker_2 已停止
2025-07-22 17:40:54 | INFO     | app.service.document.async_parse_queue:_worker_loop:121 | 工作进程 worker_0 被取消
2025-07-22 17:40:54 | INFO     | app.service.document.async_parse_queue:_worker_loop:127 | 工作进程 worker_0 已停止
2025-07-22 17:40:54 | INFO     | app.service.document.document_processor:initialize:97 | 文档处理器初始化完成
2025-07-22 17:40:54 | INFO     | app.service.document.async_parse_queue:initialize:50 | AsyncDocumentQueue 初始化完成
2025-07-22 17:40:54 | INFO     | app.service.document.async_parse_queue:initialize:50 | AsyncDocumentQueue 初始化完成
2025-07-22 17:40:54 | INFO     | app.service.document.async_parse_queue:start_workers:97 | 启动了 3 个工作进程
2025-07-22 17:40:54 | INFO     | app.service.document.parse_document:initialize_document_queue:238 | 文档处理队列已启动
2025-07-22 17:40:54 | INFO     | app.service.document.async_parse_queue:_worker_loop:101 | 工作进程 worker_0 开始运行
2025-07-22 17:40:54 | INFO     | app.service.document.async_parse_queue:_worker_loop:101 | 工作进程 worker_1 开始运行
2025-07-22 17:40:54 | INFO     | app.service.document.async_parse_queue:_worker_loop:101 | 工作进程 worker_2 开始运行
2025-07-22 17:40:54 | INFO     | app.service.document.async_parse_queue:_worker_loop:121 | 工作进程 worker_0 被取消
2025-07-22 17:40:54 | INFO     | app.service.document.async_parse_queue:_worker_loop:127 | 工作进程 worker_0 已停止
2025-07-22 17:40:54 | INFO     | app.service.document.async_parse_queue:_worker_loop:121 | 工作进程 worker_2 被取消
2025-07-22 17:40:54 | INFO     | app.service.document.async_parse_queue:_worker_loop:127 | 工作进程 worker_2 已停止
2025-07-22 17:40:54 | INFO     | app.service.document.async_parse_queue:_worker_loop:121 | 工作进程 worker_1 被取消
2025-07-22 17:40:54 | INFO     | app.service.document.async_parse_queue:_worker_loop:127 | 工作进程 worker_1 已停止
2025-07-22 17:41:08 | INFO     | app.main:lifespan:40 | RAG系统关闭中...
2025-07-22 17:41:08 | INFO     | app.service.document.async_parse_queue:_worker_loop:121 | 工作进程 worker_1 被取消
2025-07-22 17:41:08 | INFO     | app.service.document.async_parse_queue:_worker_loop:127 | 工作进程 worker_1 已停止
2025-07-22 17:41:08 | INFO     | app.service.document.async_parse_queue:_worker_loop:121 | 工作进程 worker_0 被取消
2025-07-22 17:41:08 | INFO     | app.service.document.async_parse_queue:_worker_loop:127 | 工作进程 worker_0 已停止
2025-07-22 17:41:08 | INFO     | app.service.document.async_parse_queue:_worker_loop:121 | 工作进程 worker_2 被取消
2025-07-22 17:41:08 | INFO     | app.service.document.async_parse_queue:_worker_loop:127 | 工作进程 worker_2 已停止
2025-07-22 17:41:10 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 17:41:10 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 17:41:10 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 17:41:15 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 17:41:15 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 17:41:15 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 17:41:15 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 17:41:15 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 17:41:15 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 17:41:15 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 17:41:15 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 17:41:15 | INFO     | app.main:lifespan:25 | RAG系统启动中...
2025-07-22 17:41:16 | INFO     | app.main:lifespan:25 | RAG系统启动中...
2025-07-22 17:41:16 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 17:41:16 | INFO     | app.main:lifespan:29 | PostgreSQL数据库连接成功
2025-07-22 17:41:16 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 17:41:16 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 17:41:16 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 17:41:16 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 17:41:16 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 17:41:16 | INFO     | app.main:lifespan:29 | PostgreSQL数据库连接成功
2025-07-22 17:41:16 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 17:41:16 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 17:41:16 | INFO     | app.main:lifespan:25 | RAG系统启动中...
2025-07-22 17:41:16 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 17:41:16 | INFO     | app.main:lifespan:29 | PostgreSQL数据库连接成功
2025-07-22 17:41:16 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 17:41:46 | ERROR    | app.service.model_client:check_health:97 | 模型服务健康检查失败: 
2025-07-22 17:41:46 | ERROR    | app.utils.exceptions:wrapper:85 | check_health RAG系统异常: 模型服务健康检查失败: 
2025-07-22 17:41:46 | ERROR    | app.main:lifespan:37 | 系统初始化失败: 模型服务健康检查失败: 
2025-07-22 17:41:46 | ERROR    | app.service.model_client:check_health:97 | 模型服务健康检查失败: 
2025-07-22 17:41:46 | ERROR    | app.utils.exceptions:wrapper:85 | check_health RAG系统异常: 模型服务健康检查失败: 
2025-07-22 17:41:46 | ERROR    | app.main:lifespan:37 | 系统初始化失败: 模型服务健康检查失败: 
2025-07-22 17:41:46 | ERROR    | app.service.model_client:check_health:97 | 模型服务健康检查失败: 
2025-07-22 17:41:46 | ERROR    | app.utils.exceptions:wrapper:85 | check_health RAG系统异常: 模型服务健康检查失败: 
2025-07-22 17:41:46 | ERROR    | app.main:lifespan:37 | 系统初始化失败: 模型服务健康检查失败: 
2025-07-22 17:49:53 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 17:49:58 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 17:49:58 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 17:49:58 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 17:49:58 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 17:49:58 | INFO     | __main__:lifespan:25 | RAG系统启动中...
2025-07-22 17:49:58 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 17:49:58 | INFO     | __main__:lifespan:29 | PostgreSQL数据库连接成功
2025-07-22 17:49:58 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 17:50:05 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 17:50:05 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 17:50:05 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 17:50:06 | INFO     | app.service.document.document_processor:initialize:97 | 文档处理器初始化完成
2025-07-22 17:50:06 | INFO     | app.service.document.async_parse_queue:initialize:50 | AsyncDocumentQueue 初始化完成
2025-07-22 17:50:06 | INFO     | app.service.document.async_parse_queue:initialize:50 | AsyncDocumentQueue 初始化完成
2025-07-22 17:50:06 | INFO     | app.service.document.async_parse_queue:start_workers:97 | 启动了 3 个工作进程
2025-07-22 17:50:06 | INFO     | app.service.document.parse_document:initialize_document_queue:238 | 文档处理队列已启动
2025-07-22 17:50:06 | INFO     | app.service.document.async_parse_queue:_worker_loop:101 | 工作进程 worker_0 开始运行
2025-07-22 17:50:06 | INFO     | app.service.document.async_parse_queue:_worker_loop:101 | 工作进程 worker_1 开始运行
2025-07-22 17:50:06 | INFO     | app.service.document.async_parse_queue:_worker_loop:101 | 工作进程 worker_2 开始运行
2025-07-22 17:50:10 | INFO     | app.service.document.async_parse_queue:add_task:72 | 任务已加入队列: 0f88d2ce-9971-477d-8dde-b659b90eec4d (file_hash: d673f1bec5d7572105ca8dc80da42e26)
2025-07-22 17:50:10 | INFO     | app.service.document.async_parse_queue:_process_task:145 | 工作进程 worker_0 开始处理任务: 0f88d2ce-9971-477d-8dde-b659b90eec4d
2025-07-22 17:50:10 | INFO     | app.service.document.async_parse_queue:add_task:72 | 任务已加入队列: 2f5c7eb3-38d7-4b1c-9087-d9a0ae7f1a79 (file_hash: d3ffacb717d9296e625fde86b5178741)
2025-07-22 17:50:10 | INFO     | app.service.document.async_parse_queue:_process_task:145 | 工作进程 worker_1 开始处理任务: 2f5c7eb3-38d7-4b1c-9087-d9a0ae7f1a79
2025-07-22 17:50:10 | INFO     | app.service.document.async_parse_queue:add_task:72 | 任务已加入队列: 6c97a82d-e667-4a3c-a628-544666de9321 (file_hash: 17224950eff21079a7dc90f5478c0338)
2025-07-22 17:50:10 | INFO     | app.service.document.async_parse_queue:_process_task:145 | 工作进程 worker_2 开始处理任务: 6c97a82d-e667-4a3c-a628-544666de9321
2025-07-22 17:50:10 | INFO     | app.service.document.async_parse_queue:add_task:72 | 任务已加入队列: 9f6017e4-4f8a-4c72-954b-051c307b8036 (file_hash: 5ee5b529f7790fd92dbf99b6c59659cc)
2025-07-22 17:50:10 | INFO     | app.service.document.document_processor:_parse_document:214 | 开始解析文件: d673f1bec5d7572105ca8dc80da42e26.docx
2025-07-22 17:50:15 | INFO     | app.service.document.async_parse_queue:add_task:72 | 任务已加入队列: 1f464109-c2b5-4f38-84d8-dfee8082048b (file_hash: 279e472ff8f1e2672398020a32630604)
2025-07-22 17:50:15 | INFO     | app.service.document.document_processor:process_document:123 | 开始分块: d673f1bec5d7572105ca8dc80da42e26.docx
2025-07-22 17:50:45 | INFO     | app.service.document.async_parse_queue:add_task:72 | 任务已加入队列: 1cc66f8f-1686-4b92-878c-4209f3c67b72 (file_hash: ac58b2194caad5efd4a573a10912d4a2)
2025-07-22 17:50:45 | INFO     | app.service.document.document_processor:process_document:127 | 共有32 片
2025-07-22 17:50:45 | INFO     | app.service.document.document_processor:_parse_document:214 | 开始解析文件: d3ffacb717d9296e625fde86b5178741.pdf
2025-07-22 17:51:41 | INFO     | app.service.document.document_processor:_parse_document:214 | 开始解析文件: 17224950eff21079a7dc90f5478c0338.pdf
2025-07-22 17:52:05 | INFO     | app.service.document.document_processor:process_document:123 | 开始分块: d3ffacb717d9296e625fde86b5178741.pdf
2025-07-22 17:52:05 | INFO     | app.service.document.async_parse_queue:add_task:72 | 任务已加入队列: 0b5a5a54-54c1-47b9-91ea-3a495286b3be (file_hash: 2d6cc1c63af8ef20abf6177fca49e097)
2025-07-22 17:52:05 | INFO     | app.service.document.document_processor:process_document:127 | 共有2 片
2025-07-22 17:52:06 | INFO     | app.service.document.document_processor:process_document:123 | 开始分块: 17224950eff21079a7dc90f5478c0338.pdf
2025-07-22 17:52:06 | INFO     | app.service.document.async_parse_queue:add_task:72 | 任务已加入队列: 56b11321-c540-4476-99a5-b2c177764491 (file_hash: 4f0e3d3f79ca4f09321c61ce679af449)
2025-07-22 17:52:06 | INFO     | app.service.document.document_processor:process_document:127 | 共有6 片
2025-07-22 17:52:06 | INFO     | app.service.document.async_parse_queue:add_task:72 | 任务已加入队列: 7841103d-39d0-4f61-999c-3a0498c3d273 (file_hash: 12134a1bc2e7a7906cf3b09afbb066ca)
2025-07-22 17:52:06 | INFO     | app.service.document.async_parse_queue:add_task:72 | 任务已加入队列: cf7a6b59-bdff-4c54-9877-66bfdc9dca69 (file_hash: 0f2e292a160aa2820ea3c077dc668d80)
2025-07-22 17:52:07 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:52:13 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:52:14 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:52:14 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:52:14 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:52:14 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:52:14 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:52:14 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:52:14 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:52:14 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:52:14 | INFO     | app.service.document.document_processor:process_document:132 | 向量化开始
2025-07-22 17:52:14 | INFO     | app.database.qdrant_manager:store_vectors:85 | 开始插入 2 个向量，批次大小: 100
2025-07-22 17:52:14 | INFO     | app.database.qdrant_manager:store_vectors:94 | 成功插入批次，大小: 2
2025-07-22 17:52:14 | INFO     | app.database.qdrant_manager:store_vectors:102 | 向量插入完成，总耗时: 0.06s
2025-07-22 17:52:14 | INFO     | app.service.document.document_processor:process_document:136 | 摘要生成开始
2025-07-22 17:52:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:52:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:52:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:52:50 | INFO     | app.service.document.document_processor:process_document:132 | 向量化开始
2025-07-22 17:52:50 | INFO     | app.database.qdrant_manager:store_vectors:85 | 开始插入 6 个向量，批次大小: 100
2025-07-22 17:52:50 | INFO     | app.database.qdrant_manager:store_vectors:94 | 成功插入批次，大小: 6
2025-07-22 17:52:50 | INFO     | app.database.qdrant_manager:store_vectors:102 | 向量插入完成，总耗时: 0.04s
2025-07-22 17:52:50 | INFO     | app.service.document.document_processor:process_document:136 | 摘要生成开始
2025-07-22 17:53:55 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:53:55 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:53:55 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:53:55 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:53:55 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:54:01 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 874
2025-07-22 17:54:01 | INFO     | app.service.document.document_processor:_finalize_processing:244 | 摘要生成完成: d3ffacb717d9296e625fde86b5178741.pdf
2025-07-22 17:54:01 | INFO     | app.service.document.document_processor:process_document:142 | 文档处理完成: d3ffacb717d9296e625fde86b5178741.pdf
2025-07-22 17:54:01 | INFO     | app.service.document.async_parse_queue:_complete_task:173 | 任务处理成功: 2f5c7eb3-38d7-4b1c-9087-d9a0ae7f1a79
2025-07-22 17:54:01 | INFO     | app.service.document.async_parse_queue:_process_task:145 | 工作进程 worker_1 开始处理任务: 9f6017e4-4f8a-4c72-954b-051c307b8036
2025-07-22 17:54:01 | INFO     | app.service.document.document_processor:_parse_document:214 | 开始解析文件: 5ee5b529f7790fd92dbf99b6c59659cc.pdf
2025-07-22 17:54:26 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:54:26 | INFO     | app.service.document.document_processor:process_document:123 | 开始分块: 5ee5b529f7790fd92dbf99b6c59659cc.pdf
2025-07-22 17:54:26 | INFO     | app.service.document.document_processor:process_document:127 | 共有9 片
2025-07-22 17:54:38 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 3534
2025-07-22 17:54:38 | INFO     | app.service.document.document_processor:_finalize_processing:244 | 摘要生成完成: 17224950eff21079a7dc90f5478c0338.pdf
2025-07-22 17:54:38 | INFO     | app.service.document.document_processor:process_document:142 | 文档处理完成: 17224950eff21079a7dc90f5478c0338.pdf
2025-07-22 17:54:38 | INFO     | app.service.document.async_parse_queue:_complete_task:173 | 任务处理成功: 6c97a82d-e667-4a3c-a628-544666de9321
2025-07-22 17:54:38 | INFO     | app.service.document.async_parse_queue:_process_task:145 | 工作进程 worker_2 开始处理任务: 1f464109-c2b5-4f38-84d8-dfee8082048b
2025-07-22 17:54:39 | INFO     | app.service.document.document_processor:_parse_document:214 | 开始解析文件: 279e472ff8f1e2672398020a32630604.pdf
2025-07-22 17:54:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:54:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:54:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:54:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:54:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:54:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:54:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:54:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:54:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:54:47 | INFO     | app.service.document.document_processor:process_document:123 | 开始分块: 279e472ff8f1e2672398020a32630604.pdf
2025-07-22 17:54:47 | INFO     | app.service.document.document_processor:process_document:127 | 共有1 片
2025-07-22 17:54:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:54:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:54:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:54:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:54:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:54:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:54:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:54:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:54:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:54:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:54:53 | INFO     | app.service.document.document_processor:process_document:132 | 向量化开始
2025-07-22 17:54:53 | INFO     | app.database.qdrant_manager:store_vectors:85 | 开始插入 1 个向量，批次大小: 100
2025-07-22 17:54:53 | INFO     | app.database.qdrant_manager:store_vectors:94 | 成功插入批次，大小: 1
2025-07-22 17:54:53 | INFO     | app.database.qdrant_manager:store_vectors:102 | 向量插入完成，总耗时: 0.05s
2025-07-22 17:54:53 | INFO     | app.service.document.document_processor:process_document:132 | 向量化开始
2025-07-22 17:54:53 | INFO     | app.database.qdrant_manager:store_vectors:85 | 开始插入 9 个向量，批次大小: 100
2025-07-22 17:54:53 | INFO     | app.database.qdrant_manager:store_vectors:94 | 成功插入批次，大小: 9
2025-07-22 17:54:53 | INFO     | app.database.qdrant_manager:store_vectors:102 | 向量插入完成，总耗时: 0.08s
2025-07-22 17:54:53 | INFO     | app.service.document.document_processor:process_document:136 | 摘要生成开始
2025-07-22 17:54:54 | INFO     | app.service.document.document_processor:process_document:136 | 摘要生成开始
2025-07-22 17:55:13 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:55:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:55:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:55:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:55:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:56:07 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:56:33 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 1050
2025-07-22 17:56:33 | INFO     | app.service.document.document_processor:_finalize_processing:244 | 摘要生成完成: 279e472ff8f1e2672398020a32630604.pdf
2025-07-22 17:56:33 | INFO     | app.service.document.document_processor:process_document:142 | 文档处理完成: 279e472ff8f1e2672398020a32630604.pdf
2025-07-22 17:56:33 | INFO     | app.service.document.async_parse_queue:_complete_task:173 | 任务处理成功: 1f464109-c2b5-4f38-84d8-dfee8082048b
2025-07-22 17:56:33 | INFO     | app.service.document.async_parse_queue:_process_task:145 | 工作进程 worker_2 开始处理任务: 1cc66f8f-1686-4b92-878c-4209f3c67b72
2025-07-22 17:56:33 | INFO     | app.service.document.document_processor:_parse_document:214 | 开始解析文件: ac58b2194caad5efd4a573a10912d4a2.docx
2025-07-22 17:56:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:56:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:56:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:56:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-22 17:56:44 | INFO     | app.service.document.document_processor:process_document:123 | 开始分块: ac58b2194caad5efd4a573a10912d4a2.docx
2025-07-22 18:00:23 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 3248
2025-07-22 18:00:23 | INFO     | app.service.document.document_processor:process_document:127 | 共有43 片
2025-07-22 18:00:23 | INFO     | app.service.document.document_processor:_finalize_processing:244 | 摘要生成完成: 5ee5b529f7790fd92dbf99b6c59659cc.pdf
2025-07-22 18:00:23 | INFO     | app.service.document.document_processor:process_document:142 | 文档处理完成: 5ee5b529f7790fd92dbf99b6c59659cc.pdf
2025-07-22 18:00:23 | INFO     | app.service.document.async_parse_queue:_complete_task:173 | 任务处理成功: 9f6017e4-4f8a-4c72-954b-051c307b8036
2025-07-22 18:00:23 | INFO     | app.service.document.async_parse_queue:_process_task:145 | 工作进程 worker_1 开始处理任务: 0b5a5a54-54c1-47b9-91ea-3a495286b3be
2025-07-22 18:00:23 | INFO     | app.service.document.document_processor:_parse_document:214 | 开始解析文件: 2d6cc1c63af8ef20abf6177fca49e097.pdf
2025-07-22 18:06:18 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 18:06:23 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 18:06:23 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 18:06:23 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 18:06:23 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 18:06:23 | INFO     | __main__:lifespan:25 | RAG系统启动中...
2025-07-22 18:06:23 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 18:06:23 | INFO     | __main__:lifespan:29 | PostgreSQL数据库连接成功
2025-07-22 18:06:23 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 18:06:29 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 18:06:29 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 18:06:29 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 18:06:29 | INFO     | app.service.document.document_processor:initialize:97 | 文档处理器初始化完成
2025-07-22 18:06:29 | INFO     | app.service.document.async_parse_queue:initialize:49 | AsyncDocumentQueue 初始化完成
2025-07-22 18:06:29 | INFO     | app.service.document.async_parse_queue:start_workers:96 | 启动了 3 个工作进程
2025-07-22 18:06:29 | INFO     | app.service.document.parse_document:initialize_document_queue:238 | 文档处理队列已启动
2025-07-22 18:06:29 | INFO     | app.service.document.async_parse_queue:_worker_loop:100 | 工作进程 worker_0 开始运行
2025-07-22 18:06:29 | INFO     | app.service.document.async_parse_queue:_worker_loop:100 | 工作进程 worker_1 开始运行
2025-07-22 18:06:29 | INFO     | app.service.document.async_parse_queue:_worker_loop:100 | 工作进程 worker_2 开始运行
2025-07-22 18:06:38 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: c9ca901e-3a78-4653-97af-e923785ea040 (file_hash: d673f1bec5d7572105ca8dc80da42e26)
2025-07-22 18:06:38 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: 83a012fe-eab2-4c52-8fe7-c154113ce525 (file_hash: d3ffacb717d9296e625fde86b5178741)
2025-07-22 18:06:38 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: e96b3d8f-094d-40cb-994f-6e41fd0d8a14 (file_hash: 17224950eff21079a7dc90f5478c0338)
2025-07-22 18:06:38 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: eb180eb2-6997-4d60-85b0-98723d48c25c (file_hash: 5ee5b529f7790fd92dbf99b6c59659cc)
2025-07-22 18:06:38 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: 77566553-6074-4bb8-b9cf-484b2886b38a (file_hash: 279e472ff8f1e2672398020a32630604)
2025-07-22 18:06:38 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: 5add474c-cd70-4222-aa42-8a6c0a41a000 (file_hash: ac58b2194caad5efd4a573a10912d4a2)
2025-07-22 18:06:38 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: 4a8488ad-1e16-4bd0-a04c-2b455da87d07 (file_hash: 2d6cc1c63af8ef20abf6177fca49e097)
2025-07-22 18:06:38 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: b17f9aaf-e527-499f-9426-68275639f077 (file_hash: 4f0e3d3f79ca4f09321c61ce679af449)
2025-07-22 18:06:38 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: 42147e3e-98c3-477f-afa3-0c9fcc5c7dce (file_hash: 12134a1bc2e7a7906cf3b09afbb066ca)
2025-07-22 18:06:38 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: 63c50a48-0304-485b-a293-62e2d7328207 (file_hash: 0f2e292a160aa2820ea3c077dc668d80)
2025-07-22 18:06:42 | WARNING  | app.service.document.parse_document:parse_document_service:215 | 文件 d673f1bec5d7572105ca8dc80da42e26 正在处理中
2025-07-22 18:06:42 | WARNING  | app.service.document.parse_document:parse_document_service:215 | 文件 d3ffacb717d9296e625fde86b5178741 正在处理中
2025-07-22 18:06:42 | WARNING  | app.service.document.parse_document:parse_document_service:215 | 文件 17224950eff21079a7dc90f5478c0338 正在处理中
2025-07-22 18:06:42 | WARNING  | app.service.document.parse_document:parse_document_service:215 | 文件 5ee5b529f7790fd92dbf99b6c59659cc 正在处理中
2025-07-22 18:06:42 | WARNING  | app.service.document.parse_document:parse_document_service:215 | 文件 279e472ff8f1e2672398020a32630604 正在处理中
2025-07-22 18:06:42 | WARNING  | app.service.document.parse_document:parse_document_service:215 | 文件 ac58b2194caad5efd4a573a10912d4a2 正在处理中
2025-07-22 18:06:42 | WARNING  | app.service.document.parse_document:parse_document_service:215 | 文件 2d6cc1c63af8ef20abf6177fca49e097 正在处理中
2025-07-22 18:06:42 | WARNING  | app.service.document.parse_document:parse_document_service:215 | 文件 4f0e3d3f79ca4f09321c61ce679af449 正在处理中
2025-07-22 18:06:42 | WARNING  | app.service.document.parse_document:parse_document_service:215 | 文件 12134a1bc2e7a7906cf3b09afbb066ca 正在处理中
2025-07-22 18:06:42 | WARNING  | app.service.document.parse_document:parse_document_service:215 | 文件 0f2e292a160aa2820ea3c077dc668d80 正在处理中
2025-07-22 18:07:57 | INFO     | __main__:lifespan:40 | RAG系统关闭中...
2025-07-22 18:07:57 | INFO     | app.service.document.async_parse_queue:_worker_loop:120 | 工作进程 worker_1 被取消
2025-07-22 18:07:57 | INFO     | app.service.document.async_parse_queue:_worker_loop:126 | 工作进程 worker_1 已停止
2025-07-22 18:07:57 | INFO     | app.service.document.async_parse_queue:_worker_loop:120 | 工作进程 worker_0 被取消
2025-07-22 18:07:57 | INFO     | app.service.document.async_parse_queue:_worker_loop:126 | 工作进程 worker_0 已停止
2025-07-22 18:07:57 | INFO     | app.service.document.async_parse_queue:_worker_loop:120 | 工作进程 worker_2 被取消
2025-07-22 18:07:57 | INFO     | app.service.document.async_parse_queue:_worker_loop:126 | 工作进程 worker_2 已停止
2025-07-22 18:08:09 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 18:08:14 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 18:08:14 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 18:08:14 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 18:08:14 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 18:08:14 | INFO     | __main__:lifespan:25 | RAG系统启动中...
2025-07-22 18:08:15 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 18:08:15 | INFO     | __main__:lifespan:29 | PostgreSQL数据库连接成功
2025-07-22 18:08:15 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 18:08:18 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 18:08:18 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 18:08:18 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 18:08:18 | INFO     | app.service.document.document_processor:initialize:97 | 文档处理器初始化完成
2025-07-22 18:08:18 | INFO     | app.service.document.async_parse_queue:initialize:49 | AsyncDocumentQueue 初始化完成
2025-07-22 18:08:18 | INFO     | app.service.document.async_parse_queue:start_workers:96 | 启动了 3 个工作进程
2025-07-22 18:08:18 | INFO     | app.service.document.parse_document:initialize_document_queue:238 | 文档处理队列已启动
2025-07-22 18:08:18 | INFO     | app.service.document.async_parse_queue:_worker_loop:100 | 工作进程 worker_0 开始运行
2025-07-22 18:08:18 | INFO     | app.service.document.async_parse_queue:_worker_loop:100 | 工作进程 worker_1 开始运行
2025-07-22 18:08:18 | INFO     | app.service.document.async_parse_queue:_worker_loop:100 | 工作进程 worker_2 开始运行
2025-07-22 18:08:25 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: 61be85fc-49f0-4814-ac75-744affd9b72b (file_hash: d673f1bec5d7572105ca8dc80da42e26)
2025-07-22 18:08:25 | INFO     | app.service.document.async_parse_queue:_process_task:144 | 工作进程 worker_1 开始处理任务: 61be85fc-49f0-4814-ac75-744affd9b72b
2025-07-22 18:08:25 | INFO     | app.service.document.async_parse_queue:_complete_task:178 | 任务处理成功: 61be85fc-49f0-4814-ac75-744affd9b72b
2025-07-22 18:08:25 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: 56cabb40-33d1-447c-b53a-3db4937ecae8 (file_hash: d3ffacb717d9296e625fde86b5178741)
2025-07-22 18:08:25 | INFO     | app.service.document.async_parse_queue:_process_task:144 | 工作进程 worker_0 开始处理任务: 56cabb40-33d1-447c-b53a-3db4937ecae8
2025-07-22 18:08:25 | INFO     | app.service.document.async_parse_queue:_complete_task:178 | 任务处理成功: 56cabb40-33d1-447c-b53a-3db4937ecae8
2025-07-22 18:08:25 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: d470d579-7af3-4945-8c70-3ece1f498594 (file_hash: 17224950eff21079a7dc90f5478c0338)
2025-07-22 18:08:25 | INFO     | app.service.document.async_parse_queue:_process_task:144 | 工作进程 worker_2 开始处理任务: d470d579-7af3-4945-8c70-3ece1f498594
2025-07-22 18:08:25 | INFO     | app.service.document.async_parse_queue:_complete_task:178 | 任务处理成功: d470d579-7af3-4945-8c70-3ece1f498594
2025-07-22 18:08:25 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: 9fd39fd1-7405-4cb1-87ea-21be5027cb2d (file_hash: 5ee5b529f7790fd92dbf99b6c59659cc)
2025-07-22 18:08:25 | INFO     | app.service.document.async_parse_queue:_process_task:144 | 工作进程 worker_1 开始处理任务: 9fd39fd1-7405-4cb1-87ea-21be5027cb2d
2025-07-22 18:08:25 | INFO     | app.service.document.async_parse_queue:_complete_task:178 | 任务处理成功: 9fd39fd1-7405-4cb1-87ea-21be5027cb2d
2025-07-22 18:08:25 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: b96be130-9d2f-42bf-9265-f11cce8f3102 (file_hash: 279e472ff8f1e2672398020a32630604)
2025-07-22 18:08:25 | INFO     | app.service.document.async_parse_queue:_process_task:144 | 工作进程 worker_0 开始处理任务: b96be130-9d2f-42bf-9265-f11cce8f3102
2025-07-22 18:08:25 | INFO     | app.service.document.async_parse_queue:_complete_task:178 | 任务处理成功: b96be130-9d2f-42bf-9265-f11cce8f3102
2025-07-22 18:08:25 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: 3cfa0b17-45b6-41f9-9c81-d77d0433a133 (file_hash: ac58b2194caad5efd4a573a10912d4a2)
2025-07-22 18:08:25 | INFO     | app.service.document.async_parse_queue:_process_task:144 | 工作进程 worker_2 开始处理任务: 3cfa0b17-45b6-41f9-9c81-d77d0433a133
2025-07-22 18:08:25 | INFO     | app.service.document.async_parse_queue:_complete_task:178 | 任务处理成功: 3cfa0b17-45b6-41f9-9c81-d77d0433a133
2025-07-22 18:08:25 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: c256260c-58c1-4853-88b3-d4bd85a65eca (file_hash: 2d6cc1c63af8ef20abf6177fca49e097)
2025-07-22 18:08:25 | INFO     | app.service.document.async_parse_queue:_process_task:144 | 工作进程 worker_1 开始处理任务: c256260c-58c1-4853-88b3-d4bd85a65eca
2025-07-22 18:08:25 | INFO     | app.service.document.async_parse_queue:_complete_task:178 | 任务处理成功: c256260c-58c1-4853-88b3-d4bd85a65eca
2025-07-22 18:08:25 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: 5e8b0ccb-3e7f-4d4d-87ad-374b84c2b8a0 (file_hash: 4f0e3d3f79ca4f09321c61ce679af449)
2025-07-22 18:08:25 | INFO     | app.service.document.async_parse_queue:_process_task:144 | 工作进程 worker_0 开始处理任务: 5e8b0ccb-3e7f-4d4d-87ad-374b84c2b8a0
2025-07-22 18:08:25 | INFO     | app.service.document.async_parse_queue:_complete_task:178 | 任务处理成功: 5e8b0ccb-3e7f-4d4d-87ad-374b84c2b8a0
2025-07-22 18:08:25 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: b394f981-699c-4ab3-961b-2858d8ab5781 (file_hash: 12134a1bc2e7a7906cf3b09afbb066ca)
2025-07-22 18:08:25 | INFO     | app.service.document.async_parse_queue:_process_task:144 | 工作进程 worker_2 开始处理任务: b394f981-699c-4ab3-961b-2858d8ab5781
2025-07-22 18:08:25 | INFO     | app.service.document.async_parse_queue:_complete_task:178 | 任务处理成功: b394f981-699c-4ab3-961b-2858d8ab5781
2025-07-22 18:08:25 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: 220e363b-6138-4646-a5c8-42a942c04f00 (file_hash: 0f2e292a160aa2820ea3c077dc668d80)
2025-07-22 18:08:25 | INFO     | app.service.document.async_parse_queue:_process_task:144 | 工作进程 worker_1 开始处理任务: 220e363b-6138-4646-a5c8-42a942c04f00
2025-07-22 18:08:25 | INFO     | app.service.document.async_parse_queue:_complete_task:178 | 任务处理成功: 220e363b-6138-4646-a5c8-42a942c04f00
2025-07-22 18:08:27 | WARNING  | app.service.document.parse_document:parse_document_service:215 | 文件 d673f1bec5d7572105ca8dc80da42e26 正在处理中
2025-07-22 18:08:27 | WARNING  | app.service.document.parse_document:parse_document_service:215 | 文件 d3ffacb717d9296e625fde86b5178741 正在处理中
2025-07-22 18:08:27 | WARNING  | app.service.document.parse_document:parse_document_service:215 | 文件 17224950eff21079a7dc90f5478c0338 正在处理中
2025-07-22 18:08:27 | WARNING  | app.service.document.parse_document:parse_document_service:215 | 文件 5ee5b529f7790fd92dbf99b6c59659cc 正在处理中
2025-07-22 18:08:27 | WARNING  | app.service.document.parse_document:parse_document_service:215 | 文件 279e472ff8f1e2672398020a32630604 正在处理中
2025-07-22 18:08:27 | WARNING  | app.service.document.parse_document:parse_document_service:215 | 文件 ac58b2194caad5efd4a573a10912d4a2 正在处理中
2025-07-22 18:08:27 | WARNING  | app.service.document.parse_document:parse_document_service:215 | 文件 2d6cc1c63af8ef20abf6177fca49e097 正在处理中
2025-07-22 18:08:27 | WARNING  | app.service.document.parse_document:parse_document_service:215 | 文件 4f0e3d3f79ca4f09321c61ce679af449 正在处理中
2025-07-22 18:08:27 | WARNING  | app.service.document.parse_document:parse_document_service:215 | 文件 12134a1bc2e7a7906cf3b09afbb066ca 正在处理中
2025-07-22 18:08:27 | WARNING  | app.service.document.parse_document:parse_document_service:215 | 文件 0f2e292a160aa2820ea3c077dc668d80 正在处理中
2025-07-22 18:11:37 | INFO     | __main__:lifespan:40 | RAG系统关闭中...
2025-07-22 18:11:37 | INFO     | app.service.document.async_parse_queue:_worker_loop:120 | 工作进程 worker_0 被取消
2025-07-22 18:11:37 | INFO     | app.service.document.async_parse_queue:_worker_loop:126 | 工作进程 worker_0 已停止
2025-07-22 18:11:37 | INFO     | app.service.document.async_parse_queue:_worker_loop:120 | 工作进程 worker_1 被取消
2025-07-22 18:11:37 | INFO     | app.service.document.async_parse_queue:_worker_loop:126 | 工作进程 worker_1 已停止
2025-07-22 18:11:37 | INFO     | app.service.document.async_parse_queue:_worker_loop:120 | 工作进程 worker_2 被取消
2025-07-22 18:11:37 | INFO     | app.service.document.async_parse_queue:_worker_loop:126 | 工作进程 worker_2 已停止
2025-07-22 18:11:48 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 18:11:52 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 18:11:52 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 18:11:52 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 18:11:52 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 18:11:52 | INFO     | __main__:lifespan:25 | RAG系统启动中...
2025-07-22 18:11:52 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 18:11:52 | INFO     | __main__:lifespan:29 | PostgreSQL数据库连接成功
2025-07-22 18:11:52 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 18:12:00 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 18:12:00 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 18:12:00 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 18:12:01 | INFO     | app.service.document.document_processor:initialize:97 | 文档处理器初始化完成
2025-07-22 18:12:01 | INFO     | app.service.document.async_parse_queue:initialize:49 | AsyncDocumentQueue 初始化完成
2025-07-22 18:12:01 | INFO     | app.service.document.async_parse_queue:start_workers:96 | 启动了 3 个工作进程
2025-07-22 18:12:01 | INFO     | app.service.document.parse_document:initialize_document_queue:238 | 文档处理队列已启动
2025-07-22 18:12:01 | INFO     | app.service.document.async_parse_queue:_worker_loop:100 | 工作进程 worker_0 开始运行
2025-07-22 18:12:01 | INFO     | app.service.document.async_parse_queue:_worker_loop:100 | 工作进程 worker_1 开始运行
2025-07-22 18:12:01 | INFO     | app.service.document.async_parse_queue:_worker_loop:100 | 工作进程 worker_2 开始运行
2025-07-22 18:12:06 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: c6a4b870-98fc-4b36-a942-51fb78f00b39 (file_hash: d673f1bec5d7572105ca8dc80da42e26)
2025-07-22 18:12:06 | INFO     | app.service.document.async_parse_queue:_process_task:144 | 工作进程 worker_2 开始处理任务: c6a4b870-98fc-4b36-a942-51fb78f00b39
2025-07-22 18:12:06 | ERROR    | app.service.document.document_processor:process_document:146 | 文档处理失败 d673f1bec5d7572105ca8dc80da42e26: Task <Task pending name='Task-13' coro=<DocumentProcessor.process_document() running at D:\项目\星图\知识库\app\service\document\document_processor.py:104> cb=[_run_until_complete_cb() at D:\miniconda3\envs\zhishiku\Lib\asyncio\base_events.py:181]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at D:\miniconda3\envs\zhishiku\Lib\asyncio\futures.py:389]> attached to a different loop
2025-07-22 18:12:06 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: 6fc52900-ad08-48e9-8948-41dba20578f9 (file_hash: d3ffacb717d9296e625fde86b5178741)
2025-07-22 18:12:06 | INFO     | app.service.document.async_parse_queue:_process_task:144 | 工作进程 worker_1 开始处理任务: 6fc52900-ad08-48e9-8948-41dba20578f9
2025-07-22 18:12:06 | ERROR    | app.service.document.async_parse_queue:_process_task:158 | 任务执行异常 c6a4b870-98fc-4b36-a942-51fb78f00b39: Task <Task pending name='Task-13' coro=<DocumentProcessor.process_document() running at D:\项目\星图\知识库\app\service\document\document_processor.py:147> cb=[_run_until_complete_cb() at D:\miniconda3\envs\zhishiku\Lib\asyncio\base_events.py:181]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at D:\miniconda3\envs\zhishiku\Lib\asyncio\futures.py:389]> attached to a different loop
2025-07-22 18:12:06 | ERROR    | app.service.document.async_parse_queue:_complete_task:196 | 任务处理失败: c6a4b870-98fc-4b36-a942-51fb78f00b39, 错误: Task <Task pending name='Task-13' coro=<DocumentProcessor.process_document() running at D:\项目\星图\知识库\app\service\document\document_processor.py:147> cb=[_run_until_complete_cb() at D:\miniconda3\envs\zhishiku\Lib\asyncio\base_events.py:181]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at D:\miniconda3\envs\zhishiku\Lib\asyncio\futures.py:389]> attached to a different loop
2025-07-22 18:12:06 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: 93dd0675-ab59-4b74-a200-c30e21b96eaf (file_hash: 17224950eff21079a7dc90f5478c0338)
2025-07-22 18:12:06 | ERROR    | app.service.document.document_processor:process_document:146 | 文档处理失败 d3ffacb717d9296e625fde86b5178741: Task <Task pending name='Task-17' coro=<DocumentProcessor.process_document() running at D:\项目\星图\知识库\app\service\document\document_processor.py:104> cb=[_run_until_complete_cb() at D:\miniconda3\envs\zhishiku\Lib\asyncio\base_events.py:181]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at D:\miniconda3\envs\zhishiku\Lib\asyncio\futures.py:389]> attached to a different loop
2025-07-22 18:12:06 | INFO     | app.service.document.async_parse_queue:_process_task:144 | 工作进程 worker_0 开始处理任务: 93dd0675-ab59-4b74-a200-c30e21b96eaf
2025-07-22 18:12:06 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: 3a5ed022-99bf-4fc9-9cce-849932e47513 (file_hash: 5ee5b529f7790fd92dbf99b6c59659cc)
2025-07-22 18:12:06 | INFO     | app.service.document.async_parse_queue:_process_task:144 | 工作进程 worker_2 开始处理任务: 3a5ed022-99bf-4fc9-9cce-849932e47513
2025-07-22 18:12:06 | ERROR    | app.service.document.async_parse_queue:_process_task:158 | 任务执行异常 6fc52900-ad08-48e9-8948-41dba20578f9: Task <Task pending name='Task-17' coro=<DocumentProcessor.process_document() running at D:\项目\星图\知识库\app\service\document\document_processor.py:147> cb=[_run_until_complete_cb() at D:\miniconda3\envs\zhishiku\Lib\asyncio\base_events.py:181]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at D:\miniconda3\envs\zhishiku\Lib\asyncio\futures.py:389]> attached to a different loop
2025-07-22 18:12:06 | ERROR    | app.service.document.async_parse_queue:_complete_task:196 | 任务处理失败: 6fc52900-ad08-48e9-8948-41dba20578f9, 错误: Task <Task pending name='Task-17' coro=<DocumentProcessor.process_document() running at D:\项目\星图\知识库\app\service\document\document_processor.py:147> cb=[_run_until_complete_cb() at D:\miniconda3\envs\zhishiku\Lib\asyncio\base_events.py:181]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at D:\miniconda3\envs\zhishiku\Lib\asyncio\futures.py:389]> attached to a different loop
2025-07-22 18:12:06 | ERROR    | app.service.document.document_processor:process_document:146 | 文档处理失败 17224950eff21079a7dc90f5478c0338: Task <Task pending name='Task-20' coro=<DocumentProcessor.process_document() running at D:\项目\星图\知识库\app\service\document\document_processor.py:104> cb=[_run_until_complete_cb() at D:\miniconda3\envs\zhishiku\Lib\asyncio\base_events.py:181]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at D:\miniconda3\envs\zhishiku\Lib\asyncio\futures.py:389]> attached to a different loop
2025-07-22 18:12:06 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: 62cf1cda-35af-4c7d-a64b-d5056d3c8dd9 (file_hash: 279e472ff8f1e2672398020a32630604)
2025-07-22 18:12:06 | INFO     | app.service.document.async_parse_queue:_process_task:144 | 工作进程 worker_1 开始处理任务: 62cf1cda-35af-4c7d-a64b-d5056d3c8dd9
2025-07-22 18:12:06 | ERROR    | app.service.document.async_parse_queue:_process_task:158 | 任务执行异常 93dd0675-ab59-4b74-a200-c30e21b96eaf: Task <Task pending name='Task-20' coro=<DocumentProcessor.process_document() running at D:\项目\星图\知识库\app\service\document\document_processor.py:147> cb=[_run_until_complete_cb() at D:\miniconda3\envs\zhishiku\Lib\asyncio\base_events.py:181]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at D:\miniconda3\envs\zhishiku\Lib\asyncio\futures.py:389]> attached to a different loop
2025-07-22 18:12:06 | ERROR    | app.service.document.document_processor:process_document:146 | 文档处理失败 5ee5b529f7790fd92dbf99b6c59659cc: Task <Task pending name='Task-23' coro=<DocumentProcessor.process_document() running at D:\项目\星图\知识库\app\service\document\document_processor.py:104> cb=[_run_until_complete_cb() at D:\miniconda3\envs\zhishiku\Lib\asyncio\base_events.py:181]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at D:\miniconda3\envs\zhishiku\Lib\asyncio\futures.py:389]> attached to a different loop
2025-07-22 18:12:06 | ERROR    | app.service.document.async_parse_queue:_complete_task:196 | 任务处理失败: 93dd0675-ab59-4b74-a200-c30e21b96eaf, 错误: Task <Task pending name='Task-20' coro=<DocumentProcessor.process_document() running at D:\项目\星图\知识库\app\service\document\document_processor.py:147> cb=[_run_until_complete_cb() at D:\miniconda3\envs\zhishiku\Lib\asyncio\base_events.py:181]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at D:\miniconda3\envs\zhishiku\Lib\asyncio\futures.py:389]> attached to a different loop
2025-07-22 18:12:06 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: 15d8e8ec-6a36-4689-82d7-062d2597f366 (file_hash: ac58b2194caad5efd4a573a10912d4a2)
2025-07-22 18:12:06 | INFO     | app.service.document.async_parse_queue:_process_task:144 | 工作进程 worker_0 开始处理任务: 15d8e8ec-6a36-4689-82d7-062d2597f366
2025-07-22 18:12:06 | ERROR    | app.service.document.document_processor:process_document:146 | 文档处理失败 279e472ff8f1e2672398020a32630604: Task <Task pending name='Task-26' coro=<DocumentProcessor.process_document() running at D:\项目\星图\知识库\app\service\document\document_processor.py:104> cb=[_run_until_complete_cb() at D:\miniconda3\envs\zhishiku\Lib\asyncio\base_events.py:181]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at D:\miniconda3\envs\zhishiku\Lib\asyncio\futures.py:389]> attached to a different loop
2025-07-22 18:12:06 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: 276e66ca-891c-455f-acf4-726fe897e07e (file_hash: 2d6cc1c63af8ef20abf6177fca49e097)
2025-07-22 18:12:06 | ERROR    | app.service.document.document_processor:process_document:146 | 文档处理失败 ac58b2194caad5efd4a573a10912d4a2: Task <Task pending name='Task-29' coro=<DocumentProcessor.process_document() running at D:\项目\星图\知识库\app\service\document\document_processor.py:104> cb=[_run_until_complete_cb() at D:\miniconda3\envs\zhishiku\Lib\asyncio\base_events.py:181]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at D:\miniconda3\envs\zhishiku\Lib\asyncio\futures.py:389]> attached to a different loop
2025-07-22 18:12:06 | ERROR    | app.service.document.async_parse_queue:_process_task:158 | 任务执行异常 3a5ed022-99bf-4fc9-9cce-849932e47513: Task <Task pending name='Task-23' coro=<DocumentProcessor.process_document() running at D:\项目\星图\知识库\app\service\document\document_processor.py:147> cb=[_run_until_complete_cb() at D:\miniconda3\envs\zhishiku\Lib\asyncio\base_events.py:181]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at D:\miniconda3\envs\zhishiku\Lib\asyncio\futures.py:389]> attached to a different loop
2025-07-22 18:12:06 | ERROR    | app.service.document.async_parse_queue:_complete_task:196 | 任务处理失败: 3a5ed022-99bf-4fc9-9cce-849932e47513, 错误: Task <Task pending name='Task-23' coro=<DocumentProcessor.process_document() running at D:\项目\星图\知识库\app\service\document\document_processor.py:147> cb=[_run_until_complete_cb() at D:\miniconda3\envs\zhishiku\Lib\asyncio\base_events.py:181]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at D:\miniconda3\envs\zhishiku\Lib\asyncio\futures.py:389]> attached to a different loop
2025-07-22 18:12:06 | INFO     | app.service.document.async_parse_queue:_process_task:144 | 工作进程 worker_2 开始处理任务: 276e66ca-891c-455f-acf4-726fe897e07e
2025-07-22 18:12:06 | ERROR    | app.service.document.async_parse_queue:_process_task:158 | 任务执行异常 62cf1cda-35af-4c7d-a64b-d5056d3c8dd9: Task <Task pending name='Task-26' coro=<DocumentProcessor.process_document() running at D:\项目\星图\知识库\app\service\document\document_processor.py:147> cb=[_run_until_complete_cb() at D:\miniconda3\envs\zhishiku\Lib\asyncio\base_events.py:181]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at D:\miniconda3\envs\zhishiku\Lib\asyncio\futures.py:389]> attached to a different loop
2025-07-22 18:12:06 | ERROR    | app.service.document.async_parse_queue:_complete_task:196 | 任务处理失败: 62cf1cda-35af-4c7d-a64b-d5056d3c8dd9, 错误: Task <Task pending name='Task-26' coro=<DocumentProcessor.process_document() running at D:\项目\星图\知识库\app\service\document\document_processor.py:147> cb=[_run_until_complete_cb() at D:\miniconda3\envs\zhishiku\Lib\asyncio\base_events.py:181]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at D:\miniconda3\envs\zhishiku\Lib\asyncio\futures.py:389]> attached to a different loop
2025-07-22 18:12:06 | ERROR    | app.service.document.async_parse_queue:_process_task:158 | 任务执行异常 15d8e8ec-6a36-4689-82d7-062d2597f366: Task <Task pending name='Task-29' coro=<DocumentProcessor.process_document() running at D:\项目\星图\知识库\app\service\document\document_processor.py:147> cb=[_run_until_complete_cb() at D:\miniconda3\envs\zhishiku\Lib\asyncio\base_events.py:181]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at D:\miniconda3\envs\zhishiku\Lib\asyncio\futures.py:389]> attached to a different loop
2025-07-22 18:12:06 | ERROR    | app.service.document.async_parse_queue:_complete_task:196 | 任务处理失败: 15d8e8ec-6a36-4689-82d7-062d2597f366, 错误: Task <Task pending name='Task-29' coro=<DocumentProcessor.process_document() running at D:\项目\星图\知识库\app\service\document\document_processor.py:147> cb=[_run_until_complete_cb() at D:\miniconda3\envs\zhishiku\Lib\asyncio\base_events.py:181]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at D:\miniconda3\envs\zhishiku\Lib\asyncio\futures.py:389]> attached to a different loop
2025-07-22 18:12:06 | ERROR    | app.service.document.document_processor:process_document:146 | 文档处理失败 2d6cc1c63af8ef20abf6177fca49e097: Task <Task pending name='Task-31' coro=<DocumentProcessor.process_document() running at D:\项目\星图\知识库\app\service\document\document_processor.py:104> cb=[_run_until_complete_cb() at D:\miniconda3\envs\zhishiku\Lib\asyncio\base_events.py:181]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at D:\miniconda3\envs\zhishiku\Lib\asyncio\futures.py:389]> attached to a different loop
2025-07-22 18:12:06 | ERROR    | app.service.document.async_parse_queue:_process_task:158 | 任务执行异常 276e66ca-891c-455f-acf4-726fe897e07e: Task <Task pending name='Task-31' coro=<DocumentProcessor.process_document() running at D:\项目\星图\知识库\app\service\document\document_processor.py:147> cb=[_run_until_complete_cb() at D:\miniconda3\envs\zhishiku\Lib\asyncio\base_events.py:181]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at D:\miniconda3\envs\zhishiku\Lib\asyncio\futures.py:389]> attached to a different loop
2025-07-22 18:12:06 | ERROR    | app.service.document.async_parse_queue:_complete_task:196 | 任务处理失败: 276e66ca-891c-455f-acf4-726fe897e07e, 错误: Task <Task pending name='Task-31' coro=<DocumentProcessor.process_document() running at D:\项目\星图\知识库\app\service\document\document_processor.py:147> cb=[_run_until_complete_cb() at D:\miniconda3\envs\zhishiku\Lib\asyncio\base_events.py:181]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at D:\miniconda3\envs\zhishiku\Lib\asyncio\futures.py:389]> attached to a different loop
2025-07-22 18:12:06 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: 1c5c64f6-4dbf-41e7-b73d-cb9418419dd2 (file_hash: 4f0e3d3f79ca4f09321c61ce679af449)
2025-07-22 18:12:06 | INFO     | app.service.document.async_parse_queue:_process_task:144 | 工作进程 worker_1 开始处理任务: 1c5c64f6-4dbf-41e7-b73d-cb9418419dd2
2025-07-22 18:12:06 | ERROR    | app.service.document.document_processor:process_document:146 | 文档处理失败 4f0e3d3f79ca4f09321c61ce679af449: Task <Task pending name='Task-34' coro=<DocumentProcessor.process_document() running at D:\项目\星图\知识库\app\service\document\document_processor.py:104> cb=[_run_until_complete_cb() at D:\miniconda3\envs\zhishiku\Lib\asyncio\base_events.py:181]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at D:\miniconda3\envs\zhishiku\Lib\asyncio\futures.py:389]> attached to a different loop
2025-07-22 18:12:06 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: 100fc195-d2c4-4e83-a501-3da6f3d387f9 (file_hash: 12134a1bc2e7a7906cf3b09afbb066ca)
2025-07-22 18:12:06 | INFO     | app.service.document.async_parse_queue:_process_task:144 | 工作进程 worker_0 开始处理任务: 100fc195-d2c4-4e83-a501-3da6f3d387f9
2025-07-22 18:12:06 | ERROR    | app.service.document.async_parse_queue:_process_task:158 | 任务执行异常 1c5c64f6-4dbf-41e7-b73d-cb9418419dd2: cannot perform operation: another operation is in progress
2025-07-22 18:12:06 | ERROR    | app.service.document.async_parse_queue:_complete_task:196 | 任务处理失败: 1c5c64f6-4dbf-41e7-b73d-cb9418419dd2, 错误: cannot perform operation: another operation is in progress
2025-07-22 18:12:06 | ERROR    | app.service.document.document_processor:process_document:146 | 文档处理失败 12134a1bc2e7a7906cf3b09afbb066ca: Task <Task pending name='Task-38' coro=<DocumentProcessor.process_document() running at D:\项目\星图\知识库\app\service\document\document_processor.py:104> cb=[_run_until_complete_cb() at D:\miniconda3\envs\zhishiku\Lib\asyncio\base_events.py:181]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at D:\miniconda3\envs\zhishiku\Lib\asyncio\futures.py:389]> attached to a different loop
2025-07-22 18:12:06 | ERROR    | app.service.document.async_parse_queue:_process_task:158 | 任务执行异常 100fc195-d2c4-4e83-a501-3da6f3d387f9: Task <Task pending name='Task-38' coro=<DocumentProcessor.process_document() running at D:\项目\星图\知识库\app\service\document\document_processor.py:147> cb=[_run_until_complete_cb() at D:\miniconda3\envs\zhishiku\Lib\asyncio\base_events.py:181]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at D:\miniconda3\envs\zhishiku\Lib\asyncio\futures.py:389]> attached to a different loop
2025-07-22 18:12:06 | ERROR    | app.service.document.async_parse_queue:_complete_task:196 | 任务处理失败: 100fc195-d2c4-4e83-a501-3da6f3d387f9, 错误: Task <Task pending name='Task-38' coro=<DocumentProcessor.process_document() running at D:\项目\星图\知识库\app\service\document\document_processor.py:147> cb=[_run_until_complete_cb() at D:\miniconda3\envs\zhishiku\Lib\asyncio\base_events.py:181]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at D:\miniconda3\envs\zhishiku\Lib\asyncio\futures.py:389]> attached to a different loop
2025-07-22 18:12:06 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: 2708dc4c-3b06-437c-903b-67a59d472300 (file_hash: 0f2e292a160aa2820ea3c077dc668d80)
2025-07-22 18:12:06 | INFO     | app.service.document.async_parse_queue:_process_task:144 | 工作进程 worker_2 开始处理任务: 2708dc4c-3b06-437c-903b-67a59d472300
2025-07-22 18:12:06 | ERROR    | app.service.document.document_processor:process_document:146 | 文档处理失败 0f2e292a160aa2820ea3c077dc668d80: Task <Task pending name='Task-41' coro=<DocumentProcessor.process_document() running at D:\项目\星图\知识库\app\service\document\document_processor.py:104> cb=[_run_until_complete_cb() at D:\miniconda3\envs\zhishiku\Lib\asyncio\base_events.py:181]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at D:\miniconda3\envs\zhishiku\Lib\asyncio\futures.py:389]> attached to a different loop
2025-07-22 18:12:06 | ERROR    | app.service.document.async_parse_queue:_process_task:158 | 任务执行异常 2708dc4c-3b06-437c-903b-67a59d472300: Task <Task pending name='Task-41' coro=<DocumentProcessor.process_document() running at D:\项目\星图\知识库\app\service\document\document_processor.py:147> cb=[_run_until_complete_cb() at D:\miniconda3\envs\zhishiku\Lib\asyncio\base_events.py:181]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at D:\miniconda3\envs\zhishiku\Lib\asyncio\futures.py:389]> attached to a different loop
2025-07-22 18:12:06 | ERROR    | app.service.document.async_parse_queue:_complete_task:196 | 任务处理失败: 2708dc4c-3b06-437c-903b-67a59d472300, 错误: Task <Task pending name='Task-41' coro=<DocumentProcessor.process_document() running at D:\项目\星图\知识库\app\service\document\document_processor.py:147> cb=[_run_until_complete_cb() at D:\miniconda3\envs\zhishiku\Lib\asyncio\base_events.py:181]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at D:\miniconda3\envs\zhishiku\Lib\asyncio\futures.py:389]> attached to a different loop
2025-07-22 18:13:09 | INFO     | __main__:lifespan:40 | RAG系统关闭中...
2025-07-22 18:13:09 | INFO     | app.service.document.async_parse_queue:_worker_loop:120 | 工作进程 worker_0 被取消
2025-07-22 18:13:09 | INFO     | app.service.document.async_parse_queue:_worker_loop:126 | 工作进程 worker_0 已停止
2025-07-22 18:13:09 | INFO     | app.service.document.async_parse_queue:_worker_loop:120 | 工作进程 worker_1 被取消
2025-07-22 18:13:09 | INFO     | app.service.document.async_parse_queue:_worker_loop:126 | 工作进程 worker_1 已停止
2025-07-22 18:13:09 | INFO     | app.service.document.async_parse_queue:_worker_loop:120 | 工作进程 worker_2 被取消
2025-07-22 18:13:09 | INFO     | app.service.document.async_parse_queue:_worker_loop:126 | 工作进程 worker_2 已停止
2025-07-22 18:13:20 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 18:13:24 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 18:13:24 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 18:13:24 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 18:13:24 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 18:13:24 | INFO     | __main__:lifespan:25 | RAG系统启动中...
2025-07-22 18:13:24 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 18:13:24 | INFO     | __main__:lifespan:29 | PostgreSQL数据库连接成功
2025-07-22 18:13:24 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 18:13:34 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 18:13:34 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 18:13:34 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 18:13:34 | INFO     | app.service.document.document_processor:initialize:97 | 文档处理器初始化完成
2025-07-22 18:13:34 | INFO     | app.service.document.async_parse_queue:initialize:49 | AsyncDocumentQueue 初始化完成
2025-07-22 18:13:34 | INFO     | app.service.document.async_parse_queue:start_workers:96 | 启动了 3 个工作进程
2025-07-22 18:13:34 | INFO     | app.service.document.parse_document:initialize_document_queue:238 | 文档处理队列已启动
2025-07-22 18:13:34 | INFO     | app.service.document.async_parse_queue:_worker_loop:100 | 工作进程 worker_0 开始运行
2025-07-22 18:13:34 | INFO     | app.service.document.async_parse_queue:_worker_loop:100 | 工作进程 worker_1 开始运行
2025-07-22 18:13:34 | INFO     | app.service.document.async_parse_queue:_worker_loop:100 | 工作进程 worker_2 开始运行
2025-07-22 18:13:39 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: ac8a2405-4f9d-48a6-b8fa-879acf1a13be (file_hash: d673f1bec5d7572105ca8dc80da42e26)
2025-07-22 18:13:39 | INFO     | app.service.document.async_parse_queue:_process_task:144 | 工作进程 worker_0 开始处理任务: ac8a2405-4f9d-48a6-b8fa-879acf1a13be
2025-07-22 18:13:39 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: f3523d8f-17e2-4b48-8792-5127fdde01fa (file_hash: d3ffacb717d9296e625fde86b5178741)
2025-07-22 18:13:39 | INFO     | app.service.document.async_parse_queue:_process_task:144 | 工作进程 worker_1 开始处理任务: f3523d8f-17e2-4b48-8792-5127fdde01fa
2025-07-22 18:13:40 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: 6ac03a6a-83f6-4142-b111-6c83c0d89631 (file_hash: 17224950eff21079a7dc90f5478c0338)
2025-07-22 18:13:40 | INFO     | app.service.document.async_parse_queue:_process_task:144 | 工作进程 worker_2 开始处理任务: 6ac03a6a-83f6-4142-b111-6c83c0d89631
2025-07-22 18:13:40 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: 109920b4-9e54-4c50-a8e3-5b44b3ea7481 (file_hash: 5ee5b529f7790fd92dbf99b6c59659cc)
2025-07-22 18:13:40 | INFO     | app.service.document.document_processor:_parse_document:214 | 开始解析文件: d673f1bec5d7572105ca8dc80da42e26.docx
2025-07-22 18:13:44 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: b094452b-e7aa-441e-9c5c-a78ac319d6a4 (file_hash: 279e472ff8f1e2672398020a32630604)
2025-07-22 18:13:44 | INFO     | app.service.document.document_processor:process_document:123 | 开始分块: d673f1bec5d7572105ca8dc80da42e26.docx
2025-07-22 18:14:11 | INFO     | app.service.document.document_processor:_parse_document:214 | 开始解析文件: d3ffacb717d9296e625fde86b5178741.pdf
2025-07-22 18:16:31 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 18:16:36 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 18:16:36 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 18:16:36 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 18:16:36 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 18:16:36 | INFO     | __main__:lifespan:25 | RAG系统启动中...
2025-07-22 18:16:36 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 18:16:36 | INFO     | __main__:lifespan:29 | PostgreSQL数据库连接成功
2025-07-22 18:16:36 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 18:16:43 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 18:16:43 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 18:16:43 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 18:16:43 | INFO     | app.service.document.document_processor:initialize:97 | 文档处理器初始化完成
2025-07-22 18:16:43 | INFO     | app.service.document.async_parse_queue:initialize:49 | AsyncDocumentQueue 初始化完成
2025-07-22 18:16:43 | INFO     | app.service.document.async_parse_queue:start_workers:96 | 启动了 3 个工作进程
2025-07-22 18:16:43 | INFO     | app.service.document.parse_document:initialize_document_queue:238 | 文档处理队列已启动
2025-07-22 18:16:43 | INFO     | app.service.document.async_parse_queue:_worker_loop:100 | 工作进程 worker_0 开始运行
2025-07-22 18:16:43 | INFO     | app.service.document.async_parse_queue:_worker_loop:100 | 工作进程 worker_1 开始运行
2025-07-22 18:16:43 | INFO     | app.service.document.async_parse_queue:_worker_loop:100 | 工作进程 worker_2 开始运行
2025-07-22 18:16:47 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: 897eecba-2b2d-4718-802f-77a9fc6da8ab (file_hash: d673f1bec5d7572105ca8dc80da42e26)
2025-07-22 18:16:47 | INFO     | app.service.document.async_parse_queue:_process_task:144 | 工作进程 worker_1 开始处理任务: 897eecba-2b2d-4718-802f-77a9fc6da8ab
2025-07-22 18:16:47 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: a6230a04-e48c-4a1c-ae32-dc1a37cbcfca (file_hash: d3ffacb717d9296e625fde86b5178741)
2025-07-22 18:16:47 | INFO     | app.service.document.async_parse_queue:_process_task:144 | 工作进程 worker_2 开始处理任务: a6230a04-e48c-4a1c-ae32-dc1a37cbcfca
2025-07-22 18:16:47 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: 6f4026bf-5125-48c8-961c-2ac6abdb13b9 (file_hash: 17224950eff21079a7dc90f5478c0338)
2025-07-22 18:16:47 | INFO     | app.service.document.async_parse_queue:_process_task:144 | 工作进程 worker_0 开始处理任务: 6f4026bf-5125-48c8-961c-2ac6abdb13b9
2025-07-22 18:16:47 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: 8029ca7f-6857-4c71-b59e-5a7d53ce901c (file_hash: 5ee5b529f7790fd92dbf99b6c59659cc)
2025-07-22 18:16:47 | INFO     | app.service.document.document_processor:_parse_document:214 | 开始解析文件: d673f1bec5d7572105ca8dc80da42e26.docx
2025-07-22 18:16:52 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: 29075583-74b3-4d2c-b2b6-ed1f4b93cd2f (file_hash: 279e472ff8f1e2672398020a32630604)
2025-07-22 18:16:52 | INFO     | app.service.document.document_processor:process_document:123 | 开始分块: d673f1bec5d7572105ca8dc80da42e26.docx
2025-07-22 18:18:38 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 18:18:43 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 18:18:43 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 18:18:43 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 18:18:43 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 18:18:43 | INFO     | __main__:lifespan:25 | RAG系统启动中...
2025-07-22 18:18:43 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 18:18:43 | INFO     | __main__:lifespan:29 | PostgreSQL数据库连接成功
2025-07-22 18:18:43 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 18:18:46 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 18:18:46 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 18:18:46 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 18:18:47 | INFO     | app.service.document.document_processor:initialize:97 | 文档处理器初始化完成
2025-07-22 18:18:47 | INFO     | app.service.document.async_parse_queue:initialize:49 | AsyncDocumentQueue 初始化完成
2025-07-22 18:18:47 | INFO     | app.service.document.async_parse_queue:start_workers:96 | 启动了 3 个工作进程
2025-07-22 18:18:47 | INFO     | app.service.document.parse_document:initialize_document_queue:238 | 文档处理队列已启动
2025-07-22 18:18:47 | INFO     | app.service.document.async_parse_queue:_worker_loop:100 | 工作进程 worker_0 开始运行
2025-07-22 18:18:47 | INFO     | app.service.document.async_parse_queue:_worker_loop:100 | 工作进程 worker_1 开始运行
2025-07-22 18:18:47 | INFO     | app.service.document.async_parse_queue:_worker_loop:100 | 工作进程 worker_2 开始运行
2025-07-22 18:18:51 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: f38c0221-6a14-49f3-9b2d-567fba7670ee (file_hash: d673f1bec5d7572105ca8dc80da42e26)
2025-07-22 18:18:51 | INFO     | app.service.document.async_parse_queue:_process_task:144 | 工作进程 worker_0 开始处理任务: f38c0221-6a14-49f3-9b2d-567fba7670ee
2025-07-22 18:18:51 | INFO     | app.service.document.async_parse_queue:_complete_task:185 | 任务处理成功: f38c0221-6a14-49f3-9b2d-567fba7670ee
2025-07-22 18:18:51 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: b39da6a1-5e2c-4030-b2e9-41d81931a02f (file_hash: d3ffacb717d9296e625fde86b5178741)
2025-07-22 18:18:51 | INFO     | app.service.document.async_parse_queue:_process_task:144 | 工作进程 worker_2 开始处理任务: b39da6a1-5e2c-4030-b2e9-41d81931a02f
2025-07-22 18:18:51 | INFO     | app.service.document.async_parse_queue:_complete_task:185 | 任务处理成功: b39da6a1-5e2c-4030-b2e9-41d81931a02f
2025-07-22 18:18:51 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: 944f5211-cc54-45af-a542-55af0f943af9 (file_hash: 17224950eff21079a7dc90f5478c0338)
2025-07-22 18:18:51 | INFO     | app.service.document.async_parse_queue:_process_task:144 | 工作进程 worker_1 开始处理任务: 944f5211-cc54-45af-a542-55af0f943af9
2025-07-22 18:18:51 | INFO     | app.service.document.async_parse_queue:_complete_task:185 | 任务处理成功: 944f5211-cc54-45af-a542-55af0f943af9
2025-07-22 18:18:51 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: 1c5823ad-b3a5-46ce-a508-75365cbbff31 (file_hash: 5ee5b529f7790fd92dbf99b6c59659cc)
2025-07-22 18:18:51 | INFO     | app.service.document.async_parse_queue:_process_task:144 | 工作进程 worker_0 开始处理任务: 1c5823ad-b3a5-46ce-a508-75365cbbff31
2025-07-22 18:18:51 | INFO     | app.service.document.async_parse_queue:_complete_task:185 | 任务处理成功: 1c5823ad-b3a5-46ce-a508-75365cbbff31
2025-07-22 18:18:51 | INFO     | app.service.document.document_processor:_parse_document:214 | 开始解析文件: d673f1bec5d7572105ca8dc80da42e26.docx
2025-07-22 18:18:56 | INFO     | app.service.document.async_parse_queue:add_task:71 | 任务已加入队列: c0dbe4c6-9f31-42df-8c0f-493f38a100a4 (file_hash: 279e472ff8f1e2672398020a32630604)
2025-07-22 18:18:56 | INFO     | app.service.document.async_parse_queue:_process_task:144 | 工作进程 worker_2 开始处理任务: c0dbe4c6-9f31-42df-8c0f-493f38a100a4
2025-07-22 18:18:56 | INFO     | app.service.document.async_parse_queue:_complete_task:185 | 任务处理成功: c0dbe4c6-9f31-42df-8c0f-493f38a100a4
2025-07-22 18:18:56 | INFO     | app.service.document.document_processor:process_document:123 | 开始分块: d673f1bec5d7572105ca8dc80da42e26.docx
2025-07-22 18:28:41 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 18:28:44 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 18:28:44 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 18:28:44 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 18:28:44 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 18:29:05 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 18:29:08 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 18:29:08 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 18:29:08 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 18:29:08 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 18:29:08 | INFO     | __main__:lifespan:26 | RAG系统启动中...
2025-07-22 18:29:08 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 18:29:08 | INFO     | __main__:lifespan:30 | PostgreSQL数据库连接成功
2025-07-22 18:29:08 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 18:29:12 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 18:29:12 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 18:29:12 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 18:29:12 | INFO     | app.service.document.document_queue:start:40 | 文档处理队列已启动，最大并发数: 2
2025-07-22 18:29:12 | INFO     | app.service.document.document_queue:_worker:77 | 工作进程 worker-0 已启动
2025-07-22 18:29:12 | INFO     | app.service.document.document_queue:_worker:77 | 工作进程 worker-1 已启动
2025-07-22 18:29:39 | INFO     | __main__:lifespan:42 | RAG系统关闭中...
2025-07-22 18:29:39 | INFO     | app.service.document.document_queue:_worker:118 | 工作进程 worker-0 被取消
2025-07-22 18:29:39 | INFO     | app.service.document.document_queue:_worker:124 | 工作进程 worker-0 已停止
2025-07-22 18:29:39 | INFO     | app.service.document.document_queue:_worker:118 | 工作进程 worker-1 被取消
2025-07-22 18:29:39 | INFO     | app.service.document.document_queue:_worker:124 | 工作进程 worker-1 已停止
2025-07-22 18:29:39 | INFO     | app.service.document.document_queue:stop:56 | 文档处理队列已停止
2025-07-22 18:30:55 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 18:30:58 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 18:30:58 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 18:30:58 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 18:30:58 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 18:30:58 | INFO     | __main__:lifespan:26 | RAG系统启动中...
2025-07-22 18:30:58 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 18:30:58 | INFO     | __main__:lifespan:30 | PostgreSQL数据库连接成功
2025-07-22 18:30:58 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 18:31:00 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 18:31:00 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 18:31:00 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 18:31:00 | INFO     | app.service.document.document_queue:start:40 | 文档处理队列已启动，最大并发数: 2
2025-07-22 18:31:00 | INFO     | app.service.document.document_queue:_worker:77 | 工作进程 worker-0 已启动
2025-07-22 18:31:00 | INFO     | app.service.document.document_queue:_worker:77 | 工作进程 worker-1 已启动
2025-07-22 18:31:22 | INFO     | app.service.document.document_queue:add_task:71 | 任务已添加到队列: 09ffe0dc-dc01-48b1-b88a-c8cd4fce670c, 文件: d673f1bec5d7572105ca8dc80da42e26
2025-07-22 18:31:22 | INFO     | app.service.document.document_queue:_worker:95 | 工作进程 worker-1 开始处理任务: 09ffe0dc-dc01-48b1-b88a-c8cd4fce670c, 文件: d673f1bec5d7572105ca8dc80da42e26
2025-07-22 18:31:22 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 18:31:22 | ERROR    | app.service.document.document_queue:_worker:104 | 任务处理失败: 09ffe0dc-dc01-48b1-b88a-c8cd4fce670c, 错误: 'DocumentProcessor' object has no attribute 'parse_document'
2025-07-22 18:31:22 | INFO     | app.service.document.document_queue:add_task:71 | 任务已添加到队列: 0d3c74aa-11b3-4ae1-8535-774f978d3030, 文件: d3ffacb717d9296e625fde86b5178741
2025-07-22 18:31:22 | INFO     | app.service.document.document_queue:_worker:95 | 工作进程 worker-0 开始处理任务: 0d3c74aa-11b3-4ae1-8535-774f978d3030, 文件: d3ffacb717d9296e625fde86b5178741
2025-07-22 18:31:22 | ERROR    | app.service.document.document_queue:_worker:104 | 任务处理失败: 0d3c74aa-11b3-4ae1-8535-774f978d3030, 错误: 'DocumentProcessor' object has no attribute 'parse_document'
2025-07-22 18:31:22 | INFO     | app.service.document.document_queue:add_task:71 | 任务已添加到队列: b7f5a54f-576c-4082-833d-c90e7a8eebcc, 文件: 17224950eff21079a7dc90f5478c0338
2025-07-22 18:31:22 | INFO     | app.service.document.document_queue:add_task:71 | 任务已添加到队列: 105d1487-b7e1-485d-a09e-efe7dcb8ee3c, 文件: 5ee5b529f7790fd92dbf99b6c59659cc
2025-07-22 18:31:22 | INFO     | app.service.document.document_queue:_worker:95 | 工作进程 worker-1 开始处理任务: b7f5a54f-576c-4082-833d-c90e7a8eebcc, 文件: 17224950eff21079a7dc90f5478c0338
2025-07-22 18:31:22 | ERROR    | app.service.document.document_queue:_worker:104 | 任务处理失败: b7f5a54f-576c-4082-833d-c90e7a8eebcc, 错误: 'DocumentProcessor' object has no attribute 'parse_document'
2025-07-22 18:31:22 | INFO     | app.service.document.document_queue:_worker:95 | 工作进程 worker-0 开始处理任务: 105d1487-b7e1-485d-a09e-efe7dcb8ee3c, 文件: 5ee5b529f7790fd92dbf99b6c59659cc
2025-07-22 18:31:22 | ERROR    | app.service.document.document_queue:_worker:104 | 任务处理失败: 105d1487-b7e1-485d-a09e-efe7dcb8ee3c, 错误: 'DocumentProcessor' object has no attribute 'parse_document'
2025-07-22 18:31:22 | INFO     | app.service.document.document_queue:add_task:71 | 任务已添加到队列: 673c03e9-5494-4f03-949d-d7d855190a20, 文件: 279e472ff8f1e2672398020a32630604
2025-07-22 18:31:22 | INFO     | app.service.document.document_queue:_worker:95 | 工作进程 worker-1 开始处理任务: 673c03e9-5494-4f03-949d-d7d855190a20, 文件: 279e472ff8f1e2672398020a32630604
2025-07-22 18:31:22 | ERROR    | app.service.document.document_queue:_worker:104 | 任务处理失败: 673c03e9-5494-4f03-949d-d7d855190a20, 错误: 'DocumentProcessor' object has no attribute 'parse_document'
2025-07-22 18:31:22 | INFO     | app.service.document.document_queue:add_task:71 | 任务已添加到队列: c81b6729-f0e3-409d-a808-950504b67808, 文件: ac58b2194caad5efd4a573a10912d4a2
2025-07-22 18:31:22 | INFO     | app.service.document.document_queue:_worker:95 | 工作进程 worker-0 开始处理任务: c81b6729-f0e3-409d-a808-950504b67808, 文件: ac58b2194caad5efd4a573a10912d4a2
2025-07-22 18:31:22 | ERROR    | app.service.document.document_queue:_worker:104 | 任务处理失败: c81b6729-f0e3-409d-a808-950504b67808, 错误: 'DocumentProcessor' object has no attribute 'parse_document'
2025-07-22 18:31:22 | INFO     | app.service.document.document_queue:add_task:71 | 任务已添加到队列: 7fa08a9b-82e4-4c05-bd99-7f8aaef81e64, 文件: 2d6cc1c63af8ef20abf6177fca49e097
2025-07-22 18:31:22 | INFO     | app.service.document.document_queue:_worker:95 | 工作进程 worker-1 开始处理任务: 7fa08a9b-82e4-4c05-bd99-7f8aaef81e64, 文件: 2d6cc1c63af8ef20abf6177fca49e097
2025-07-22 18:31:22 | ERROR    | app.service.document.document_queue:_worker:104 | 任务处理失败: 7fa08a9b-82e4-4c05-bd99-7f8aaef81e64, 错误: 'DocumentProcessor' object has no attribute 'parse_document'
2025-07-22 18:31:22 | INFO     | app.service.document.document_queue:add_task:71 | 任务已添加到队列: 4adc0804-df05-43e8-a0f9-3619742dd119, 文件: 4f0e3d3f79ca4f09321c61ce679af449
2025-07-22 18:31:22 | INFO     | app.service.document.document_queue:_worker:95 | 工作进程 worker-0 开始处理任务: 4adc0804-df05-43e8-a0f9-3619742dd119, 文件: 4f0e3d3f79ca4f09321c61ce679af449
2025-07-22 18:31:22 | ERROR    | app.service.document.document_queue:_worker:104 | 任务处理失败: 4adc0804-df05-43e8-a0f9-3619742dd119, 错误: 'DocumentProcessor' object has no attribute 'parse_document'
2025-07-22 18:31:22 | INFO     | app.service.document.document_queue:add_task:71 | 任务已添加到队列: a4acb046-8d16-4374-a4cf-854e3553d378, 文件: 12134a1bc2e7a7906cf3b09afbb066ca
2025-07-22 18:31:22 | INFO     | app.service.document.document_queue:_worker:95 | 工作进程 worker-1 开始处理任务: a4acb046-8d16-4374-a4cf-854e3553d378, 文件: 12134a1bc2e7a7906cf3b09afbb066ca
2025-07-22 18:31:22 | ERROR    | app.service.document.document_queue:_worker:104 | 任务处理失败: a4acb046-8d16-4374-a4cf-854e3553d378, 错误: 'DocumentProcessor' object has no attribute 'parse_document'
2025-07-22 18:31:22 | INFO     | app.service.document.document_queue:add_task:71 | 任务已添加到队列: 04407fa7-5126-4e52-b549-dced5b68bab3, 文件: 0f2e292a160aa2820ea3c077dc668d80
2025-07-22 18:31:22 | INFO     | app.service.document.document_queue:_worker:95 | 工作进程 worker-0 开始处理任务: 04407fa7-5126-4e52-b549-dced5b68bab3, 文件: 0f2e292a160aa2820ea3c077dc668d80
2025-07-22 18:31:22 | ERROR    | app.service.document.document_queue:_worker:104 | 任务处理失败: 04407fa7-5126-4e52-b549-dced5b68bab3, 错误: 'DocumentProcessor' object has no attribute 'parse_document'
2025-07-22 18:31:37 | INFO     | __main__:lifespan:42 | RAG系统关闭中...
2025-07-22 18:31:37 | INFO     | app.service.document.document_queue:_worker:119 | 工作进程 worker-0 被取消
2025-07-22 18:31:37 | INFO     | app.service.document.document_queue:_worker:125 | 工作进程 worker-0 已停止
2025-07-22 18:31:37 | INFO     | app.service.document.document_queue:_worker:119 | 工作进程 worker-1 被取消
2025-07-22 18:31:37 | INFO     | app.service.document.document_queue:_worker:125 | 工作进程 worker-1 已停止
2025-07-22 18:31:37 | INFO     | app.service.document.document_queue:stop:56 | 文档处理队列已停止
2025-07-22 18:31:49 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 18:31:53 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 18:31:53 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 18:31:53 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 18:31:53 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 18:31:53 | INFO     | __main__:lifespan:26 | RAG系统启动中...
2025-07-22 18:31:53 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 18:31:53 | INFO     | __main__:lifespan:30 | PostgreSQL数据库连接成功
2025-07-22 18:31:53 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 18:32:05 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 18:32:05 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 18:32:05 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 18:32:05 | INFO     | app.service.document.document_queue:start:40 | 文档处理队列已启动，最大并发数: 2
2025-07-22 18:32:05 | INFO     | app.service.document.document_queue:_worker:77 | 工作进程 worker-0 已启动
2025-07-22 18:32:05 | INFO     | app.service.document.document_queue:_worker:77 | 工作进程 worker-1 已启动
2025-07-22 18:32:11 | INFO     | app.service.document.document_queue:add_task:71 | 任务已添加到队列: fd2666ab-f5f6-4598-ad4c-f0e65c04eb0c, 文件: d673f1bec5d7572105ca8dc80da42e26
2025-07-22 18:32:11 | INFO     | app.service.document.document_queue:_worker:95 | 工作进程 worker-0 开始处理任务: fd2666ab-f5f6-4598-ad4c-f0e65c04eb0c, 文件: d673f1bec5d7572105ca8dc80da42e26
2025-07-22 18:32:12 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 18:32:12 | INFO     | app.service.document.document_queue:add_task:71 | 任务已添加到队列: 10f1a9a7-e4fc-4822-a552-f8fd8832cf5d, 文件: d3ffacb717d9296e625fde86b5178741
2025-07-22 18:32:12 | INFO     | app.service.document.document_queue:_worker:95 | 工作进程 worker-1 开始处理任务: 10f1a9a7-e4fc-4822-a552-f8fd8832cf5d, 文件: d3ffacb717d9296e625fde86b5178741
2025-07-22 18:32:12 | INFO     | app.service.document.document_queue:add_task:71 | 任务已添加到队列: 27bb92f7-10c7-46e7-b980-8b713de298a8, 文件: 17224950eff21079a7dc90f5478c0338
2025-07-22 18:32:12 | INFO     | app.service.document.document_queue:add_task:71 | 任务已添加到队列: 3e910c5e-ed66-4aca-a9fc-9542bd4ed28f, 文件: 5ee5b529f7790fd92dbf99b6c59659cc
2025-07-22 18:32:12 | INFO     | app.service.document.document_processor:_parse_document:213 | 开始解析文件: d673f1bec5d7572105ca8dc80da42e26.docx
2025-07-22 18:32:17 | INFO     | app.service.document.document_queue:add_task:71 | 任务已添加到队列: 7edd9c2e-aa88-4cfc-97a8-14cf442fd373, 文件: 279e472ff8f1e2672398020a32630604
2025-07-22 18:32:17 | INFO     | app.service.document.document_processor:process_document:122 | 开始分块: d673f1bec5d7572105ca8dc80da42e26.docx
2025-07-22 18:32:44 | INFO     | app.service.document.document_queue:add_task:71 | 任务已添加到队列: d85656ac-bdc3-4bad-b41b-41175585aae1, 文件: ac58b2194caad5efd4a573a10912d4a2
2025-07-22 18:32:44 | INFO     | app.service.document.document_processor:process_document:126 | 共有32 片
2025-07-22 18:32:44 | INFO     | app.service.document.document_processor:_parse_document:213 | 开始解析文件: d3ffacb717d9296e625fde86b5178741.pdf
2025-07-22 18:33:40 | INFO     | app.service.document.document_queue:add_task:71 | 任务已添加到队列: 98803240-8bcc-4971-aac1-176ceb200183, 文件: 2d6cc1c63af8ef20abf6177fca49e097
2025-07-22 18:33:40 | INFO     | app.service.document.document_processor:process_document:122 | 开始分块: d3ffacb717d9296e625fde86b5178741.pdf
2025-07-22 18:33:40 | INFO     | app.service.document.document_queue:add_task:71 | 任务已添加到队列: 8e10284a-f275-42d3-815a-e4dd495dfe8b, 文件: 4f0e3d3f79ca4f09321c61ce679af449
2025-07-22 18:33:40 | INFO     | app.service.document.document_processor:process_document:126 | 共有2 片
2025-07-22 18:33:40 | INFO     | app.service.document.document_queue:add_task:71 | 任务已添加到队列: e4c34984-4673-4902-8a0b-ea07c87e22a7, 文件: 12134a1bc2e7a7906cf3b09afbb066ca
2025-07-22 18:33:40 | INFO     | app.service.document.document_queue:add_task:71 | 任务已添加到队列: 07259307-725a-4d00-9847-ec0000cdf550, 文件: 0f2e292a160aa2820ea3c077dc668d80
2025-07-22 18:33:40 | INFO     | app.service.document.document_queue:_worker:119 | 工作进程 worker-0 被取消
2025-07-22 18:33:40 | INFO     | app.service.document.document_queue:_worker:125 | 工作进程 worker-0 已停止
2025-07-22 18:33:40 | INFO     | app.service.document.document_queue:_worker:119 | 工作进程 worker-1 被取消
2025-07-22 18:33:40 | INFO     | app.service.document.document_queue:_worker:125 | 工作进程 worker-1 已停止
2025-07-22 18:34:02 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-22 18:34:06 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-22 18:34:06 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-22 18:34:06 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-22 18:34:06 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-22 18:34:06 | INFO     | __main__:lifespan:26 | RAG系统启动中...
2025-07-22 18:34:06 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-22 18:34:06 | INFO     | __main__:lifespan:30 | PostgreSQL数据库连接成功
2025-07-22 18:34:06 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-22 18:34:07 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-22 18:34:07 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-22 18:34:07 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-22 18:34:07 | INFO     | app.service.document.document_queue:start:41 | 文档处理队列已启动，最大并发数: 2
2025-07-22 18:34:07 | INFO     | app.service.document.document_queue:_worker:84 | 工作进程 worker-0 已启动
2025-07-22 18:34:07 | INFO     | app.service.document.document_queue:_worker:84 | 工作进程 worker-1 已启动
2025-07-22 18:34:13 | INFO     | app.service.document.document_queue:add_task:75 | 任务已添加到队列: 62e23579-a660-4d00-a839-3e7fb46bfdc0, 文件: d673f1bec5d7572105ca8dc80da42e26
2025-07-22 18:34:13 | INFO     | app.service.document.document_queue:_worker:115 | 工作进程 worker-1 开始处理任务: 62e23579-a660-4d00-a839-3e7fb46bfdc0, 文件: d673f1bec5d7572105ca8dc80da42e26
2025-07-22 18:34:13 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-22 18:34:13 | INFO     | app.service.document.document_queue:add_task:75 | 任务已添加到队列: 501c8dc6-72c9-47c4-b3e1-2713fb0498f6, 文件: d3ffacb717d9296e625fde86b5178741
2025-07-22 18:34:13 | INFO     | app.service.document.document_queue:_worker:115 | 工作进程 worker-0 开始处理任务: 501c8dc6-72c9-47c4-b3e1-2713fb0498f6, 文件: d3ffacb717d9296e625fde86b5178741
2025-07-22 18:34:13 | INFO     | app.service.document.document_queue:add_task:75 | 任务已添加到队列: 9873dbcd-b2c8-4250-8554-b8c16b0ea1b7, 文件: 17224950eff21079a7dc90f5478c0338
2025-07-22 18:34:13 | INFO     | app.service.document.document_queue:add_task:75 | 任务已添加到队列: e5caa21c-4ebe-4eb3-8ad8-28c416345906, 文件: 5ee5b529f7790fd92dbf99b6c59659cc
2025-07-22 18:34:13 | INFO     | app.service.document.document_processor:_parse_document:213 | 开始解析文件: d673f1bec5d7572105ca8dc80da42e26.docx
2025-07-22 18:34:18 | INFO     | app.service.document.document_queue:add_task:75 | 任务已添加到队列: f8531db5-47f4-4bad-aae6-e5a641a654aa, 文件: 279e472ff8f1e2672398020a32630604
2025-07-22 18:34:18 | INFO     | app.service.document.document_processor:process_document:122 | 开始分块: d673f1bec5d7572105ca8dc80da42e26.docx
2025-07-22 18:34:46 | INFO     | app.service.document.document_queue:add_task:75 | 任务已添加到队列: f8847822-4f19-4551-8c30-7c9c25db0f68, 文件: ac58b2194caad5efd4a573a10912d4a2
2025-07-22 18:34:46 | INFO     | app.service.document.document_processor:process_document:126 | 共有32 片
2025-07-22 18:34:46 | INFO     | app.service.document.document_processor:_parse_document:213 | 开始解析文件: d3ffacb717d9296e625fde86b5178741.pdf
