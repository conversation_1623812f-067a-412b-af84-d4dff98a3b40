2025-07-21 10:09:25 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-21 10:09:42 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-21 10:09:42 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-21 10:09:42 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-21 10:09:42 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-21 10:09:42 | INFO     | __main__:lifespan:23 | RAG系统启动中...
2025-07-21 10:09:42 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-21 10:09:42 | INFO     | __main__:lifespan:27 | PostgreSQL数据库连接成功
2025-07-21 10:09:42 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-21 10:10:07 | ERROR    | app.service.model_client:check_health:97 | 模型服务健康检查失败: All connection attempts failed
2025-07-21 10:10:07 | ERROR    | app.utils.exceptions:wrapper:85 | check_health RAG系统异常: 模型服务健康检查失败: All connection attempts failed
2025-07-21 10:10:07 | ERROR    | __main__:lifespan:33 | 系统初始化失败: 模型服务健康检查失败: All connection attempts failed
2025-07-21 10:10:13 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-21 10:10:22 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-21 10:10:22 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-21 10:10:22 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-21 10:10:22 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-21 10:10:22 | INFO     | __main__:lifespan:23 | RAG系统启动中...
2025-07-21 10:10:23 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-21 10:10:23 | INFO     | __main__:lifespan:27 | PostgreSQL数据库连接成功
2025-07-21 10:10:23 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-21 10:10:28 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-21 10:10:28 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-21 10:10:28 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-21 10:11:10 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 10:11:10 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 10 个结果
2025-07-21 10:11:10 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 10:11:10 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 10 个结果
2025-07-21 10:11:10 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 10:11:10 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-21 10:11:10 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 10:11:10 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-21 10:11:10 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 10:11:10 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-21 10:11:10 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 10:11:10 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-21 10:11:10 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 10:11:10 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-21 10:13:13 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 10:13:13 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 10 个结果
2025-07-21 10:13:13 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 10:13:13 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 10 个结果
2025-07-21 10:13:13 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 10:13:13 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-21 10:13:13 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 10:13:13 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-21 10:13:13 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 10:13:13 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-21 10:13:13 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 10:13:13 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-21 10:13:13 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 10:13:13 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-21 11:29:51 | INFO     | __main__:lifespan:36 | RAG系统关闭中...
2025-07-21 11:29:56 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-21 11:30:07 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-21 11:30:07 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-21 11:30:07 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-21 11:30:07 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-21 11:30:07 | INFO     | __main__:lifespan:23 | RAG系统启动中...
2025-07-21 11:30:07 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-21 11:30:07 | INFO     | __main__:lifespan:27 | PostgreSQL数据库连接成功
2025-07-21 11:30:07 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-21 11:30:10 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-21 11:30:10 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-21 11:30:10 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-21 11:30:22 | INFO     | app.api.knowledge:create_knowledge:42 | 成功创建知识库: 三江源
2025-07-21 11:32:07 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: CAMH_photo_exhibit.pdf
2025-07-21 11:32:07 | ERROR    | app.api.document_router:upload_document:30 | 文档上传处理失败: Unknown filter param 'name_hash'. Allowed base values are ['created_at', 'description', 'id', 'name', 'name_md5', 'public', 'updated_at']
2025-07-21 11:32:34 | INFO     | __main__:lifespan:36 | RAG系统关闭中...
2025-07-21 11:32:39 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-21 11:32:49 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-21 11:32:49 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-21 11:32:49 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-21 11:32:49 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-21 11:32:49 | INFO     | __main__:lifespan:23 | RAG系统启动中...
2025-07-21 11:32:49 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-21 11:32:49 | INFO     | __main__:lifespan:27 | PostgreSQL数据库连接成功
2025-07-21 11:32:49 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-21 11:32:50 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-21 11:32:50 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-21 11:32:50 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-21 11:32:53 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: CAMH_photo_exhibit.pdf
2025-07-21 11:32:53 | ERROR    | app.api.document_router:upload_document:30 | 文档上传处理失败: Object "KnowledgeBase" does not exist
2025-07-21 11:55:25 | INFO     | __main__:lifespan:36 | RAG系统关闭中...
2025-07-21 11:55:30 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-21 11:55:41 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-21 11:55:41 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-21 11:55:41 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-21 11:55:41 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-21 11:55:41 | INFO     | __main__:lifespan:23 | RAG系统启动中...
2025-07-21 11:55:42 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-21 11:55:42 | INFO     | __main__:lifespan:27 | PostgreSQL数据库连接成功
2025-07-21 11:55:42 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-21 11:55:44 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-21 11:55:44 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-21 11:55:44 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-21 11:55:46 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: CAMH_photo_exhibit.pdf
2025-07-21 11:55:46 | ERROR    | app.api.document_router:upload_document:30 | 文档上传处理失败: Object "KnowledgeBase" does not exist
2025-07-21 12:09:50 | INFO     | __main__:lifespan:36 | RAG系统关闭中...
2025-07-21 12:09:55 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-21 12:10:06 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-21 12:10:06 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-21 12:10:06 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-21 12:10:06 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-21 12:10:06 | INFO     | __main__:lifespan:23 | RAG系统启动中...
2025-07-21 12:10:06 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-21 12:10:06 | INFO     | __main__:lifespan:27 | PostgreSQL数据库连接成功
2025-07-21 12:10:06 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-21 12:10:08 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-21 12:10:08 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-21 12:10:08 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-21 12:10:13 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: CAMH_photo_exhibit.pdf
2025-07-21 12:10:13 | ERROR    | app.api.document_router:upload_document:30 | 文档上传处理失败: Object "KnowledgeBase" does not exist
2025-07-21 12:13:50 | INFO     | __main__:lifespan:36 | RAG系统关闭中...
2025-07-21 12:14:27 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-21 12:14:40 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-21 12:14:40 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-21 12:14:40 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-21 12:14:40 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-21 12:14:40 | INFO     | __main__:lifespan:23 | RAG系统启动中...
2025-07-21 12:14:40 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-21 12:14:40 | INFO     | __main__:lifespan:27 | PostgreSQL数据库连接成功
2025-07-21 12:14:40 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-21 12:14:42 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-21 12:14:42 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-21 12:14:42 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-21 12:14:45 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: CAMH_photo_exhibit.pdf
2025-07-21 12:14:45 | ERROR    | app.api.document_router:upload_document:30 | 文档上传处理失败: Object "KnowledgeBase" does not exist
2025-07-21 12:15:34 | INFO     | __main__:lifespan:36 | RAG系统关闭中...
2025-07-21 12:15:39 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-21 12:15:49 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-21 12:15:49 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-21 12:15:49 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-21 12:15:49 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-21 12:15:49 | INFO     | __main__:lifespan:23 | RAG系统启动中...
2025-07-21 12:15:49 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-21 12:15:49 | INFO     | __main__:lifespan:27 | PostgreSQL数据库连接成功
2025-07-21 12:15:49 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-21 12:15:53 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-21 12:15:53 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-21 12:15:53 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-21 12:16:08 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: CAMH_photo_exhibit.pdf
2025-07-21 12:16:08 | ERROR    | app.service.document.document_upload:upload_document_service:165 | 获取知识库失败: Object "KnowledgeBase" does not exist
2025-07-21 12:17:05 | INFO     | __main__:lifespan:36 | RAG系统关闭中...
2025-07-21 12:17:16 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-21 12:17:20 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-21 12:17:20 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-21 12:17:20 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-21 12:17:20 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-21 12:17:20 | INFO     | __main__:lifespan:23 | RAG系统启动中...
2025-07-21 12:17:21 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-21 12:17:21 | INFO     | __main__:lifespan:27 | PostgreSQL数据库连接成功
2025-07-21 12:17:21 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-21 12:17:22 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-21 12:17:22 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-21 12:17:22 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-21 12:17:32 | INFO     | app.api.document_router:upload_document:23 | 开始处理上传文件: CAMH_photo_exhibit.pdf
2025-07-21 12:17:32 | INFO     | app.service.document.document_upload:extract_document_metadata:54 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmp4b9lkmgo.pdf
2025-07-21 12:17:32 | WARNING  | app.service.document.document_upload:extract_document_metadata:81 | 不支持的文件类型: .pdf
2025-07-21 12:17:32 | INFO     | app.service.document.document_upload:extract_document_metadata:87 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmp4b9lkmgo.pdf
2025-07-21 12:17:32 | INFO     | app.service.document.document_upload:upload_document_service:186 | 成功提取元数据: CAMH_photo_exhibit.pdf
2025-07-21 12:17:32 | ERROR    | app.database.minio_manager:upload_file:58 | 文件上传失败: C:\Users\<USER>\AppData\Local\Temp\tmp4b9lkmgo.pdf -> None/993cfd22e465111c6d9afb23a6210831.pdf, 错误: expected string or bytes-like object, got 'NoneType'
2025-07-21 12:17:32 | ERROR    | app.api.document_router:upload_document:29 | 文档上传处理失败: expected string or bytes-like object, got 'NoneType'
2025-07-21 12:19:52 | INFO     | __main__:lifespan:36 | RAG系统关闭中...
2025-07-21 12:20:03 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-21 12:20:07 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-21 12:20:07 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-21 12:20:07 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-21 12:20:07 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-21 12:20:07 | INFO     | __main__:lifespan:23 | RAG系统启动中...
2025-07-21 12:20:07 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-21 12:20:07 | INFO     | __main__:lifespan:27 | PostgreSQL数据库连接成功
2025-07-21 12:20:07 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-21 12:20:11 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-21 12:20:11 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-21 12:20:11 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-21 12:20:19 | INFO     | app.api.document_router:upload_document:23 | 开始处理上传文件: CAMH_photo_exhibit.pdf
2025-07-21 12:20:19 | ERROR    | app.api.document_router:upload_document:29 | 文档上传处理失败: Object "KnowledgeBase" does not exist
2025-07-21 12:20:45 | INFO     | app.api.document_router:upload_document:23 | 开始处理上传文件: CAMH_photo_exhibit.pdf
2025-07-21 12:20:45 | ERROR    | app.api.document_router:upload_document:29 | 文档上传处理失败: Object "KnowledgeBase" does not exist
2025-07-21 12:22:35 | INFO     | __main__:lifespan:36 | RAG系统关闭中...
2025-07-21 12:22:46 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-21 12:22:51 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-21 12:22:51 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-21 12:22:51 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-21 12:22:51 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-21 12:22:51 | INFO     | __main__:lifespan:23 | RAG系统启动中...
2025-07-21 12:22:51 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-21 12:22:51 | INFO     | __main__:lifespan:27 | PostgreSQL数据库连接成功
2025-07-21 12:22:51 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-21 12:22:53 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-21 12:22:53 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-21 12:22:53 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-21 12:22:54 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: CAMH_photo_exhibit.pdf
2025-07-21 12:22:55 | INFO     | app.service.document.document_upload:extract_document_metadata:54 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmpfa1vkms3.pdf
2025-07-21 12:22:55 | WARNING  | app.service.document.document_upload:extract_document_metadata:81 | 不支持的文件类型: .pdf
2025-07-21 12:22:55 | INFO     | app.service.document.document_upload:extract_document_metadata:87 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmpfa1vkms3.pdf
2025-07-21 12:22:55 | INFO     | app.service.document.document_upload:upload_document_service:182 | 成功提取元数据: CAMH_photo_exhibit.pdf
2025-07-21 12:22:55 | INFO     | app.database.minio_manager:upload_file:49 | 存储桶 a8afbd04bd4fe4f6ab4f2a9c8736413c 不存在，已自动创建
2025-07-21 12:22:55 | INFO     | app.database.minio_manager:upload_file:53 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmpfa1vkms3.pdf -> a8afbd04bd4fe4f6ab4f2a9c8736413c/993cfd22e465111c6d9afb23a6210831.pdf
2025-07-21 12:22:55 | ERROR    | app.api.document_router:upload_document:30 | 文档上传处理失败: duplicate key value violates unique constraint "file_info_pkey"
DETAIL:  Key (id)=(1) already exists.
2025-07-21 12:26:52 | INFO     | __main__:lifespan:36 | RAG系统关闭中...
2025-07-21 12:27:05 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-21 12:27:11 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-21 12:27:11 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-21 12:27:11 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-21 12:27:11 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-21 12:27:11 | INFO     | __main__:lifespan:23 | RAG系统启动中...
2025-07-21 12:27:11 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-21 12:27:11 | INFO     | __main__:lifespan:27 | PostgreSQL数据库连接成功
2025-07-21 12:27:11 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-21 12:27:15 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-21 12:27:15 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-21 12:27:15 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-21 12:27:59 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: CAMH_photo_exhibit.pdf
2025-07-21 12:27:59 | INFO     | app.service.document.document_upload:extract_document_metadata:54 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmpts490emb.pdf
2025-07-21 12:27:59 | WARNING  | app.service.document.document_upload:extract_document_metadata:81 | 不支持的文件类型: .pdf
2025-07-21 12:27:59 | INFO     | app.service.document.document_upload:extract_document_metadata:87 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmpts490emb.pdf
2025-07-21 12:27:59 | INFO     | app.service.document.document_upload:upload_document_service:181 | 成功提取元数据: CAMH_photo_exhibit.pdf
2025-07-21 12:28:00 | INFO     | app.database.minio_manager:upload_file:53 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmpts490emb.pdf -> a8afbd04bd4fe4f6ab4f2a9c8736413c/993cfd22e465111c6d9afb23a6210831.pdf
2025-07-21 12:28:00 | ERROR    | app.api.document_router:upload_document:30 | 文档上传处理失败: duplicate key value violates unique constraint "file_info_pkey"
DETAIL:  Key (id)=(2) already exists.
2025-07-21 12:28:06 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: CAMH_photo_exhibit.pdf
2025-07-21 12:28:06 | INFO     | app.service.document.document_upload:extract_document_metadata:54 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmpzka5n_se.pdf
2025-07-21 12:28:06 | WARNING  | app.service.document.document_upload:extract_document_metadata:81 | 不支持的文件类型: .pdf
2025-07-21 12:28:06 | INFO     | app.service.document.document_upload:extract_document_metadata:87 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmpzka5n_se.pdf
2025-07-21 12:28:06 | INFO     | app.service.document.document_upload:upload_document_service:181 | 成功提取元数据: CAMH_photo_exhibit.pdf
2025-07-21 12:28:06 | INFO     | app.database.minio_manager:upload_file:53 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmpzka5n_se.pdf -> a8afbd04bd4fe4f6ab4f2a9c8736413c/993cfd22e465111c6d9afb23a6210831.pdf
2025-07-21 12:28:37 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: qt9ns0x69b_noSplash_268b1dff02ae394f3b43a09a5d72e8bf.pdf
2025-07-21 12:28:37 | INFO     | app.service.document.document_upload:extract_document_metadata:54 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmpnef1nzpl.pdf
2025-07-21 12:28:37 | WARNING  | app.service.document.document_upload:extract_document_metadata:81 | 不支持的文件类型: .pdf
2025-07-21 12:28:37 | INFO     | app.service.document.document_upload:extract_document_metadata:87 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmpnef1nzpl.pdf
2025-07-21 12:28:37 | INFO     | app.service.document.document_upload:upload_document_service:181 | 成功提取元数据: qt9ns0x69b_noSplash_268b1dff02ae394f3b43a09a5d72e8bf.pdf
2025-07-21 12:28:37 | INFO     | app.database.minio_manager:upload_file:53 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmpnef1nzpl.pdf -> a8afbd04bd4fe4f6ab4f2a9c8736413c/d03b5aa3c5ccbc109869cd0e4905e3a0.pdf
2025-07-21 12:28:42 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: icimodPR4-017.pdf
2025-07-21 12:28:42 | INFO     | app.service.document.document_upload:extract_document_metadata:54 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmp001hxoza.pdf
2025-07-21 12:28:42 | WARNING  | app.service.document.document_upload:extract_document_metadata:81 | 不支持的文件类型: .pdf
2025-07-21 12:28:42 | INFO     | app.service.document.document_upload:extract_document_metadata:87 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmp001hxoza.pdf
2025-07-21 12:28:42 | INFO     | app.service.document.document_upload:upload_document_service:181 | 成功提取元数据: icimodPR4-017.pdf
2025-07-21 12:28:42 | INFO     | app.database.minio_manager:upload_file:53 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmp001hxoza.pdf -> a8afbd04bd4fe4f6ab4f2a9c8736413c/2f485756545989422ab90ad410aff89a.pdf
2025-07-21 12:28:49 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: Foggin-CV-Sept-2024.pdf
2025-07-21 12:28:49 | INFO     | app.service.document.document_upload:extract_document_metadata:54 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmpzb5xzesz.pdf
2025-07-21 12:28:49 | WARNING  | app.service.document.document_upload:extract_document_metadata:81 | 不支持的文件类型: .pdf
2025-07-21 12:28:49 | INFO     | app.service.document.document_upload:extract_document_metadata:87 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmpzb5xzesz.pdf
2025-07-21 12:28:49 | INFO     | app.service.document.document_upload:upload_document_service:181 | 成功提取元数据: Foggin-CV-Sept-2024.pdf
2025-07-21 12:28:49 | INFO     | app.database.minio_manager:upload_file:53 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmpzb5xzesz.pdf -> a8afbd04bd4fe4f6ab4f2a9c8736413c/4b5d3036d307179b979f4e265767c54f.pdf
2025-07-21 12:28:50 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: Foggin-CV-Sept-2024.pdf
2025-07-21 12:28:59 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: MarcFoggin.pdf
2025-07-21 12:28:59 | INFO     | app.service.document.document_upload:extract_document_metadata:54 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmpu23_02jz.pdf
2025-07-21 12:28:59 | WARNING  | app.service.document.document_upload:extract_document_metadata:81 | 不支持的文件类型: .pdf
2025-07-21 12:28:59 | INFO     | app.service.document.document_upload:extract_document_metadata:87 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmpu23_02jz.pdf
2025-07-21 12:28:59 | INFO     | app.service.document.document_upload:upload_document_service:181 | 成功提取元数据: MarcFoggin.pdf
2025-07-21 12:28:59 | INFO     | app.database.minio_manager:upload_file:53 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmpu23_02jz.pdf -> a8afbd04bd4fe4f6ab4f2a9c8736413c/7f2c42523c00ed492dc4e91507d69a54.pdf
2025-07-21 12:29:05 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: f1000research-81334.pdf
2025-07-21 12:29:05 | INFO     | app.service.document.document_upload:extract_document_metadata:54 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmplxn9l1up.pdf
2025-07-21 12:29:05 | WARNING  | app.service.document.document_upload:extract_document_metadata:81 | 不支持的文件类型: .pdf
2025-07-21 12:29:05 | INFO     | app.service.document.document_upload:extract_document_metadata:87 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmplxn9l1up.pdf
2025-07-21 12:29:05 | INFO     | app.service.document.document_upload:upload_document_service:181 | 成功提取元数据: f1000research-81334.pdf
2025-07-21 12:29:05 | INFO     | app.database.minio_manager:upload_file:53 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmplxn9l1up.pdf -> a8afbd04bd4fe4f6ab4f2a9c8736413c/bc665869a7d10f36ad60b9128a3c3b93.pdf
2025-07-21 12:29:12 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: CV-Marc-Foggin2.pdf
2025-07-21 12:29:12 | INFO     | app.service.document.document_upload:extract_document_metadata:54 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmpp9je7rer.pdf
2025-07-21 12:29:12 | WARNING  | app.service.document.document_upload:extract_document_metadata:81 | 不支持的文件类型: .pdf
2025-07-21 12:29:12 | INFO     | app.service.document.document_upload:extract_document_metadata:87 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmpp9je7rer.pdf
2025-07-21 12:29:12 | INFO     | app.service.document.document_upload:upload_document_service:181 | 成功提取元数据: CV-Marc-Foggin2.pdf
2025-07-21 12:29:12 | INFO     | app.database.minio_manager:upload_file:53 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmpp9je7rer.pdf -> a8afbd04bd4fe4f6ab4f2a9c8736413c/c723abbb1ed5438774da530d0a49f91b.pdf
2025-07-21 12:29:18 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: PP-AR-2012-Cn.pdf
2025-07-21 12:29:18 | INFO     | app.service.document.document_upload:extract_document_metadata:54 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmpn26bg81w.pdf
2025-07-21 12:29:18 | WARNING  | app.service.document.document_upload:extract_document_metadata:81 | 不支持的文件类型: .pdf
2025-07-21 12:29:18 | INFO     | app.service.document.document_upload:extract_document_metadata:87 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmpn26bg81w.pdf
2025-07-21 12:29:18 | INFO     | app.service.document.document_upload:upload_document_service:181 | 成功提取元数据: PP-AR-2012-Cn.pdf
2025-07-21 12:29:18 | INFO     | app.database.minio_manager:upload_file:53 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmpn26bg81w.pdf -> a8afbd04bd4fe4f6ab4f2a9c8736413c/2210e708f388f9d17802f579c61dce13.pdf
2025-07-21 12:29:25 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: Biodiv-Sanjiangyuan-Ch.pdf
2025-07-21 12:29:25 | INFO     | app.service.document.document_upload:extract_document_metadata:54 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmp85tol_7o.pdf
2025-07-21 12:29:25 | WARNING  | app.service.document.document_upload:extract_document_metadata:81 | 不支持的文件类型: .pdf
2025-07-21 12:29:25 | INFO     | app.service.document.document_upload:extract_document_metadata:87 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmp85tol_7o.pdf
2025-07-21 12:29:25 | INFO     | app.service.document.document_upload:upload_document_service:181 | 成功提取元数据: Biodiv-Sanjiangyuan-Ch.pdf
2025-07-21 12:29:25 | INFO     | app.database.minio_manager:upload_file:53 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmp85tol_7o.pdf -> a8afbd04bd4fe4f6ab4f2a9c8736413c/a795e7f69478f5a6e2b2b77c82b29100.pdf
2025-07-21 12:29:33 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: 瑞典安全和发展政策研究所（ISDP）.pdf
2025-07-21 12:29:33 | INFO     | app.service.document.document_upload:extract_document_metadata:54 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmpssg6pmaj.pdf
2025-07-21 12:29:33 | WARNING  | app.service.document.document_upload:extract_document_metadata:81 | 不支持的文件类型: .pdf
2025-07-21 12:29:33 | INFO     | app.service.document.document_upload:extract_document_metadata:87 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmpssg6pmaj.pdf
2025-07-21 12:29:33 | INFO     | app.service.document.document_upload:upload_document_service:181 | 成功提取元数据: 瑞典安全和发展政策研究所（ISDP）.pdf
2025-07-21 12:29:33 | INFO     | app.database.minio_manager:upload_file:53 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmpssg6pmaj.pdf -> a8afbd04bd4fe4f6ab4f2a9c8736413c/4e4ae2228f2e9132cc89ed86f8ada5c3.pdf
2025-07-21 12:29:41 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: 中国科学院与青海省三江源国家公园联合研究.pdf
2025-07-21 12:29:41 | INFO     | app.service.document.document_upload:extract_document_metadata:54 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmpxmjgo8y9.pdf
2025-07-21 12:29:41 | WARNING  | app.service.document.document_upload:extract_document_metadata:81 | 不支持的文件类型: .pdf
2025-07-21 12:29:41 | INFO     | app.service.document.document_upload:extract_document_metadata:87 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmpxmjgo8y9.pdf
2025-07-21 12:29:41 | INFO     | app.service.document.document_upload:upload_document_service:181 | 成功提取元数据: 中国科学院与青海省三江源国家公园联合研究.pdf
2025-07-21 12:29:41 | INFO     | app.database.minio_manager:upload_file:53 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmpxmjgo8y9.pdf -> a8afbd04bd4fe4f6ab4f2a9c8736413c/f6480c3b03f939d84ce3963e381f9a0a.pdf
2025-07-21 12:29:48 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: 中国新闻周刊.pdf
2025-07-21 12:29:48 | INFO     | app.service.document.document_upload:extract_document_metadata:54 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmp8ysv8d_7.pdf
2025-07-21 12:29:48 | WARNING  | app.service.document.document_upload:extract_document_metadata:81 | 不支持的文件类型: .pdf
2025-07-21 12:29:48 | INFO     | app.service.document.document_upload:extract_document_metadata:87 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmp8ysv8d_7.pdf
2025-07-21 12:29:48 | INFO     | app.service.document.document_upload:upload_document_service:181 | 成功提取元数据: 中国新闻周刊.pdf
2025-07-21 12:29:48 | INFO     | app.database.minio_manager:upload_file:53 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmp8ysv8d_7.pdf -> a8afbd04bd4fe4f6ab4f2a9c8736413c/63abda32e7db0048f82841b375747933.pdf
2025-07-21 12:29:56 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: 海因里希·伯尔基金会.pdf
2025-07-21 12:29:56 | INFO     | app.service.document.document_upload:extract_document_metadata:54 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmpiff371vr.pdf
2025-07-21 12:29:56 | WARNING  | app.service.document.document_upload:extract_document_metadata:81 | 不支持的文件类型: .pdf
2025-07-21 12:29:56 | INFO     | app.service.document.document_upload:extract_document_metadata:87 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmpiff371vr.pdf
2025-07-21 12:29:56 | INFO     | app.service.document.document_upload:upload_document_service:181 | 成功提取元数据: 海因里希·伯尔基金会.pdf
2025-07-21 12:29:56 | INFO     | app.database.minio_manager:upload_file:53 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmpiff371vr.pdf -> a8afbd04bd4fe4f6ab4f2a9c8736413c/bde40b835a57d70693dc49daf0149fc8.pdf
2025-07-21 12:30:04 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: 中国科学院青藏高原研究所项目.pdf
2025-07-21 12:30:04 | INFO     | app.service.document.document_upload:extract_document_metadata:54 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmps98ivg2o.pdf
2025-07-21 12:30:04 | WARNING  | app.service.document.document_upload:extract_document_metadata:81 | 不支持的文件类型: .pdf
2025-07-21 12:30:04 | INFO     | app.service.document.document_upload:extract_document_metadata:87 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmps98ivg2o.pdf
2025-07-21 12:30:04 | INFO     | app.service.document.document_upload:upload_document_service:181 | 成功提取元数据: 中国科学院青藏高原研究所项目.pdf
2025-07-21 12:30:04 | INFO     | app.database.minio_manager:upload_file:53 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmps98ivg2o.pdf -> a8afbd04bd4fe4f6ab4f2a9c8736413c/ea2374e41ed08be067d610d70df4d197.pdf
2025-07-21 12:30:11 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: 中国科学院与青海省三江源国家公园联合研究项目.pdf
2025-07-21 12:30:11 | INFO     | app.service.document.document_upload:extract_document_metadata:54 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmpblph0ykm.pdf
2025-07-21 12:30:11 | WARNING  | app.service.document.document_upload:extract_document_metadata:81 | 不支持的文件类型: .pdf
2025-07-21 12:30:11 | INFO     | app.service.document.document_upload:extract_document_metadata:87 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmpblph0ykm.pdf
2025-07-21 12:30:11 | INFO     | app.service.document.document_upload:upload_document_service:181 | 成功提取元数据: 中国科学院与青海省三江源国家公园联合研究项目.pdf
2025-07-21 12:30:11 | INFO     | app.database.minio_manager:upload_file:53 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmpblph0ykm.pdf -> a8afbd04bd4fe4f6ab4f2a9c8736413c/c9ff2ea3ab0db4d0ecb2ca78ee77637a.pdf
2025-07-21 12:30:19 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: 国际声援新疆运动（ICT）.pdf
2025-07-21 12:30:19 | INFO     | app.service.document.document_upload:extract_document_metadata:54 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmphgve_9g_.pdf
2025-07-21 12:30:19 | WARNING  | app.service.document.document_upload:extract_document_metadata:81 | 不支持的文件类型: .pdf
2025-07-21 12:30:19 | INFO     | app.service.document.document_upload:extract_document_metadata:87 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmphgve_9g_.pdf
2025-07-21 12:30:19 | INFO     | app.service.document.document_upload:upload_document_service:181 | 成功提取元数据: 国际声援新疆运动（ICT）.pdf
2025-07-21 12:30:19 | INFO     | app.database.minio_manager:upload_file:53 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmphgve_9g_.pdf -> a8afbd04bd4fe4f6ab4f2a9c8736413c/e40a41b103ad024f2a9a1397c3a67a2e.pdf
2025-07-21 12:30:29 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: 藏人行政中央驻日本办事处.pdf
2025-07-21 12:30:29 | INFO     | app.service.document.document_upload:extract_document_metadata:54 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmptm8fqrih.pdf
2025-07-21 12:30:29 | WARNING  | app.service.document.document_upload:extract_document_metadata:81 | 不支持的文件类型: .pdf
2025-07-21 12:30:29 | INFO     | app.service.document.document_upload:extract_document_metadata:87 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmptm8fqrih.pdf
2025-07-21 12:30:29 | INFO     | app.service.document.document_upload:upload_document_service:181 | 成功提取元数据: 藏人行政中央驻日本办事处.pdf
2025-07-21 12:30:29 | INFO     | app.database.minio_manager:upload_file:53 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmptm8fqrih.pdf -> a8afbd04bd4fe4f6ab4f2a9c8736413c/e8d6a2e2da78e37a03df7981eb5e508f.pdf
2025-07-21 12:30:39 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: 藏人行政中央驻日本办事处.pdf
2025-07-21 12:30:46 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: 人权观察（HRW）报告.pdf
2025-07-21 12:30:46 | INFO     | app.service.document.document_upload:extract_document_metadata:54 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmpbpx8zhyn.pdf
2025-07-21 12:30:46 | WARNING  | app.service.document.document_upload:extract_document_metadata:81 | 不支持的文件类型: .pdf
2025-07-21 12:30:46 | INFO     | app.service.document.document_upload:extract_document_metadata:87 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmpbpx8zhyn.pdf
2025-07-21 12:30:46 | INFO     | app.service.document.document_upload:upload_document_service:181 | 成功提取元数据: 人权观察（HRW）报告.pdf
2025-07-21 12:30:46 | INFO     | app.database.minio_manager:upload_file:53 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmpbpx8zhyn.pdf -> a8afbd04bd4fe4f6ab4f2a9c8736413c/77322b717588cff77f62a90e73cc7ffd.pdf
2025-07-21 12:30:51 | INFO     | app.api.document_router:upload_document:24 | 开始处理上传文件: 美国国际战略研究中心（CSIS）报告.pdf
2025-07-21 12:30:51 | INFO     | app.service.document.document_upload:extract_document_metadata:54 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmpwopsmqku.pdf
2025-07-21 12:30:51 | WARNING  | app.service.document.document_upload:extract_document_metadata:81 | 不支持的文件类型: .pdf
2025-07-21 12:30:51 | INFO     | app.service.document.document_upload:extract_document_metadata:87 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmpwopsmqku.pdf
2025-07-21 12:30:51 | INFO     | app.service.document.document_upload:upload_document_service:181 | 成功提取元数据: 美国国际战略研究中心（CSIS）报告.pdf
2025-07-21 12:30:51 | INFO     | app.database.minio_manager:upload_file:53 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmpwopsmqku.pdf -> a8afbd04bd4fe4f6ab4f2a9c8736413c/64a918b34435b78872e26db93650895c.pdf
2025-07-21 13:09:27 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 13:09:27 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 10 个结果
2025-07-21 13:09:27 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 13:09:27 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 10 个结果
2025-07-21 13:09:28 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 13:09:28 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-21 13:09:28 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 13:09:28 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-21 13:09:28 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 13:09:28 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-21 13:09:28 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 13:09:28 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-21 13:14:14 | INFO     | __main__:lifespan:36 | RAG系统关闭中...
2025-07-21 13:14:27 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-21 13:14:32 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-21 13:14:32 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-21 13:14:32 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-21 13:14:32 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-21 13:14:32 | INFO     | __main__:lifespan:23 | RAG系统启动中...
2025-07-21 13:14:32 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-21 13:14:32 | INFO     | __main__:lifespan:27 | PostgreSQL数据库连接成功
2025-07-21 13:14:32 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-21 13:14:33 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-21 13:14:33 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-21 13:14:33 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-21 14:45:08 | INFO     | __main__:lifespan:36 | RAG系统关闭中...
2025-07-21 14:45:19 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-21 14:45:23 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-21 14:45:23 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-21 14:45:23 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-21 14:45:23 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-21 14:49:42 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-21 14:49:46 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-21 14:49:46 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-21 14:49:46 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-21 14:49:46 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-21 14:52:26 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-21 14:52:30 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-21 14:52:30 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-21 14:52:30 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-21 14:52:30 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-21 14:52:30 | INFO     | __main__:lifespan:24 | RAG系统启动中...
2025-07-21 14:52:30 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-21 14:52:30 | INFO     | __main__:lifespan:28 | PostgreSQL数据库连接成功
2025-07-21 14:52:30 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-21 14:52:39 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-21 14:52:39 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-21 14:52:39 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-21 14:52:39 | INFO     | app.service.document.async_parse_queue:start_workers:65 | 启动了 3 个工作进程
2025-07-21 14:52:40 | INFO     | app.service.document.parse_document:initialize_document_queue:223 | 文档处理队列已启动
2025-07-21 14:52:40 | ERROR    | app.service.document.async_parse_queue:_worker_loop:81 | 工作进程 worker_0 异常: value is not an integer or out of range
2025-07-21 14:52:40 | ERROR    | app.service.document.async_parse_queue:_worker_loop:81 | 工作进程 worker_1 异常: value is not an integer or out of range
2025-07-21 14:52:40 | ERROR    | app.service.document.async_parse_queue:_worker_loop:81 | 工作进程 worker_2 异常: value is not an integer or out of range
2025-07-21 14:52:45 | ERROR    | app.service.document.async_parse_queue:_worker_loop:81 | 工作进程 worker_0 异常: value is not an integer or out of range
2025-07-21 14:52:45 | ERROR    | app.service.document.async_parse_queue:_worker_loop:81 | 工作进程 worker_2 异常: value is not an integer or out of range
2025-07-21 14:52:45 | ERROR    | app.service.document.async_parse_queue:_worker_loop:81 | 工作进程 worker_1 异常: value is not an integer or out of range
2025-07-21 14:52:50 | ERROR    | app.service.document.async_parse_queue:_worker_loop:81 | 工作进程 worker_1 异常: value is not an integer or out of range
2025-07-21 14:52:50 | ERROR    | app.service.document.async_parse_queue:_worker_loop:81 | 工作进程 worker_2 异常: value is not an integer or out of range
2025-07-21 14:52:50 | ERROR    | app.service.document.async_parse_queue:_worker_loop:81 | 工作进程 worker_0 异常: value is not an integer or out of range
2025-07-21 14:52:50 | INFO     | __main__:lifespan:39 | RAG系统关闭中...
2025-07-21 14:53:16 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-21 14:53:20 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-21 14:53:20 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-21 14:53:20 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-21 14:53:20 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-21 14:53:20 | INFO     | __main__:lifespan:24 | RAG系统启动中...
2025-07-21 14:53:20 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-21 14:53:20 | INFO     | __main__:lifespan:28 | PostgreSQL数据库连接成功
2025-07-21 14:53:20 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-21 14:53:22 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-21 14:53:22 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-21 14:53:22 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-21 14:53:22 | INFO     | app.service.document.async_parse_queue:start_workers:65 | 启动了 3 个工作进程
2025-07-21 14:53:22 | INFO     | app.service.document.parse_document:initialize_document_queue:223 | 文档处理队列已启动
2025-07-21 15:09:42 | INFO     | app.service.document.async_parse_queue:add_task:55 | 任务已加入队列: 1f4d7054-8d67-4ce4-969e-bf34cb3e41b9 (file_hash: c9ff2ea3ab0db4d0ecb2ca78ee77637a)
2025-07-21 15:09:42 | INFO     | app.service.document.async_parse_queue:_process_task:118 | 工作进程 worker_1 开始处理任务: 1f4d7054-8d67-4ce4-969e-bf34cb3e41b9
2025-07-21 15:09:43 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-21 15:09:43 | INFO     | app.service.document.document_processor:_parse_document:210 | 开始解析文件: c9ff2ea3ab0db4d0ecb2ca78ee77637a.pdf
2025-07-21 15:11:52 | ERROR    | app.service.document.document_processor:process_document:142 | 文档处理失败 c9ff2ea3ab0db4d0ecb2ca78ee77637a: DocumentProcessor._process_chunks() missing 1 required positional argument: 'processed_status'
2025-07-21 15:11:52 | ERROR    | app.service.document.async_parse_queue:_complete_task:157 | 任务处理失败: 1f4d7054-8d67-4ce4-969e-bf34cb3e41b9, 错误: None
2025-07-21 15:16:58 | INFO     | __main__:lifespan:39 | RAG系统关闭中...
2025-07-21 15:17:13 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-21 15:17:18 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-21 15:17:18 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-21 15:17:18 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-21 15:17:18 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-21 15:17:18 | INFO     | __main__:lifespan:24 | RAG系统启动中...
2025-07-21 15:17:18 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-21 15:17:18 | INFO     | __main__:lifespan:28 | PostgreSQL数据库连接成功
2025-07-21 15:17:18 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-21 15:17:20 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-21 15:17:22 | WARNING  | app.service.model_client:check_health:83 | Embedding 模型服务不可用
2025-07-21 15:17:22 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-21 15:17:22 | INFO     | app.service.document.async_parse_queue:start_workers:65 | 启动了 3 个工作进程
2025-07-21 15:17:22 | INFO     | app.service.document.parse_document:initialize_document_queue:223 | 文档处理队列已启动
2025-07-21 15:17:24 | INFO     | app.service.document.async_parse_queue:add_task:55 | 任务已加入队列: 920146e4-2ab9-48c3-9b99-3fedf7955e7e (file_hash: c9ff2ea3ab0db4d0ecb2ca78ee77637a)
2025-07-21 15:17:25 | INFO     | app.service.document.async_parse_queue:_process_task:118 | 工作进程 worker_1 开始处理任务: 920146e4-2ab9-48c3-9b99-3fedf7955e7e
2025-07-21 15:17:26 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-21 15:17:26 | INFO     | app.service.document.document_processor:_parse_document:214 | 开始解析文件: c9ff2ea3ab0db4d0ecb2ca78ee77637a.pdf
2025-07-21 15:19:22 | INFO     | app.service.document.document_processor:process_document:124 | 开始分块: c9ff2ea3ab0db4d0ecb2ca78ee77637a.pdf
2025-07-21 15:19:27 | INFO     | app.service.document.document_processor:process_document:128 | 共有141 片
2025-07-21 15:19:35 | ERROR    | app.service.model_client:get_embeddings:133 | 嵌入生成失败: <!DOCTYPE html>
<html>
<head>
<title>Error</title>
<style>
html { color-scheme: light dark; }
body { width: 35em; margin: 0 auto;
font-family: Tahoma, Verdana, Arial, sans-serif; }
</style>
</head>
<body>
<h1>An error occurred.</h1>
<p>Sorry, the page you are looking for is currently unavailable.<br/>
Please try again later.</p>
<p>If you are the system administrator of this resource then you should check
the error log for details.</p>
<p><em>Faithfully yours, nginx.</em></p>
</body>
</html>
2025-07-21 15:19:35 | ERROR    | app.utils.exceptions:wrapper:85 | get_embeddings RAG系统异常: 嵌入生成失败: <!DOCTYPE html>
<html>
<head>
<title>Error</title>
<style>
html { color-scheme: light dark; }
body { width: 35em; margin: 0 auto;
font-family: Tahoma, Verdana, Arial, sans-serif; }
</style>
</head>
<body>
<h1>An error occurred.</h1>
<p>Sorry, the page you are looking for is currently unavailable.<br/>
Please try again later.</p>
<p>If you are the system administrator of this resource then you should check
the error log for details.</p>
<p><em>Faithfully yours, nginx.</em></p>
</body>
</html>
2025-07-21 15:19:35 | ERROR    | app.service.document.document_processor:process_document:146 | 文档处理失败 c9ff2ea3ab0db4d0ecb2ca78ee77637a: 嵌入生成失败: <!DOCTYPE html>
<html>
<head>
<title>Error</title>
<style>
html { color-scheme: light dark; }
body { width: 35em; margin: 0 auto;
font-family: Tahoma, Verdana, Arial, sans-serif; }
</style>
</head>
<body>
<h1>An error occurred.</h1>
<p>Sorry, the page you are looking for is currently unavailable.<br/>
Please try again later.</p>
<p>If you are the system administrator of this resource then you should check
the error log for details.</p>
<p><em>Faithfully yours, nginx.</em></p>
</body>
</html>
2025-07-21 15:19:35 | ERROR    | app.service.document.async_parse_queue:_complete_task:157 | 任务处理失败: 920146e4-2ab9-48c3-9b99-3fedf7955e7e, 错误: None
2025-07-21 15:19:35 | ERROR    | app.service.model_client:get_embeddings:133 | 嵌入生成失败: <!DOCTYPE html>
<html>
<head>
<title>Error</title>
<style>
html { color-scheme: light dark; }
body { width: 35em; margin: 0 auto;
font-family: Tahoma, Verdana, Arial, sans-serif; }
</style>
</head>
<body>
<h1>An error occurred.</h1>
<p>Sorry, the page you are looking for is currently unavailable.<br/>
Please try again later.</p>
<p>If you are the system administrator of this resource then you should check
the error log for details.</p>
<p><em>Faithfully yours, nginx.</em></p>
</body>
</html>
2025-07-21 15:19:35 | ERROR    | app.utils.exceptions:wrapper:85 | get_embeddings RAG系统异常: 嵌入生成失败: <!DOCTYPE html>
<html>
<head>
<title>Error</title>
<style>
html { color-scheme: light dark; }
body { width: 35em; margin: 0 auto;
font-family: Tahoma, Verdana, Arial, sans-serif; }
</style>
</head>
<body>
<h1>An error occurred.</h1>
<p>Sorry, the page you are looking for is currently unavailable.<br/>
Please try again later.</p>
<p>If you are the system administrator of this resource then you should check
the error log for details.</p>
<p><em>Faithfully yours, nginx.</em></p>
</body>
</html>
2025-07-21 15:19:35 | ERROR    | app.service.model_client:get_embeddings:133 | 嵌入生成失败: <!DOCTYPE html>
<html>
<head>
<title>Error</title>
<style>
html { color-scheme: light dark; }
body { width: 35em; margin: 0 auto;
font-family: Tahoma, Verdana, Arial, sans-serif; }
</style>
</head>
<body>
<h1>An error occurred.</h1>
<p>Sorry, the page you are looking for is currently unavailable.<br/>
Please try again later.</p>
<p>If you are the system administrator of this resource then you should check
the error log for details.</p>
<p><em>Faithfully yours, nginx.</em></p>
</body>
</html>
2025-07-21 15:19:35 | ERROR    | app.utils.exceptions:wrapper:85 | get_embeddings RAG系统异常: 嵌入生成失败: <!DOCTYPE html>
<html>
<head>
<title>Error</title>
<style>
html { color-scheme: light dark; }
body { width: 35em; margin: 0 auto;
font-family: Tahoma, Verdana, Arial, sans-serif; }
</style>
</head>
<body>
<h1>An error occurred.</h1>
<p>Sorry, the page you are looking for is currently unavailable.<br/>
Please try again later.</p>
<p>If you are the system administrator of this resource then you should check
the error log for details.</p>
<p><em>Faithfully yours, nginx.</em></p>
</body>
</html>
2025-07-21 15:19:35 | ERROR    | app.service.model_client:get_embeddings:133 | 嵌入生成失败: <!DOCTYPE html>
<html>
<head>
<title>Error</title>
<style>
html { color-scheme: light dark; }
body { width: 35em; margin: 0 auto;
font-family: Tahoma, Verdana, Arial, sans-serif; }
</style>
</head>
<body>
<h1>An error occurred.</h1>
<p>Sorry, the page you are looking for is currently unavailable.<br/>
Please try again later.</p>
<p>If you are the system administrator of this resource then you should check
the error log for details.</p>
<p><em>Faithfully yours, nginx.</em></p>
</body>
</html>
2025-07-21 15:19:35 | ERROR    | app.utils.exceptions:wrapper:85 | get_embeddings RAG系统异常: 嵌入生成失败: <!DOCTYPE html>
<html>
<head>
<title>Error</title>
<style>
html { color-scheme: light dark; }
body { width: 35em; margin: 0 auto;
font-family: Tahoma, Verdana, Arial, sans-serif; }
</style>
</head>
<body>
<h1>An error occurred.</h1>
<p>Sorry, the page you are looking for is currently unavailable.<br/>
Please try again later.</p>
<p>If you are the system administrator of this resource then you should check
the error log for details.</p>
<p><em>Faithfully yours, nginx.</em></p>
</body>
</html>
2025-07-21 15:19:35 | ERROR    | app.service.model_client:get_embeddings:133 | 嵌入生成失败: <!DOCTYPE html>
<html>
<head>
<title>Error</title>
<style>
html { color-scheme: light dark; }
body { width: 35em; margin: 0 auto;
font-family: Tahoma, Verdana, Arial, sans-serif; }
</style>
</head>
<body>
<h1>An error occurred.</h1>
<p>Sorry, the page you are looking for is currently unavailable.<br/>
Please try again later.</p>
<p>If you are the system administrator of this resource then you should check
the error log for details.</p>
<p><em>Faithfully yours, nginx.</em></p>
</body>
</html>
2025-07-21 15:19:35 | ERROR    | app.utils.exceptions:wrapper:85 | get_embeddings RAG系统异常: 嵌入生成失败: <!DOCTYPE html>
<html>
<head>
<title>Error</title>
<style>
html { color-scheme: light dark; }
body { width: 35em; margin: 0 auto;
font-family: Tahoma, Verdana, Arial, sans-serif; }
</style>
</head>
<body>
<h1>An error occurred.</h1>
<p>Sorry, the page you are looking for is currently unavailable.<br/>
Please try again later.</p>
<p>If you are the system administrator of this resource then you should check
the error log for details.</p>
<p><em>Faithfully yours, nginx.</em></p>
</body>
</html>
2025-07-21 15:22:12 | INFO     | app.service.document.async_parse_queue:add_task:55 | 任务已加入队列: cccc21cc-9639-4afd-b2d1-f68937f27a85 (file_hash: c9ff2ea3ab0db4d0ecb2ca78ee77637a)
2025-07-21 15:22:12 | INFO     | app.service.document.async_parse_queue:_process_task:118 | 工作进程 worker_2 开始处理任务: cccc21cc-9639-4afd-b2d1-f68937f27a85
2025-07-21 15:22:13 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-21 15:22:13 | INFO     | app.service.document.document_processor:_parse_document:214 | 开始解析文件: c9ff2ea3ab0db4d0ecb2ca78ee77637a.pdf
2025-07-21 15:23:24 | INFO     | app.service.document.document_processor:process_document:124 | 开始分块: c9ff2ea3ab0db4d0ecb2ca78ee77637a.pdf
2025-07-21 15:23:28 | INFO     | app.service.document.document_processor:process_document:128 | 共有141 片
2025-07-21 15:23:30 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:32 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:32 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:32 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:32 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:32 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:32 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:32 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:32 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:32 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:32 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:33 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:33 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:33 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:33 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:33 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:34 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:34 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:34 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:34 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:34 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:35 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:35 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:35 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:35 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:35 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:35 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:36 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:36 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:36 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:36 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:45 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:45 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:45 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:45 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:45 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:46 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:46 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:46 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:46 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:46 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:46 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:48 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:48 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:48 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:48 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:48 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:48 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:51 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:51 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:51 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:51 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:51 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:23:53 | INFO     | app.service.document.document_processor:process_document:132 | 向量化开始
2025-07-21 15:23:54 | INFO     | app.database.qdrant_manager:_create_collection_if_not_exists:63 | 创建Qdrant集合: a8afbd04bd4fe4f6ab4f2a9c8736413c
2025-07-21 15:23:54 | INFO     | app.database.qdrant_manager:store_vectors:85 | 开始插入 141 个向量，批次大小: 100
2025-07-21 15:23:54 | INFO     | app.database.qdrant_manager:store_vectors:94 | 成功插入批次，大小: 100
2025-07-21 15:23:54 | INFO     | app.database.qdrant_manager:store_vectors:94 | 成功插入批次，大小: 41
2025-07-21 15:23:54 | INFO     | app.database.qdrant_manager:store_vectors:102 | 向量插入完成，总耗时: 1.02s
2025-07-21 15:23:54 | INFO     | app.service.document.document_processor:process_document:136 | 摘要生成开始
2025-07-21 15:24:18 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 5005
2025-07-21 15:24:19 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 4861
2025-07-21 15:24:19 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 3675
2025-07-21 15:24:28 | INFO     | app.service.model_client:_generate_final_summary:283 | 最终摘要生成成功，总分片数: 3
2025-07-21 15:24:28 | INFO     | app.service.document.document_processor:_finalize_processing:245 | 摘要生成完成: c9ff2ea3ab0db4d0ecb2ca78ee77637a.pdf
2025-07-21 15:24:28 | INFO     | app.service.document.document_processor:process_document:142 | 文档处理完成: c9ff2ea3ab0db4d0ecb2ca78ee77637a.pdf
2025-07-21 15:24:28 | INFO     | app.service.document.async_parse_queue:_complete_task:155 | 任务处理成功: cccc21cc-9639-4afd-b2d1-f68937f27a85
2025-07-21 15:26:21 | INFO     | app.service.document.async_parse_queue:add_task:55 | 任务已加入队列: 2a291b80-1616-48f0-b645-b6fbc9d63e18 (file_hash: 64a918b34435b78872e26db93650895c)
2025-07-21 15:26:21 | INFO     | app.service.document.async_parse_queue:add_task:55 | 任务已加入队列: 5b1709e1-2278-49a7-a650-3b2a6e89e63d (file_hash: 77322b717588cff77f62a90e73cc7ffd)
2025-07-21 15:26:22 | INFO     | app.service.document.async_parse_queue:_process_task:118 | 工作进程 worker_0 开始处理任务: 5b1709e1-2278-49a7-a650-3b2a6e89e63d
2025-07-21 15:26:22 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-21 15:26:22 | INFO     | app.service.document.async_parse_queue:_process_task:118 | 工作进程 worker_1 开始处理任务: 2a291b80-1616-48f0-b645-b6fbc9d63e18
2025-07-21 15:26:22 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-21 15:26:23 | INFO     | app.service.document.document_processor:_parse_document:214 | 开始解析文件: 77322b717588cff77f62a90e73cc7ffd.pdf
2025-07-21 15:30:32 | ERROR    | app.service.document.document_processor:process_document:146 | 文档处理失败 64a918b34435b78872e26db93650895c: 
2025-07-21 15:30:32 | INFO     | app.service.document.document_processor:process_document:124 | 开始分块: 77322b717588cff77f62a90e73cc7ffd.pdf
2025-07-21 15:30:38 | INFO     | app.service.document.async_parse_queue:add_task:55 | 任务已加入队列: bf63259f-6cba-4b92-8fc7-f811839acf7b (file_hash: ea2374e41ed08be067d610d70df4d197)
2025-07-21 15:30:38 | INFO     | app.service.document.async_parse_queue:add_task:55 | 任务已加入队列: 48f3d650-2220-4d80-853d-6d0ae798607d (file_hash: ea2374e41ed08be067d610d70df4d197)
2025-07-21 15:30:38 | INFO     | app.service.document.document_processor:process_document:128 | 共有313 片
2025-07-21 15:30:38 | ERROR    | app.service.document.async_parse_queue:_complete_task:157 | 任务处理失败: 2a291b80-1616-48f0-b645-b6fbc9d63e18, 错误: None
2025-07-21 15:30:38 | INFO     | app.service.document.async_parse_queue:_process_task:118 | 工作进程 worker_1 开始处理任务: bf63259f-6cba-4b92-8fc7-f811839acf7b
2025-07-21 15:30:39 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-21 15:30:39 | INFO     | app.service.document.async_parse_queue:add_task:55 | 任务已加入队列: d3662f5c-cedb-4613-9094-bf1047de7568 (file_hash: bde40b835a57d70693dc49daf0149fc8)
2025-07-21 15:30:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:30:39 | INFO     | app.service.document.document_processor:_parse_document:214 | 开始解析文件: ea2374e41ed08be067d610d70df4d197.pdf
2025-07-21 15:31:10 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:10 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:10 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:10 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:10 | INFO     | app.service.document.async_parse_queue:_process_task:118 | 工作进程 worker_2 开始处理任务: d3662f5c-cedb-4613-9094-bf1047de7568
2025-07-21 15:31:10 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-21 15:31:10 | INFO     | app.service.document.document_processor:process_document:124 | 开始分块: ea2374e41ed08be067d610d70df4d197.pdf
2025-07-21 15:31:12 | INFO     | app.service.document.document_processor:process_document:128 | 共有58 片
2025-07-21 15:31:12 | INFO     | app.service.document.document_processor:_parse_document:214 | 开始解析文件: bde40b835a57d70693dc49daf0149fc8.pdf
2025-07-21 15:31:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:43 | INFO     | app.service.document.document_processor:process_document:124 | 开始分块: bde40b835a57d70693dc49daf0149fc8.pdf
2025-07-21 15:31:43 | INFO     | app.service.document.document_processor:process_document:128 | 共有33 片
2025-07-21 15:31:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:45 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:57 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:57 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:57 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:57 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:57 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:57 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:57 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:57 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:57 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:57 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:57 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:57 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:57 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:57 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:57 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:31:59 | INFO     | app.service.document.document_processor:process_document:132 | 向量化开始
2025-07-21 15:31:59 | INFO     | app.database.qdrant_manager:store_vectors:85 | 开始插入 33 个向量，批次大小: 100
2025-07-21 15:31:59 | INFO     | app.database.qdrant_manager:store_vectors:94 | 成功插入批次，大小: 33
2025-07-21 15:31:59 | INFO     | app.database.qdrant_manager:store_vectors:102 | 向量插入完成，总耗时: 0.21s
2025-07-21 15:31:59 | INFO     | app.service.document.document_processor:process_document:136 | 摘要生成开始
2025-07-21 15:31:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:00 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:00 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:00 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:00 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:00 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:00 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:00 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:00 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:00 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:01 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:02 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:02 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:02 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:02 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:02 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:02 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:02 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:02 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:02 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:03 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:04 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:04 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:04 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:04 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:04 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:04 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:04 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:04 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:04 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:05 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:06 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:06 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:06 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:06 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:06 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:06 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:06 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:06 | INFO     | app.service.document.document_processor:process_document:132 | 向量化开始
2025-07-21 15:32:06 | INFO     | app.database.qdrant_manager:store_vectors:85 | 开始插入 58 个向量，批次大小: 100
2025-07-21 15:32:06 | INFO     | app.database.qdrant_manager:store_vectors:94 | 成功插入批次，大小: 58
2025-07-21 15:32:06 | INFO     | app.database.qdrant_manager:store_vectors:102 | 向量插入完成，总耗时: 0.31s
2025-07-21 15:32:06 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:06 | INFO     | app.service.document.document_processor:process_document:136 | 摘要生成开始
2025-07-21 15:32:07 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:07 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:07 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:07 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:07 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:08 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:08 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:08 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:08 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:08 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:09 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:09 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:09 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:09 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:09 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:10 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:10 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:10 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:10 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:10 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:10 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:10 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:11 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:11 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:11 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:11 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:12 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:12 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:12 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:12 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:13 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:13 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:13 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:13 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:13 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:14 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:14 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:14 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:14 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:14 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:15 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:15 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:15 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:15 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:15 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:16 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:16 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:16 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:16 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:16 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:17 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:17 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:17 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:17 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:17 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:18 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:18 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:18 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:18 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:18 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:18 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:18 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:18 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:18 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:19 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:19 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:19 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:19 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:19 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:20 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:20 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:20 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:20 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:20 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:21 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:21 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:21 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:21 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:21 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:22 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:22 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:22 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:22 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:22 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:23 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:23 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:23 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:23 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:23 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:24 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:24 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:24 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:24 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:24 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:25 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:25 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:25 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:25 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:25 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:26 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:26 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:26 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:26 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:26 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:27 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:27 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:27 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:27 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:27 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:28 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:28 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:28 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:28 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:28 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:28 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:29 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:29 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:29 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:29 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:29 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:30 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:30 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:30 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:30 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:30 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:32 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:32 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:32 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:32 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:32 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:32 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:33 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:33 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:33 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:33 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:33 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:33 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:33 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:33 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:34 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:34 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:34 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:34 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:34 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:35 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:35 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:35 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:35 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:35 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:35 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:36 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:36 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:36 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:36 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:36 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:43 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 574
2025-07-21 15:32:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:45 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:45 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:45 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:45 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:45 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:45 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:46 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:46 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:46 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:46 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:46 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:48 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:48 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:48 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:48 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:48 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:49 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 4707
2025-07-21 15:32:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:51 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:51 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:51 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:51 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:51 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:51 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:51 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:32:52 | INFO     | app.service.document.document_processor:process_document:132 | 向量化开始
2025-07-21 15:32:52 | INFO     | app.database.qdrant_manager:store_vectors:85 | 开始插入 313 个向量，批次大小: 100
2025-07-21 15:32:52 | INFO     | app.database.qdrant_manager:store_vectors:94 | 成功插入批次，大小: 100
2025-07-21 15:32:52 | INFO     | app.database.qdrant_manager:store_vectors:94 | 成功插入批次，大小: 100
2025-07-21 15:32:53 | INFO     | app.database.qdrant_manager:store_vectors:94 | 成功插入批次，大小: 100
2025-07-21 15:32:53 | INFO     | app.database.qdrant_manager:store_vectors:94 | 成功插入批次，大小: 13
2025-07-21 15:32:53 | INFO     | app.database.qdrant_manager:store_vectors:102 | 向量插入完成，总耗时: 1.56s
2025-07-21 15:32:53 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 3754
2025-07-21 15:32:53 | INFO     | app.service.document.document_processor:process_document:136 | 摘要生成开始
2025-07-21 15:32:53 | INFO     | app.service.document.document_processor:_finalize_processing:245 | 摘要生成完成: bde40b835a57d70693dc49daf0149fc8.pdf
2025-07-21 15:32:53 | INFO     | app.service.document.document_processor:process_document:142 | 文档处理完成: bde40b835a57d70693dc49daf0149fc8.pdf
2025-07-21 15:32:53 | INFO     | app.service.document.async_parse_queue:_complete_task:155 | 任务处理成功: d3662f5c-cedb-4613-9094-bf1047de7568
2025-07-21 15:32:53 | INFO     | app.service.document.async_parse_queue:_process_task:118 | 工作进程 worker_2 开始处理任务: 48f3d650-2220-4d80-853d-6d0ae798607d
2025-07-21 15:32:54 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-21 15:32:54 | INFO     | app.service.document.document_processor:_parse_document:214 | 开始解析文件: ea2374e41ed08be067d610d70df4d197.pdf
2025-07-21 15:33:28 | INFO     | app.service.model_client:_generate_final_summary:283 | 最终摘要生成成功，总分片数: 2
2025-07-21 15:33:28 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 4758
2025-07-21 15:33:28 | INFO     | app.service.document.document_processor:process_document:124 | 开始分块: ea2374e41ed08be067d610d70df4d197.pdf
2025-07-21 15:33:30 | INFO     | app.service.document.document_processor:_finalize_processing:245 | 摘要生成完成: ea2374e41ed08be067d610d70df4d197.pdf
2025-07-21 15:33:30 | INFO     | app.service.document.document_processor:process_document:142 | 文档处理完成: ea2374e41ed08be067d610d70df4d197.pdf
2025-07-21 15:33:30 | INFO     | app.service.document.document_processor:process_document:128 | 共有58 片
2025-07-21 15:33:30 | INFO     | app.service.document.async_parse_queue:_complete_task:155 | 任务处理成功: bf63259f-6cba-4b92-8fc7-f811839acf7b
2025-07-21 15:33:30 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:32 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:32 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:32 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:32 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:32 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:33 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:33 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:33 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:33 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:33 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:34 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:34 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:34 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:34 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:34 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:35 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:35 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:35 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:35 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:35 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:36 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:36 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:36 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:36 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:36 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 15:33:41 | INFO     | app.service.document.document_processor:process_document:132 | 向量化开始
2025-07-21 15:33:41 | INFO     | app.database.qdrant_manager:store_vectors:85 | 开始插入 58 个向量，批次大小: 100
2025-07-21 15:33:41 | INFO     | app.database.qdrant_manager:store_vectors:94 | 成功插入批次，大小: 58
2025-07-21 15:33:41 | INFO     | app.database.qdrant_manager:store_vectors:102 | 向量插入完成，总耗时: 0.35s
2025-07-21 15:33:41 | INFO     | app.service.document.document_processor:process_document:136 | 摘要生成开始
2025-07-21 15:33:42 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 4674
2025-07-21 15:33:50 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 5040
2025-07-21 15:33:56 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 4788
2025-07-21 15:34:01 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 2015
2025-07-21 15:34:02 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 574
2025-07-21 15:34:03 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 5236
2025-07-21 15:34:06 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 5078
2025-07-21 15:34:08 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 4707
2025-07-21 15:34:14 | INFO     | app.service.model_client:_generate_final_summary:283 | 最终摘要生成成功，总分片数: 7
2025-07-21 15:34:14 | INFO     | app.service.document.document_processor:_finalize_processing:245 | 摘要生成完成: 77322b717588cff77f62a90e73cc7ffd.pdf
2025-07-21 15:34:14 | INFO     | app.service.document.document_processor:process_document:142 | 文档处理完成: 77322b717588cff77f62a90e73cc7ffd.pdf
2025-07-21 15:34:14 | INFO     | app.service.document.async_parse_queue:_complete_task:155 | 任务处理成功: 5b1709e1-2278-49a7-a650-3b2a6e89e63d
2025-07-21 15:34:17 | INFO     | app.service.model_client:_generate_final_summary:283 | 最终摘要生成成功，总分片数: 2
2025-07-21 15:34:17 | INFO     | app.service.document.document_processor:_finalize_processing:245 | 摘要生成完成: ea2374e41ed08be067d610d70df4d197.pdf
2025-07-21 15:34:17 | INFO     | app.service.document.document_processor:process_document:142 | 文档处理完成: ea2374e41ed08be067d610d70df4d197.pdf
2025-07-21 15:34:17 | INFO     | app.service.document.async_parse_queue:_complete_task:155 | 任务处理成功: 48f3d650-2220-4d80-853d-6d0ae798607d
2025-07-21 16:33:45 | INFO     | app.service.document.async_parse_queue:add_task:55 | 任务已加入队列: 3d4ede5c-be20-4f6a-93a1-9e1f96f4c43c (file_hash: 993cfd22e465111c6d9afb23a6210831)
2025-07-21 16:33:45 | INFO     | app.service.document.async_parse_queue:add_task:55 | 任务已加入队列: dafac2d9-b88e-4d91-86f9-d5be977d1b05 (file_hash: d03b5aa3c5ccbc109869cd0e4905e3a0)
2025-07-21 16:33:45 | INFO     | app.service.document.async_parse_queue:add_task:55 | 任务已加入队列: 8b63630b-e6b5-46e9-b96a-dc020283a1eb (file_hash: 2f485756545989422ab90ad410aff89a)
2025-07-21 16:33:45 | INFO     | app.service.document.async_parse_queue:add_task:55 | 任务已加入队列: bc148dae-21e0-4787-8c86-49c7c1baa6e2 (file_hash: 4b5d3036d307179b979f4e265767c54f)
2025-07-21 16:33:45 | INFO     | app.service.document.async_parse_queue:_process_task:118 | 工作进程 worker_0 开始处理任务: dafac2d9-b88e-4d91-86f9-d5be977d1b05
2025-07-21 16:33:46 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-21 16:33:46 | INFO     | app.service.document.async_parse_queue:_process_task:118 | 工作进程 worker_2 开始处理任务: bc148dae-21e0-4787-8c86-49c7c1baa6e2
2025-07-21 16:33:46 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-21 16:33:46 | INFO     | app.service.document.async_parse_queue:_process_task:118 | 工作进程 worker_1 开始处理任务: 8b63630b-e6b5-46e9-b96a-dc020283a1eb
2025-07-21 16:33:47 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-21 16:33:47 | INFO     | app.service.document.document_processor:_parse_document:214 | 开始解析文件: d03b5aa3c5ccbc109869cd0e4905e3a0.pdf
2025-07-21 16:34:44 | INFO     | app.service.document.document_processor:process_document:124 | 开始分块: d03b5aa3c5ccbc109869cd0e4905e3a0.pdf
2025-07-21 16:34:47 | INFO     | app.service.document.document_processor:process_document:128 | 共有115 片
2025-07-21 16:34:47 | INFO     | app.service.document.document_processor:_parse_document:214 | 开始解析文件: 2f485756545989422ab90ad410aff89a.pdf
2025-07-21 16:36:41 | INFO     | app.service.document.document_processor:_parse_document:214 | 开始解析文件: 4b5d3036d307179b979f4e265767c54f.pdf
2025-07-21 16:37:38 | INFO     | app.service.document.document_processor:process_document:124 | 开始分块: 2f485756545989422ab90ad410aff89a.pdf
2025-07-21 16:37:41 | INFO     | app.service.document.document_processor:process_document:124 | 开始分块: 4b5d3036d307179b979f4e265767c54f.pdf
2025-07-21 16:37:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:41 | INFO     | app.service.document.async_parse_queue:add_task:55 | 任务已加入队列: 7ba8d4e6-8b62-40bc-8691-aebcab345468 (file_hash: 993cfd22e465111c6d9afb23a6210831)
2025-07-21 16:37:41 | INFO     | app.service.document.document_processor:process_document:128 | 共有98 片
2025-07-21 16:37:41 | INFO     | app.service.document.document_processor:process_document:128 | 共有75 片
2025-07-21 16:37:41 | INFO     | app.service.document.async_parse_queue:add_task:55 | 任务已加入队列: 8a2e89d7-e184-4974-88a7-3760a453f319 (file_hash: d03b5aa3c5ccbc109869cd0e4905e3a0)
2025-07-21 16:37:41 | INFO     | app.service.document.async_parse_queue:add_task:55 | 任务已加入队列: 2e4624d3-3d7f-4580-84ff-2eb54d4f4632 (file_hash: 2f485756545989422ab90ad410aff89a)
2025-07-21 16:37:41 | INFO     | app.service.document.async_parse_queue:add_task:55 | 任务已加入队列: 38b837f5-f819-499d-938b-5a56093243ed (file_hash: 4b5d3036d307179b979f4e265767c54f)
2025-07-21 16:37:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:45 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:45 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:45 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:45 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:45 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:45 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:45 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:45 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:45 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:45 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:45 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:45 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:45 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:45 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:45 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:46 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:46 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:46 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:46 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:46 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:46 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:46 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:46 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:46 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:46 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:46 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:46 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:46 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:46 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:46 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:46 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:48 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:48 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:48 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:48 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:48 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:48 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:48 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:48 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:48 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:48 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:48 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:48 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:48 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:48 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:48 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:51 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:51 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:51 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:51 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:51 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:51 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:51 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:51 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:51 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:51 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:51 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:51 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:51 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:51 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:51 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:51 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:51 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:55 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:55 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:55 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:55 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:55 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:55 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:55 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:55 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:55 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:55 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:55 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:56 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:56 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:56 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:56 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:56 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:56 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:56 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:56 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:56 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:56 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:56 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:56 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:56 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:56 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:56 | INFO     | app.service.document.document_processor:process_document:132 | 向量化开始
2025-07-21 16:37:56 | INFO     | app.database.qdrant_manager:store_vectors:85 | 开始插入 75 个向量，批次大小: 100
2025-07-21 16:37:56 | INFO     | app.database.qdrant_manager:store_vectors:94 | 成功插入批次，大小: 75
2025-07-21 16:37:56 | INFO     | app.database.qdrant_manager:store_vectors:102 | 向量插入完成，总耗时: 0.37s
2025-07-21 16:37:56 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:56 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:56 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:56 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:56 | INFO     | app.service.document.document_processor:process_document:136 | 摘要生成开始
2025-07-21 16:37:56 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:57 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:57 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:57 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:57 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:57 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:57 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:37:59 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:38:01 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:38:01 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:38:01 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:38:01 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:38:01 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:38:01 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:38:01 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:38:01 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:38:01 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:38:01 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:38:02 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:38:02 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:38:02 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:38:02 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:38:02 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:38:02 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:38:02 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:38:02 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:38:02 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:38:03 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:38:04 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:38:04 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:38:04 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:38:04 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:38:04 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:38:04 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:38:04 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:38:04 | INFO     | app.service.document.document_processor:process_document:132 | 向量化开始
2025-07-21 16:38:04 | INFO     | app.database.qdrant_manager:store_vectors:85 | 开始插入 98 个向量，批次大小: 100
2025-07-21 16:38:05 | INFO     | app.database.qdrant_manager:store_vectors:94 | 成功插入批次，大小: 98
2025-07-21 16:38:05 | INFO     | app.database.qdrant_manager:store_vectors:102 | 向量插入完成，总耗时: 0.42s
2025-07-21 16:38:05 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:38:05 | INFO     | app.service.document.document_processor:process_document:136 | 摘要生成开始
2025-07-21 16:38:05 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:38:05 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:38:05 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:38:05 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:38:05 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:38:06 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:38:06 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:38:06 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:38:06 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:38:06 | INFO     | app.service.document.document_processor:process_document:132 | 向量化开始
2025-07-21 16:38:06 | INFO     | app.database.qdrant_manager:store_vectors:85 | 开始插入 115 个向量，批次大小: 100
2025-07-21 16:38:07 | INFO     | app.database.qdrant_manager:store_vectors:94 | 成功插入批次，大小: 100
2025-07-21 16:38:07 | INFO     | app.database.qdrant_manager:store_vectors:94 | 成功插入批次，大小: 15
2025-07-21 16:38:07 | INFO     | app.database.qdrant_manager:store_vectors:102 | 向量插入完成，总耗时: 0.52s
2025-07-21 16:38:07 | INFO     | app.service.document.document_processor:process_document:136 | 摘要生成开始
2025-07-21 16:38:35 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 1312
2025-07-21 16:38:38 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 1721
2025-07-21 16:38:41 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 5224
2025-07-21 16:38:44 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 7084
2025-07-21 16:38:47 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 3903
2025-07-21 16:38:52 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 4562
2025-07-21 16:38:52 | INFO     | app.service.model_client:_generate_final_summary:283 | 最终摘要生成成功，总分片数: 2
2025-07-21 16:38:52 | INFO     | app.service.document.document_processor:_finalize_processing:245 | 摘要生成完成: 4b5d3036d307179b979f4e265767c54f.pdf
2025-07-21 16:38:52 | INFO     | app.service.document.document_processor:process_document:142 | 文档处理完成: 4b5d3036d307179b979f4e265767c54f.pdf
2025-07-21 16:38:52 | INFO     | app.service.document.async_parse_queue:_complete_task:155 | 任务处理成功: bc148dae-21e0-4787-8c86-49c7c1baa6e2
2025-07-21 16:38:52 | INFO     | app.service.document.async_parse_queue:_process_task:118 | 工作进程 worker_2 开始处理任务: 8a2e89d7-e184-4974-88a7-3760a453f319
2025-07-21 16:38:53 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-21 16:38:53 | INFO     | app.service.document.document_processor:_parse_document:214 | 开始解析文件: d03b5aa3c5ccbc109869cd0e4905e3a0.pdf
2025-07-21 16:39:52 | INFO     | app.service.model_client:_generate_final_summary:283 | 最终摘要生成成功，总分片数: 2
2025-07-21 16:39:52 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 4354
2025-07-21 16:39:52 | INFO     | app.service.document.document_processor:process_document:124 | 开始分块: d03b5aa3c5ccbc109869cd0e4905e3a0.pdf
2025-07-21 16:39:57 | INFO     | app.service.document.document_processor:_finalize_processing:245 | 摘要生成完成: 2f485756545989422ab90ad410aff89a.pdf
2025-07-21 16:39:57 | INFO     | app.service.document.document_processor:process_document:142 | 文档处理完成: 2f485756545989422ab90ad410aff89a.pdf
2025-07-21 16:39:57 | INFO     | app.service.document.async_parse_queue:_complete_task:155 | 任务处理成功: 8b63630b-e6b5-46e9-b96a-dc020283a1eb
2025-07-21 16:39:57 | INFO     | app.service.document.async_parse_queue:_process_task:118 | 工作进程 worker_1 开始处理任务: 7ba8d4e6-8b62-40bc-8691-aebcab345468
2025-07-21 16:39:57 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-21 16:39:57 | INFO     | app.service.document.document_processor:process_document:128 | 共有115 片
2025-07-21 16:39:58 | INFO     | app.service.document.document_processor:_parse_document:214 | 开始解析文件: 993cfd22e465111c6d9afb23a6210831.pdf
2025-07-21 16:40:17 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:17 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:17 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:17 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:17 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:17 | INFO     | app.service.model_client:_generate_final_summary:283 | 最终摘要生成成功，总分片数: 3
2025-07-21 16:40:17 | INFO     | app.service.document.document_processor:process_document:124 | 开始分块: 993cfd22e465111c6d9afb23a6210831.pdf
2025-07-21 16:40:17 | INFO     | app.service.document.document_processor:_finalize_processing:245 | 摘要生成完成: d03b5aa3c5ccbc109869cd0e4905e3a0.pdf
2025-07-21 16:40:17 | INFO     | app.service.document.document_processor:process_document:142 | 文档处理完成: d03b5aa3c5ccbc109869cd0e4905e3a0.pdf
2025-07-21 16:40:17 | INFO     | app.service.document.document_processor:process_document:128 | 共有16 片
2025-07-21 16:40:17 | INFO     | app.service.document.async_parse_queue:_complete_task:155 | 任务处理成功: dafac2d9-b88e-4d91-86f9-d5be977d1b05
2025-07-21 16:40:17 | INFO     | app.service.document.async_parse_queue:_process_task:118 | 工作进程 worker_0 开始处理任务: 3d4ede5c-be20-4f6a-93a1-9e1f96f4c43c
2025-07-21 16:40:18 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-21 16:40:18 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:18 | INFO     | app.service.document.document_processor:_parse_document:214 | 开始解析文件: 993cfd22e465111c6d9afb23a6210831.pdf
2025-07-21 16:40:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:37 | INFO     | app.service.document.document_processor:process_document:124 | 开始分块: 993cfd22e465111c6d9afb23a6210831.pdf
2025-07-21 16:40:37 | INFO     | app.service.document.document_processor:process_document:128 | 共有16 片
2025-07-21 16:40:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:38 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:39 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:40 | INFO     | app.service.document.document_processor:process_document:132 | 向量化开始
2025-07-21 16:40:40 | INFO     | app.database.qdrant_manager:store_vectors:85 | 开始插入 16 个向量，批次大小: 100
2025-07-21 16:40:40 | INFO     | app.database.qdrant_manager:store_vectors:94 | 成功插入批次，大小: 16
2025-07-21 16:40:40 | INFO     | app.database.qdrant_manager:store_vectors:102 | 向量插入完成，总耗时: 0.13s
2025-07-21 16:40:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:40 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:40 | INFO     | app.service.document.document_processor:process_document:136 | 摘要生成开始
2025-07-21 16:40:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:41 | INFO     | app.service.document.document_processor:process_document:132 | 向量化开始
2025-07-21 16:40:41 | INFO     | app.database.qdrant_manager:store_vectors:85 | 开始插入 16 个向量，批次大小: 100
2025-07-21 16:40:41 | INFO     | app.database.qdrant_manager:store_vectors:94 | 成功插入批次，大小: 16
2025-07-21 16:40:41 | INFO     | app.database.qdrant_manager:store_vectors:102 | 向量插入完成，总耗时: 0.12s
2025-07-21 16:40:41 | INFO     | app.service.document.document_processor:process_document:136 | 摘要生成开始
2025-07-21 16:40:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:44 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:45 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:45 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:45 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:45 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:45 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:46 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:46 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:46 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:46 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:46 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:46 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:48 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:48 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:48 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:48 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:48 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:48 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:49 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:50 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:51 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:51 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:51 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:51 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:51 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:53 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:54 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:55 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:55 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 957
2025-07-21 16:40:55 | INFO     | app.service.document.document_processor:_finalize_processing:245 | 摘要生成完成: 993cfd22e465111c6d9afb23a6210831.pdf
2025-07-21 16:40:55 | INFO     | app.service.document.document_processor:process_document:142 | 文档处理完成: 993cfd22e465111c6d9afb23a6210831.pdf
2025-07-21 16:40:55 | INFO     | app.service.document.async_parse_queue:_complete_task:155 | 任务处理成功: 3d4ede5c-be20-4f6a-93a1-9e1f96f4c43c
2025-07-21 16:40:55 | INFO     | app.service.document.async_parse_queue:_process_task:118 | 工作进程 worker_0 开始处理任务: 38b837f5-f819-499d-938b-5a56093243ed
2025-07-21 16:40:55 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-21 16:40:55 | WARNING  | app.service.document.document_processor:process_document:111 | 文件 4b5d3036d307179b979f4e265767c54f 已经解析完成
2025-07-21 16:40:55 | INFO     | app.service.document.async_parse_queue:_complete_task:155 | 任务处理成功: 38b837f5-f819-499d-938b-5a56093243ed
2025-07-21 16:40:55 | INFO     | app.service.document.async_parse_queue:_process_task:118 | 工作进程 worker_0 开始处理任务: 2e4624d3-3d7f-4580-84ff-2eb54d4f4632
2025-07-21 16:40:56 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-21 16:40:56 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:56 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:56 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:56 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:56 | WARNING  | app.service.document.document_processor:process_document:111 | 文件 2f485756545989422ab90ad410aff89a 已经解析完成
2025-07-21 16:40:56 | INFO     | app.service.document.async_parse_queue:_complete_task:155 | 任务处理成功: 2e4624d3-3d7f-4580-84ff-2eb54d4f4632
2025-07-21 16:40:56 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:56 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:56 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:56 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:56 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-21 16:40:56 | INFO     | app.service.document.document_processor:process_document:132 | 向量化开始
2025-07-21 16:40:56 | INFO     | app.database.qdrant_manager:store_vectors:85 | 开始插入 115 个向量，批次大小: 100
2025-07-21 16:40:57 | INFO     | app.database.qdrant_manager:store_vectors:94 | 成功插入批次，大小: 100
2025-07-21 16:40:57 | INFO     | app.database.qdrant_manager:store_vectors:94 | 成功插入批次，大小: 15
2025-07-21 16:40:57 | INFO     | app.database.qdrant_manager:store_vectors:102 | 向量插入完成，总耗时: 0.58s
2025-07-21 16:40:57 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 957
2025-07-21 16:40:57 | INFO     | app.service.document.document_processor:process_document:136 | 摘要生成开始
2025-07-21 16:40:57 | INFO     | app.service.document.document_processor:_finalize_processing:245 | 摘要生成完成: 993cfd22e465111c6d9afb23a6210831.pdf
2025-07-21 16:40:57 | INFO     | app.service.document.document_processor:process_document:142 | 文档处理完成: 993cfd22e465111c6d9afb23a6210831.pdf
2025-07-21 16:40:57 | INFO     | app.service.document.async_parse_queue:_complete_task:155 | 任务处理成功: 7ba8d4e6-8b62-40bc-8691-aebcab345468
2025-07-21 16:41:06 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 4354
2025-07-21 16:41:07 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 4562
2025-07-21 16:41:08 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 1312
2025-07-21 16:41:16 | INFO     | app.service.model_client:_generate_final_summary:283 | 最终摘要生成成功，总分片数: 3
2025-07-21 16:41:16 | INFO     | app.service.document.document_processor:_finalize_processing:245 | 摘要生成完成: d03b5aa3c5ccbc109869cd0e4905e3a0.pdf
2025-07-21 16:41:16 | INFO     | app.service.document.document_processor:process_document:142 | 文档处理完成: d03b5aa3c5ccbc109869cd0e4905e3a0.pdf
2025-07-21 16:41:16 | INFO     | app.service.document.async_parse_queue:_complete_task:155 | 任务处理成功: 8a2e89d7-e184-4974-88a7-3760a453f319
2025-07-21 18:23:03 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-21 18:23:09 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-21 18:23:09 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-21 18:23:09 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-21 18:23:09 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-21 18:23:09 | INFO     | __main__:lifespan:24 | RAG系统启动中...
2025-07-21 18:23:09 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-21 18:23:09 | INFO     | __main__:lifespan:28 | PostgreSQL数据库连接成功
2025-07-21 18:23:09 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-21 18:23:12 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-21 18:23:12 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-21 18:23:12 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-21 18:23:12 | INFO     | app.service.document.document_processor:initialize:96 | 文档处理器初始化完成
2025-07-21 18:23:12 | INFO     | app.service.document.async_parse_queue:start_workers:70 | 启动了 3 个工作进程
2025-07-21 18:23:12 | INFO     | app.service.document.parse_document:initialize_document_queue:225 | 文档处理队列已启动
