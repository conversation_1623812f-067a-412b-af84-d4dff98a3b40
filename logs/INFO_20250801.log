2025-08-01 09:52:48 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-08-01 09:53:04 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-08-01 09:53:04 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (Qwen3-30B-A3B-GPTQ-Int4)
2025-08-01 09:53:04 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-08-01 09:53:04 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-08-01 09:53:04 | INFO     | __main__:lifespan:25 | RAG系统启动中...
2025-08-01 09:53:04 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-08-01 09:53:04 | INFO     | __main__:lifespan:29 | PostgreSQL数据库连接成功
2025-08-01 09:53:04 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-08-01 09:53:06 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-08-01 09:53:06 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-08-01 09:53:06 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-08-01 09:53:07 | INFO     | app.service.document.document_processor:initialize:105 | 文档处理器初始化完成
2025-08-01 09:53:07 | INFO     | app.service.document.async_parse_queue:initialize:49 | AsyncDocumentQueue 初始化完成
2025-08-01 09:53:07 | INFO     | app.service.document.async_parse_queue:start_workers:97 | 启动了 1 个工作进程
2025-08-01 09:53:07 | INFO     | app.service.document.parse_document:initialize_document_queue:242 | 文档处理队列已启动
2025-08-01 09:53:07 | INFO     | app.service.document.async_parse_queue:_worker_loop:101 | 工作进程 worker_0 开始运行
