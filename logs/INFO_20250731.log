2025-07-31 09:57:44 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-31 09:58:01 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-31 09:58:01 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (Qwen3-30B-A3B-GPTQ-Int4)
2025-07-31 09:58:01 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-31 09:58:01 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-31 09:58:01 | INFO     | __main__:lifespan:25 | RAG系统启动中...
2025-07-31 09:58:01 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-31 09:58:01 | INFO     | __main__:lifespan:29 | PostgreSQL数据库连接成功
2025-07-31 09:58:01 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-31 09:58:02 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-31 09:58:02 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-31 09:58:02 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-31 09:58:03 | INFO     | app.service.document.document_processor:initialize:105 | 文档处理器初始化完成
2025-07-31 09:58:03 | INFO     | app.service.document.async_parse_queue:initialize:49 | AsyncDocumentQueue 初始化完成
2025-07-31 09:58:03 | INFO     | app.service.document.async_parse_queue:start_workers:97 | 启动了 1 个工作进程
2025-07-31 09:58:03 | INFO     | app.service.document.parse_document:initialize_document_queue:242 | 文档处理队列已启动
2025-07-31 09:58:03 | INFO     | app.service.document.async_parse_queue:_worker_loop:101 | 工作进程 worker_0 开始运行
2025-07-31 13:35:10 | INFO     | app.database.minio_manager:delete_object:81 | 成功删除对象: 098f6bcd4621d373cade4e832627b4f6/d0557fa25cd937dc192b15c91f5f783f.docx
2025-07-31 13:35:10 | INFO     | app.database.qdrant_manager:delete_vectors_by_filter:209 | 成功根据过滤条件删除向量: 098f6bcd4621d373cade4e832627b4f6
2025-07-31 13:41:20 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-31 13:41:20 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 10 个结果
2025-07-31 13:41:20 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-31 13:41:20 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 10 个结果
2025-07-31 13:41:20 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-31 13:41:20 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 10 个结果
2025-07-31 13:41:20 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-31 13:41:20 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-31 13:41:20 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-31 13:41:20 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-31 13:41:20 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-31 13:41:20 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-31 13:41:20 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-31 13:41:20 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-31 13:41:20 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-31 13:41:20 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-31 13:41:22 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-31 13:41:23 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 10 个结果
2025-07-31 13:41:23 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-31 13:41:23 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 10 个结果
2025-07-31 13:41:23 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-31 13:41:23 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 10 个结果
2025-07-31 13:41:23 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-31 13:41:23 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-31 13:41:23 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-31 13:41:23 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-31 13:41:23 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-31 13:41:23 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-31 13:41:23 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-31 13:41:23 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-31 13:41:23 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-31 13:41:23 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-31 15:30:34 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-31 15:31:11 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-31 15:31:11 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (Qwen3-30B-A3B-GPTQ-Int4)
2025-07-31 15:31:11 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-31 15:31:11 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-31 15:31:11 | INFO     | __main__:lifespan:25 | RAG系统启动中...
2025-07-31 15:31:11 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-31 15:31:11 | INFO     | __main__:lifespan:29 | PostgreSQL数据库连接成功
2025-07-31 15:31:11 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-31 15:31:29 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-31 15:31:29 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-31 15:31:29 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-31 15:31:29 | INFO     | app.service.document.document_processor:initialize:105 | 文档处理器初始化完成
2025-07-31 15:31:29 | INFO     | app.service.document.async_parse_queue:initialize:49 | AsyncDocumentQueue 初始化完成
2025-07-31 15:31:29 | INFO     | app.service.document.async_parse_queue:start_workers:97 | 启动了 1 个工作进程
2025-07-31 15:31:29 | INFO     | app.service.document.parse_document:initialize_document_queue:242 | 文档处理队列已启动
2025-07-31 15:31:29 | INFO     | app.service.document.async_parse_queue:_worker_loop:101 | 工作进程 worker_0 开始运行
2025-07-31 15:36:15 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-31 15:36:28 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-31 15:36:28 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (Qwen3-30B-A3B-GPTQ-Int4)
2025-07-31 15:36:28 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-31 15:36:28 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-31 15:36:28 | INFO     | __main__:lifespan:25 | RAG系统启动中...
2025-07-31 15:36:28 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-31 15:36:28 | INFO     | __main__:lifespan:29 | PostgreSQL数据库连接成功
2025-07-31 15:36:28 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-31 15:36:30 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-31 15:36:30 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-31 15:36:30 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-31 15:36:30 | INFO     | app.service.document.document_processor:initialize:105 | 文档处理器初始化完成
2025-07-31 15:36:30 | INFO     | app.service.document.async_parse_queue:initialize:49 | AsyncDocumentQueue 初始化完成
2025-07-31 15:36:30 | INFO     | app.service.document.async_parse_queue:start_workers:97 | 启动了 1 个工作进程
2025-07-31 15:36:30 | INFO     | app.service.document.parse_document:initialize_document_queue:242 | 文档处理队列已启动
2025-07-31 15:36:30 | INFO     | app.service.document.async_parse_queue:_worker_loop:101 | 工作进程 worker_0 开始运行
2025-07-31 15:38:27 | INFO     | __main__:lifespan:40 | RAG系统关闭中...
2025-07-31 15:38:27 | INFO     | app.service.document.async_parse_queue:_worker_loop:121 | 工作进程 worker_0 被取消
2025-07-31 15:38:27 | INFO     | app.service.document.async_parse_queue:_worker_loop:127 | 工作进程 worker_0 已停止
2025-07-31 15:43:46 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-31 15:44:00 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-31 15:44:00 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (Qwen3-30B-A3B-GPTQ-Int4)
2025-07-31 15:44:00 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-31 15:44:00 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-31 15:44:00 | INFO     | __main__:lifespan:25 | RAG系统启动中...
2025-07-31 15:44:00 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-31 15:44:00 | INFO     | __main__:lifespan:29 | PostgreSQL数据库连接成功
2025-07-31 15:44:00 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-31 15:44:01 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-31 15:44:01 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-31 15:44:01 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-31 15:44:02 | INFO     | app.service.document.document_processor:initialize:105 | 文档处理器初始化完成
2025-07-31 15:44:02 | INFO     | app.service.document.async_parse_queue:initialize:49 | AsyncDocumentQueue 初始化完成
2025-07-31 15:44:02 | INFO     | app.service.document.async_parse_queue:start_workers:97 | 启动了 1 个工作进程
2025-07-31 15:44:02 | INFO     | app.service.document.parse_document:initialize_document_queue:242 | 文档处理队列已启动
2025-07-31 15:44:02 | INFO     | app.service.document.async_parse_queue:_worker_loop:101 | 工作进程 worker_0 开始运行
2025-07-31 18:17:17 | INFO     | __main__:lifespan:40 | RAG系统关闭中...
2025-07-31 18:17:17 | INFO     | app.service.document.async_parse_queue:_worker_loop:121 | 工作进程 worker_0 被取消
2025-07-31 18:17:17 | INFO     | app.service.document.async_parse_queue:_worker_loop:127 | 工作进程 worker_0 已停止
