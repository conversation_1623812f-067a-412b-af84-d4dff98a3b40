2025-07-18 11:20:22 | INFO     | app.database.qdrant_manager:_initialize_client:41 | Qdrant客户端初始化成功
2025-07-18 11:21:28 | INFO     | app.database.qdrant_manager:_initialize_client:41 | Qdrant客户端初始化成功
2025-07-18 11:21:30 | INFO     | app.service.model_client:_init_clients:50 | 模型客户端初始化完成:
2025-07-18 11:21:30 | INFO     | app.service.model_client:_init_clients:51 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-18 11:21:30 | INFO     | app.service.model_client:_init_clients:52 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-18 11:21:30 | INFO     | app.service.model_client:_init_clients:53 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-18 11:21:30 | INFO     | __main__:lifespan:21 | RAG系统启动中...
2025-07-18 11:21:30 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-18 11:21:30 | INFO     | __main__:lifespan:25 | PostgreSQL数据库连接成功
2025-07-18 11:21:30 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-18 11:21:38 | INFO     | app.service.model_client:check_health:71 | LLM 模型服务正常
2025-07-18 11:21:38 | INFO     | app.service.model_client:check_health:80 | Embedding 模型服务正常
2025-07-18 11:21:38 | WARNING  | app.service.model_client:check_health:86 | Rerank 模型服务不可用
2025-07-18 11:22:34 | INFO     | app.service.parse_document:parse_document_service:93 | 开始解析文件: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-18 11:23:42 | INFO     | app.service.parse_document:parse_document_service:111 | 共有15 片
2025-07-18 11:26:11 | INFO     | __main__:lifespan:34 | RAG系统关闭中...
2025-07-18 11:26:21 | INFO     | app.database.qdrant_manager:_initialize_client:41 | Qdrant客户端初始化成功
2025-07-18 11:26:23 | INFO     | app.service.model_client:_init_clients:50 | 模型客户端初始化完成:
2025-07-18 11:26:23 | INFO     | app.service.model_client:_init_clients:51 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-18 11:26:23 | INFO     | app.service.model_client:_init_clients:52 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-18 11:26:23 | INFO     | app.service.model_client:_init_clients:53 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-18 11:26:23 | INFO     | __main__:lifespan:21 | RAG系统启动中...
2025-07-18 11:26:23 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-18 11:26:23 | INFO     | __main__:lifespan:25 | PostgreSQL数据库连接成功
2025-07-18 11:26:23 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-18 11:26:24 | INFO     | app.service.model_client:check_health:71 | LLM 模型服务正常
2025-07-18 11:26:24 | INFO     | app.service.model_client:check_health:80 | Embedding 模型服务正常
2025-07-18 11:26:24 | WARNING  | app.service.model_client:check_health:86 | Rerank 模型服务不可用
2025-07-18 11:26:28 | INFO     | app.service.parse_document:parse_document_service:93 | 开始解析文件: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-18 11:27:33 | INFO     | app.service.parse_document:parse_document_service:111 | 共有15 片
2025-07-18 11:27:46 | INFO     | app.service.model_client:_generate_chunk_summary:237 | 分片摘要生成成功，token 数量: 1311
2025-07-18 13:03:02 | INFO     | __main__:lifespan:34 | RAG系统关闭中...
2025-07-18 13:03:11 | INFO     | app.database.qdrant_manager:_initialize_client:44 | Qdrant客户端初始化成功
2025-07-18 13:03:12 | INFO     | app.service.model_client:_init_clients:50 | 模型客户端初始化完成:
2025-07-18 13:03:12 | INFO     | app.service.model_client:_init_clients:51 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-18 13:03:12 | INFO     | app.service.model_client:_init_clients:52 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-18 13:03:12 | INFO     | app.service.model_client:_init_clients:53 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-18 13:03:12 | INFO     | __main__:lifespan:21 | RAG系统启动中...
2025-07-18 13:03:12 | ERROR    | app.database.postgres_manager:initialize:43 | Tortoise ORM初始化失败: 'NoneType' object is not callable
2025-07-18 13:03:12 | ERROR    | __main__:lifespan:31 | 系统初始化失败: 数据库初始化失败: 'NoneType' object is not callable
2025-07-18 13:10:18 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-18 13:10:20 | INFO     | app.service.model_client:_init_clients:50 | 模型客户端初始化完成:
2025-07-18 13:10:20 | INFO     | app.service.model_client:_init_clients:51 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-18 13:10:20 | INFO     | app.service.model_client:_init_clients:52 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-18 13:10:20 | INFO     | app.service.model_client:_init_clients:53 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-18 13:10:20 | INFO     | __main__:lifespan:21 | RAG系统启动中...
2025-07-18 13:10:20 | ERROR    | app.database.postgres_manager:initialize:43 | Tortoise ORM初始化失败: 'NoneType' object is not callable
2025-07-18 13:10:20 | ERROR    | __main__:lifespan:31 | 系统初始化失败: 数据库初始化失败: 'NoneType' object is not callable
2025-07-18 13:13:09 | INFO     | app.database.qdrant_manager:_initialize_client:41 | Qdrant客户端初始化成功
2025-07-18 13:13:11 | INFO     | app.service.model_client:_init_clients:50 | 模型客户端初始化完成:
2025-07-18 13:13:11 | INFO     | app.service.model_client:_init_clients:51 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-18 13:13:11 | INFO     | app.service.model_client:_init_clients:52 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-18 13:13:11 | INFO     | app.service.model_client:_init_clients:53 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-18 13:13:11 | INFO     | __main__:lifespan:21 | RAG系统启动中...
2025-07-18 13:13:11 | ERROR    | app.database.postgres_manager:initialize:43 | Tortoise ORM初始化失败: 'NoneType' object is not callable
2025-07-18 13:13:11 | ERROR    | __main__:lifespan:31 | 系统初始化失败: 数据库初始化失败: 'NoneType' object is not callable
2025-07-18 13:14:54 | INFO     | app.database.qdrant_manager:_initialize_client:41 | Qdrant客户端初始化成功
2025-07-18 13:14:56 | INFO     | app.service.model_client:_init_clients:50 | 模型客户端初始化完成:
2025-07-18 13:14:56 | INFO     | app.service.model_client:_init_clients:51 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-18 13:14:56 | INFO     | app.service.model_client:_init_clients:52 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-18 13:14:56 | INFO     | app.service.model_client:_init_clients:53 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-18 13:14:56 | INFO     | __main__:lifespan:21 | RAG系统启动中...
2025-07-18 13:14:56 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-18 13:14:56 | INFO     | __main__:lifespan:25 | PostgreSQL数据库连接成功
2025-07-18 13:14:56 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-18 13:14:57 | INFO     | app.service.model_client:check_health:71 | LLM 模型服务正常
2025-07-18 13:14:57 | INFO     | app.service.model_client:check_health:80 | Embedding 模型服务正常
2025-07-18 13:14:57 | WARNING  | app.service.model_client:check_health:86 | Rerank 模型服务不可用
2025-07-18 13:14:59 | INFO     | __main__:lifespan:34 | RAG系统关闭中...
2025-07-18 13:16:18 | INFO     | app.database.qdrant_manager:_initialize_client:41 | Qdrant客户端初始化成功
2025-07-18 13:16:20 | INFO     | app.service.model_client:_init_clients:50 | 模型客户端初始化完成:
2025-07-18 13:16:20 | INFO     | app.service.model_client:_init_clients:51 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-18 13:16:20 | INFO     | app.service.model_client:_init_clients:52 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-18 13:16:20 | INFO     | app.service.model_client:_init_clients:53 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-18 13:16:20 | INFO     | __main__:lifespan:21 | RAG系统启动中...
2025-07-18 13:16:20 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-18 13:16:20 | INFO     | __main__:lifespan:25 | PostgreSQL数据库连接成功
2025-07-18 13:16:20 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-18 13:16:22 | INFO     | app.service.model_client:check_health:71 | LLM 模型服务正常
2025-07-18 13:16:22 | INFO     | app.service.model_client:check_health:80 | Embedding 模型服务正常
2025-07-18 13:16:22 | WARNING  | app.service.model_client:check_health:86 | Rerank 模型服务不可用
2025-07-18 13:17:09 | INFO     | app.service.parse_document:parse_document_service:93 | 开始解析文件: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-18 13:18:13 | INFO     | app.service.parse_document:parse_document_service:111 | 共有15 片
2025-07-18 13:18:14 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:18:14 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: 1 validation error for DocumentEmbedding
embedding.0
  Input should be a valid number [type=float_type, input_value=[-0.00025177001953125, 0....09375, -0.0186767578125], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
2025-07-18 13:19:31 | INFO     | app.service.parse_document:parse_document_service:93 | 开始解析文件: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-18 13:19:49 | INFO     | app.service.parse_document:parse_document_service:111 | 共有15 片
2025-07-18 13:19:49 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:19:49 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: 1 validation error for DocumentEmbedding
embedding.0
  Input should be a valid number [type=float_type, input_value=[-0.00025177001953125, 0....09375, -0.0186767578125], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
2025-07-18 13:20:09 | INFO     | __main__:lifespan:34 | RAG系统关闭中...
2025-07-18 13:20:18 | INFO     | app.database.qdrant_manager:_initialize_client:41 | Qdrant客户端初始化成功
2025-07-18 13:20:19 | INFO     | app.service.model_client:_init_clients:50 | 模型客户端初始化完成:
2025-07-18 13:20:19 | INFO     | app.service.model_client:_init_clients:51 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-18 13:20:19 | INFO     | app.service.model_client:_init_clients:52 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-18 13:20:19 | INFO     | app.service.model_client:_init_clients:53 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-18 13:20:19 | INFO     | __main__:lifespan:21 | RAG系统启动中...
2025-07-18 13:20:19 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-18 13:20:19 | INFO     | __main__:lifespan:25 | PostgreSQL数据库连接成功
2025-07-18 13:20:19 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-18 13:20:22 | INFO     | app.service.model_client:check_health:71 | LLM 模型服务正常
2025-07-18 13:20:22 | INFO     | app.service.model_client:check_health:80 | Embedding 模型服务正常
2025-07-18 13:20:22 | WARNING  | app.service.model_client:check_health:86 | Rerank 模型服务不可用
2025-07-18 13:20:25 | INFO     | app.service.parse_document:parse_document_service:93 | 开始解析文件: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-18 13:21:30 | INFO     | app.service.parse_document:parse_document_service:111 | 共有15 片
2025-07-18 13:21:30 | INFO     | app.service.parse_document:parse_document_service:140 | 向量保存完成: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-18 13:21:45 | INFO     | app.service.model_client:_generate_chunk_summary:237 | 分片摘要生成成功，token 数量: 1311
2025-07-18 13:21:45 | INFO     | app.service.parse_document:parse_document_service:143 | 摘要生成完成: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-18 13:23:15 | INFO     | __main__:lifespan:34 | RAG系统关闭中...
2025-07-18 13:23:31 | INFO     | app.database.qdrant_manager:_initialize_client:41 | Qdrant客户端初始化成功
2025-07-18 13:23:33 | INFO     | app.service.model_client:_init_clients:50 | 模型客户端初始化完成:
2025-07-18 13:23:33 | INFO     | app.service.model_client:_init_clients:51 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-18 13:23:33 | INFO     | app.service.model_client:_init_clients:52 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-18 13:23:33 | INFO     | app.service.model_client:_init_clients:53 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-18 13:23:33 | INFO     | __main__:lifespan:21 | RAG系统启动中...
2025-07-18 13:23:33 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-18 13:23:33 | INFO     | __main__:lifespan:25 | PostgreSQL数据库连接成功
2025-07-18 13:23:33 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-18 13:23:34 | INFO     | app.service.model_client:check_health:71 | LLM 模型服务正常
2025-07-18 13:23:34 | INFO     | app.service.model_client:check_health:80 | Embedding 模型服务正常
2025-07-18 13:23:34 | WARNING  | app.service.model_client:check_health:86 | Rerank 模型服务不可用
2025-07-18 13:23:39 | INFO     | app.service.parse_document:parse_document_service:93 | 开始解析文件: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-18 13:24:01 | INFO     | app.service.parse_document:parse_document_service:111 | 共有15 片
2025-07-18 13:24:01 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:24:01 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: 1 validation error for DocumentEmbedding
embedding.0
  Input should be a valid number [type=float_type, input_value=[-0.00025177001953125, 0....09375, -0.0186767578125], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
2025-07-18 13:25:17 | INFO     | __main__:lifespan:34 | RAG系统关闭中...
2025-07-18 13:25:28 | INFO     | app.database.qdrant_manager:_initialize_client:41 | Qdrant客户端初始化成功
2025-07-18 13:25:29 | INFO     | app.service.model_client:_init_clients:50 | 模型客户端初始化完成:
2025-07-18 13:25:29 | INFO     | app.service.model_client:_init_clients:51 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-18 13:25:29 | INFO     | app.service.model_client:_init_clients:52 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-18 13:25:29 | INFO     | app.service.model_client:_init_clients:53 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-18 13:25:29 | INFO     | __main__:lifespan:21 | RAG系统启动中...
2025-07-18 13:25:29 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-18 13:25:29 | INFO     | __main__:lifespan:25 | PostgreSQL数据库连接成功
2025-07-18 13:25:29 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-18 13:25:31 | INFO     | app.service.model_client:check_health:71 | LLM 模型服务正常
2025-07-18 13:25:31 | INFO     | app.service.model_client:check_health:80 | Embedding 模型服务正常
2025-07-18 13:25:31 | WARNING  | app.service.model_client:check_health:86 | Rerank 模型服务不可用
2025-07-18 13:25:36 | INFO     | app.service.parse_document:parse_document_service:93 | 开始解析文件: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-18 13:26:41 | INFO     | app.service.parse_document:parse_document_service:111 | 共有15 片
2025-07-18 13:26:41 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:26:41 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: 2 validation errors for PointStruct
id.int
  Input should be a valid integer [type=int_type, input_value=UUID('9089ab56-0cca-4ed0-ac66-2cce67a46d44'), input_type=UUID]
    For further information visit https://errors.pydantic.dev/2.11/v/int_type
id.str
  Input should be a valid string [type=string_type, input_value=UUID('9089ab56-0cca-4ed0-ac66-2cce67a46d44'), input_type=UUID]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
2025-07-18 13:28:14 | INFO     | __main__:lifespan:34 | RAG系统关闭中...
2025-07-18 13:28:24 | INFO     | app.database.qdrant_manager:_initialize_client:41 | Qdrant客户端初始化成功
2025-07-18 13:28:26 | INFO     | app.service.model_client:_init_clients:50 | 模型客户端初始化完成:
2025-07-18 13:28:26 | INFO     | app.service.model_client:_init_clients:51 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-18 13:28:26 | INFO     | app.service.model_client:_init_clients:52 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-18 13:28:26 | INFO     | app.service.model_client:_init_clients:53 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-18 13:28:26 | INFO     | __main__:lifespan:21 | RAG系统启动中...
2025-07-18 13:28:26 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-18 13:28:26 | INFO     | __main__:lifespan:25 | PostgreSQL数据库连接成功
2025-07-18 13:28:26 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-18 13:28:52 | INFO     | app.service.model_client:check_health:71 | LLM 模型服务正常
2025-07-18 13:28:52 | INFO     | app.service.model_client:check_health:80 | Embedding 模型服务正常
2025-07-18 13:28:52 | WARNING  | app.service.model_client:check_health:86 | Rerank 模型服务不可用
2025-07-18 13:28:56 | INFO     | app.service.parse_document:parse_document_service:93 | 开始解析文件: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-18 13:30:00 | INFO     | app.service.parse_document:parse_document_service:111 | 共有15 片
2025-07-18 13:30:01 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:30:01 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: 1 validation error for DocumentEmbedding
embedding.0
  Input should be a valid number [type=float_type, input_value=[-0.00025177001953125, 0....09375, -0.0186767578125], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/float_type
2025-07-18 13:33:33 | INFO     | __main__:lifespan:34 | RAG系统关闭中...
2025-07-18 13:33:42 | INFO     | app.database.qdrant_manager:_initialize_client:41 | Qdrant客户端初始化成功
2025-07-18 13:33:44 | INFO     | app.service.model_client:_init_clients:50 | 模型客户端初始化完成:
2025-07-18 13:33:44 | INFO     | app.service.model_client:_init_clients:51 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-18 13:33:44 | INFO     | app.service.model_client:_init_clients:52 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-18 13:33:44 | INFO     | app.service.model_client:_init_clients:53 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-18 13:33:44 | INFO     | __main__:lifespan:21 | RAG系统启动中...
2025-07-18 13:33:44 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-18 13:33:44 | INFO     | __main__:lifespan:25 | PostgreSQL数据库连接成功
2025-07-18 13:33:44 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-18 13:33:45 | INFO     | app.service.model_client:check_health:71 | LLM 模型服务正常
2025-07-18 13:33:45 | INFO     | app.service.model_client:check_health:80 | Embedding 模型服务正常
2025-07-18 13:33:45 | WARNING  | app.service.model_client:check_health:86 | Rerank 模型服务不可用
2025-07-18 13:33:49 | INFO     | app.service.parse_document:parse_document_service:93 | 开始解析文件: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-18 13:34:54 | INFO     | app.service.parse_document:parse_document_service:111 | 共有15 片
2025-07-18 13:34:54 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:34:54 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:34:54 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:34:54 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:34:55 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:34:55 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:34:55 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:34:55 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:34:55 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:34:56 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:34:56 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:34:56 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:34:56 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:34:56 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:34:56 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:34:56 | INFO     | app.service.parse_document:parse_document_service:143 | 向量保存完成: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-18 13:34:56 | INFO     | app.service.parse_document:parse_document_service:146 | 摘要生成完成: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-18 13:35:39 | INFO     | __main__:lifespan:34 | RAG系统关闭中...
2025-07-18 13:35:48 | INFO     | app.database.qdrant_manager:_initialize_client:41 | Qdrant客户端初始化成功
2025-07-18 13:35:49 | INFO     | app.service.model_client:_init_clients:50 | 模型客户端初始化完成:
2025-07-18 13:35:49 | INFO     | app.service.model_client:_init_clients:51 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-18 13:35:49 | INFO     | app.service.model_client:_init_clients:52 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-18 13:35:49 | INFO     | app.service.model_client:_init_clients:53 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-18 13:35:49 | INFO     | __main__:lifespan:21 | RAG系统启动中...
2025-07-18 13:35:50 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-18 13:35:50 | INFO     | __main__:lifespan:25 | PostgreSQL数据库连接成功
2025-07-18 13:35:50 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-18 13:35:58 | INFO     | app.service.model_client:check_health:71 | LLM 模型服务正常
2025-07-18 13:35:58 | INFO     | app.service.model_client:check_health:80 | Embedding 模型服务正常
2025-07-18 13:35:58 | WARNING  | app.service.model_client:check_health:86 | Rerank 模型服务不可用
2025-07-18 13:36:03 | INFO     | app.service.parse_document:parse_document_service:93 | 开始解析文件: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-18 13:37:06 | INFO     | app.service.parse_document:parse_document_service:111 | 共有15 片
2025-07-18 13:37:06 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:37:07 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:37:07 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:37:07 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:37:07 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:37:07 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:37:08 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:37:08 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:37:08 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:37:08 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:37:09 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:37:09 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:37:09 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:37:09 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:37:09 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:37:09 | INFO     | app.service.parse_document:parse_document_service:141 | 向量保存完成: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-18 13:37:09 | INFO     | app.service.parse_document:parse_document_service:144 | 摘要生成完成: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-18 13:44:36 | INFO     | __main__:lifespan:34 | RAG系统关闭中...
2025-07-18 13:44:45 | INFO     | app.database.qdrant_manager:_initialize_client:41 | Qdrant客户端初始化成功
2025-07-18 13:44:46 | INFO     | app.service.model_client:_init_clients:50 | 模型客户端初始化完成:
2025-07-18 13:44:46 | INFO     | app.service.model_client:_init_clients:51 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-18 13:44:46 | INFO     | app.service.model_client:_init_clients:52 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-18 13:44:46 | INFO     | app.service.model_client:_init_clients:53 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-18 13:44:46 | INFO     | __main__:lifespan:21 | RAG系统启动中...
2025-07-18 13:44:47 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-18 13:44:47 | INFO     | __main__:lifespan:25 | PostgreSQL数据库连接成功
2025-07-18 13:44:47 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-18 13:44:50 | INFO     | app.service.model_client:check_health:71 | LLM 模型服务正常
2025-07-18 13:44:50 | INFO     | app.service.model_client:check_health:80 | Embedding 模型服务正常
2025-07-18 13:44:50 | WARNING  | app.service.model_client:check_health:86 | Rerank 模型服务不可用
2025-07-18 13:44:56 | INFO     | app.service.parse_document:parse_document_service:97 | 开始解析文件: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-18 13:45:18 | INFO     | app.service.parse_document:parse_document_service:116 | 共有15 片
2025-07-18 13:45:18 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:45:18 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:45:19 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:45:19 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:45:19 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:45:19 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:45:19 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:45:20 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:45:20 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:45:20 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:45:20 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:45:20 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:45:21 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:45:21 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:45:21 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-18 13:45:21 | INFO     | app.database.qdrant_manager:_create_collection_if_not_exists:62 | 创建Qdrant集合: 21f64da1e5792c8295b964d159a14491
2025-07-18 13:45:21 | INFO     | app.database.qdrant_manager:store_vectors:84 | 开始插入 15 个向量，批次大小: 100
2025-07-18 13:45:21 | INFO     | app.database.qdrant_manager:store_vectors:93 | 成功插入批次，大小: 15
2025-07-18 13:45:21 | INFO     | app.database.qdrant_manager:store_vectors:101 | 向量插入完成，总耗时: 0.25s
2025-07-18 13:45:21 | INFO     | app.service.parse_document:parse_document_service:146 | 向量保存完成: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-18 13:45:44 | INFO     | app.service.model_client:_generate_final_summary:275 | 最终摘要生成成功，总分片数: 0
2025-07-18 13:45:44 | INFO     | app.service.parse_document:parse_document_service:152 | 摘要生成完成: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-18 14:09:09 | INFO     | __main__:lifespan:34 | RAG系统关闭中...
2025-07-18 14:09:20 | INFO     | app.database.qdrant_manager:_initialize_client:41 | Qdrant客户端初始化成功
2025-07-18 14:09:21 | INFO     | app.service.model_client:_init_clients:50 | 模型客户端初始化完成:
2025-07-18 14:09:21 | INFO     | app.service.model_client:_init_clients:51 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-18 14:09:21 | INFO     | app.service.model_client:_init_clients:52 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-18 14:09:21 | INFO     | app.service.model_client:_init_clients:53 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-18 14:09:21 | INFO     | __main__:lifespan:21 | RAG系统启动中...
2025-07-18 14:09:21 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-18 14:09:21 | INFO     | __main__:lifespan:25 | PostgreSQL数据库连接成功
2025-07-18 14:09:21 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-18 14:09:23 | INFO     | app.service.model_client:check_health:71 | LLM 模型服务正常
2025-07-18 14:09:23 | INFO     | app.service.model_client:check_health:80 | Embedding 模型服务正常
2025-07-18 14:09:23 | WARNING  | app.service.model_client:check_health:86 | Rerank 模型服务不可用
2025-07-18 14:09:28 | INFO     | app.service.parse_document:parse_document_service:97 | 开始解析文件: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-18 14:10:34 | INFO     | app.service.parse_document:parse_document_service:116 | 共有15 片
2025-07-18 14:10:34 | INFO     | app.service.parse_document:parse_document_service:146 | 向量保存完成: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-18 14:10:47 | INFO     | app.service.model_client:_generate_chunk_summary:233 | 分片摘要生成成功，token 数量: 1311
2025-07-18 14:10:47 | INFO     | app.service.parse_document:parse_document_service:152 | 摘要生成完成: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-18 14:18:27 | INFO     | __main__:lifespan:34 | RAG系统关闭中...
2025-07-18 14:18:40 | INFO     | app.database.qdrant_manager:_initialize_client:41 | Qdrant客户端初始化成功
2025-07-18 14:18:42 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-18 14:18:42 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-18 14:18:42 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-18 14:18:42 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-18 14:18:42 | INFO     | __main__:lifespan:21 | RAG系统启动中...
2025-07-18 14:18:42 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-18 14:18:42 | INFO     | __main__:lifespan:25 | PostgreSQL数据库连接成功
2025-07-18 14:18:42 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-18 14:18:44 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-18 14:18:44 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-18 14:18:44 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-18 14:19:05 | INFO     | __main__:lifespan:34 | RAG系统关闭中...
2025-07-18 14:19:13 | INFO     | app.database.qdrant_manager:_initialize_client:41 | Qdrant客户端初始化成功
2025-07-18 14:19:14 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-18 14:19:14 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-18 14:19:14 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-18 14:19:14 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-18 14:19:14 | INFO     | __main__:lifespan:21 | RAG系统启动中...
2025-07-18 14:19:14 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-18 14:19:14 | INFO     | __main__:lifespan:25 | PostgreSQL数据库连接成功
2025-07-18 14:19:14 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-18 14:19:16 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-18 14:19:16 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-18 14:19:16 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-18 14:19:21 | INFO     | app.service.parse_document:parse_document_service:97 | 开始解析文件: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-18 14:20:27 | INFO     | app.service.parse_document:parse_document_service:116 | 共有15 片
2025-07-18 14:20:27 | INFO     | app.service.parse_document:parse_document_service:146 | 向量保存完成: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-18 14:20:41 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 1311
2025-07-18 14:20:41 | INFO     | app.service.parse_document:parse_document_service:153 | 摘要生成完成: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-18 14:22:45 | INFO     | __main__:lifespan:34 | RAG系统关闭中...
2025-07-18 14:22:55 | INFO     | app.database.qdrant_manager:_initialize_client:41 | Qdrant客户端初始化成功
2025-07-18 14:22:57 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-18 14:22:57 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-18 14:22:57 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-18 14:22:57 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-18 14:22:57 | INFO     | __main__:lifespan:21 | RAG系统启动中...
2025-07-18 14:22:57 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-18 14:22:57 | INFO     | __main__:lifespan:25 | PostgreSQL数据库连接成功
2025-07-18 14:22:57 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-18 14:22:59 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-18 14:22:59 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-18 14:22:59 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-18 14:23:02 | INFO     | app.service.parse_document:parse_document_service:97 | 开始解析文件: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-18 14:24:08 | INFO     | app.service.parse_document:parse_document_service:116 | 共有15 片
2025-07-18 14:24:08 | INFO     | app.service.parse_document:parse_document_service:146 | 向量保存完成: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-18 14:24:15 | INFO     | app.service.model_client:_generate_chunk_summary:241 | 分片摘要生成成功，token 数量: 1311
2025-07-18 14:24:15 | INFO     | app.service.parse_document:parse_document_service:153 | 摘要生成完成: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-18 14:27:47 | INFO     | __main__:lifespan:34 | RAG系统关闭中...
2025-07-18 14:57:56 | INFO     | app.database.qdrant_manager:_initialize_client:41 | Qdrant客户端初始化成功
2025-07-18 14:57:58 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-18 14:57:58 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-18 14:57:58 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-18 14:57:58 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-18 14:57:58 | INFO     | __main__:lifespan:21 | RAG系统启动中...
2025-07-18 14:57:58 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-18 14:57:58 | INFO     | __main__:lifespan:25 | PostgreSQL数据库连接成功
2025-07-18 14:57:58 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-18 14:58:01 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-18 14:58:01 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-18 14:58:01 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-18 14:58:38 | INFO     | __main__:lifespan:34 | RAG系统关闭中...
2025-07-18 14:58:47 | INFO     | app.database.qdrant_manager:_initialize_client:41 | Qdrant客户端初始化成功
2025-07-18 14:58:49 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-18 14:58:49 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-18 14:58:49 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-18 14:58:49 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-18 14:58:49 | INFO     | __main__:lifespan:21 | RAG系统启动中...
2025-07-18 14:58:49 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-18 14:58:49 | INFO     | __main__:lifespan:25 | PostgreSQL数据库连接成功
2025-07-18 14:58:49 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-18 14:58:50 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-18 14:58:50 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-18 14:58:50 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-18 15:00:19 | INFO     | __main__:lifespan:34 | RAG系统关闭中...
2025-07-18 15:00:28 | INFO     | app.database.qdrant_manager:_initialize_client:41 | Qdrant客户端初始化成功
2025-07-18 15:00:29 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-18 15:00:29 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-18 15:00:29 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-18 15:00:29 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-18 15:00:30 | INFO     | __main__:lifespan:21 | RAG系统启动中...
2025-07-18 15:00:30 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-18 15:00:30 | INFO     | __main__:lifespan:25 | PostgreSQL数据库连接成功
2025-07-18 15:00:30 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-18 15:00:31 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-18 15:00:31 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-18 15:00:32 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-18 15:01:47 | INFO     | __main__:lifespan:34 | RAG系统关闭中...
2025-07-18 15:01:51 | INFO     | app.database.qdrant_manager:_initialize_client:41 | Qdrant客户端初始化成功
2025-07-18 15:01:53 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-18 15:01:53 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-18 15:01:53 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-18 15:01:53 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-18 15:01:58 | INFO     | __main__:lifespan:21 | RAG系统启动中...
2025-07-18 15:01:58 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-18 15:01:58 | INFO     | __main__:lifespan:25 | PostgreSQL数据库连接成功
2025-07-18 15:01:58 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-18 15:01:59 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-18 15:01:59 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-18 15:01:59 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-18 15:02:15 | INFO     | __main__:lifespan:34 | RAG系统关闭中...
2025-07-18 15:02:19 | INFO     | app.database.qdrant_manager:_initialize_client:41 | Qdrant客户端初始化成功
2025-07-18 15:02:22 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-18 15:02:22 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-18 15:02:22 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-18 15:02:22 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-18 15:02:27 | INFO     | __main__:lifespan:21 | RAG系统启动中...
2025-07-18 15:02:27 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-18 15:02:27 | INFO     | __main__:lifespan:25 | PostgreSQL数据库连接成功
2025-07-18 15:02:27 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-18 15:02:28 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-18 15:02:28 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-18 15:02:28 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-18 15:02:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 15:05:34 | INFO     | __main__:lifespan:34 | RAG系统关闭中...
2025-07-18 15:05:38 | INFO     | app.database.qdrant_manager:_initialize_client:41 | Qdrant客户端初始化成功
2025-07-18 15:05:40 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-18 15:05:40 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-18 15:05:40 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-18 15:05:40 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-18 15:05:45 | INFO     | __main__:lifespan:21 | RAG系统启动中...
2025-07-18 15:05:45 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-18 15:05:45 | INFO     | __main__:lifespan:25 | PostgreSQL数据库连接成功
2025-07-18 15:05:45 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-18 15:06:00 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-18 15:06:00 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-18 15:06:00 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-18 15:06:20 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 16:27:48 | INFO     | __main__:lifespan:34 | RAG系统关闭中...
2025-07-18 16:27:51 | INFO     | app.database.qdrant_manager:_initialize_client:41 | Qdrant客户端初始化成功
2025-07-18 16:27:53 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-18 16:27:53 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-18 16:27:53 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-18 16:27:53 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-18 16:27:58 | INFO     | __main__:lifespan:21 | RAG系统启动中...
2025-07-18 16:27:58 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-18 16:27:58 | INFO     | __main__:lifespan:25 | PostgreSQL数据库连接成功
2025-07-18 16:27:58 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-18 16:28:00 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-18 16:28:00 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-18 16:28:00 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-18 16:28:06 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 16:30:50 | INFO     | __main__:lifespan:34 | RAG系统关闭中...
2025-07-18 16:30:53 | INFO     | app.database.qdrant_manager:_initialize_client:41 | Qdrant客户端初始化成功
2025-07-18 16:30:55 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-18 16:30:55 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-18 16:30:55 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-18 16:30:55 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-18 16:31:01 | INFO     | __main__:lifespan:21 | RAG系统启动中...
2025-07-18 16:31:01 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-18 16:31:01 | INFO     | __main__:lifespan:25 | PostgreSQL数据库连接成功
2025-07-18 16:31:01 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-18 16:31:03 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-18 16:31:03 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-18 16:31:03 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-18 16:31:05 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 16:31:20 | INFO     | __main__:lifespan:34 | RAG系统关闭中...
2025-07-18 16:31:24 | INFO     | app.database.qdrant_manager:_initialize_client:41 | Qdrant客户端初始化成功
2025-07-18 16:31:26 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-18 16:31:26 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-18 16:31:26 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-18 16:31:26 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-18 16:31:31 | INFO     | __main__:lifespan:21 | RAG系统启动中...
2025-07-18 16:31:31 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-18 16:31:31 | INFO     | __main__:lifespan:25 | PostgreSQL数据库连接成功
2025-07-18 16:31:31 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-18 16:31:33 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-18 16:31:33 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-18 16:31:33 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-18 16:31:35 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 16:32:46 | INFO     | __main__:lifespan:34 | RAG系统关闭中...
2025-07-18 16:32:49 | INFO     | app.database.qdrant_manager:_initialize_client:41 | Qdrant客户端初始化成功
2025-07-18 16:32:51 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-18 16:32:51 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-18 16:32:51 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-18 16:32:51 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-18 16:32:57 | INFO     | __main__:lifespan:21 | RAG系统启动中...
2025-07-18 16:32:57 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-18 16:32:57 | INFO     | __main__:lifespan:25 | PostgreSQL数据库连接成功
2025-07-18 16:32:57 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-18 16:32:59 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-18 16:32:59 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-18 16:32:59 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-18 16:33:01 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 16:57:58 | INFO     | __main__:lifespan:34 | RAG系统关闭中...
2025-07-18 16:58:02 | INFO     | app.database.qdrant_manager:_initialize_client:41 | Qdrant客户端初始化成功
2025-07-18 16:58:04 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-18 16:58:04 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-18 16:58:04 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-18 16:58:04 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-18 16:58:10 | INFO     | __main__:lifespan:22 | RAG系统启动中...
2025-07-18 16:58:10 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-18 16:58:10 | INFO     | __main__:lifespan:26 | PostgreSQL数据库连接成功
2025-07-18 16:58:10 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-18 16:58:11 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-18 16:58:11 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-18 16:58:11 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-18 17:01:52 | ERROR    | app.service.multi_strategy_search_engine:_single_strategy_search:104 | 搜索策略执行失败: exact_topic - Expected object of type bytes or bytearray, got: <class 'str'>
2025-07-18 17:01:52 | ERROR    | app.service.multi_strategy_search_engine:_single_strategy_search:104 | 搜索策略执行失败: entity_search - Expected object of type bytes or bytearray, got: <class 'str'>
2025-07-18 17:01:52 | ERROR    | app.service.multi_strategy_search_engine:_single_strategy_search:104 | 搜索策略执行失败: entity_search - Expected object of type bytes or bytearray, got: <class 'str'>
2025-07-18 17:01:52 | ERROR    | app.service.multi_strategy_search_engine:_single_strategy_search:104 | 搜索策略执行失败: entity_search - Expected object of type bytes or bytearray, got: <class 'str'>
2025-07-18 17:01:52 | ERROR    | app.service.multi_strategy_search_engine:_single_strategy_search:104 | 搜索策略执行失败: generated_query - Expected object of type bytes or bytearray, got: <class 'str'>
2025-07-18 17:01:52 | ERROR    | app.service.multi_strategy_search_engine:_single_strategy_search:104 | 搜索策略执行失败: generated_query - Expected object of type bytes or bytearray, got: <class 'str'>
2025-07-18 17:01:52 | ERROR    | app.service.multi_strategy_search_engine:_single_strategy_search:104 | 搜索策略执行失败: generated_query - Expected object of type bytes or bytearray, got: <class 'str'>
2025-07-18 17:01:52 | ERROR    | app.service.multi_strategy_search_engine:_single_strategy_search:104 | 搜索策略执行失败: generated_query - Expected object of type bytes or bytearray, got: <class 'str'>
2025-07-18 17:01:52 | ERROR    | app.service.multi_strategy_search_engine:_single_strategy_search:104 | 搜索策略执行失败: generated_query - Expected object of type bytes or bytearray, got: <class 'str'>
2025-07-18 17:03:12 | ERROR    | app.service.multi_strategy_search_engine:_single_strategy_search:104 | 搜索策略执行失败: exact_topic - Expected object of type bytes or bytearray, got: <class 'str'>
2025-07-18 17:03:12 | ERROR    | app.service.multi_strategy_search_engine:_single_strategy_search:104 | 搜索策略执行失败: entity_search - Expected object of type bytes or bytearray, got: <class 'str'>
2025-07-18 17:03:12 | ERROR    | app.service.multi_strategy_search_engine:_single_strategy_search:104 | 搜索策略执行失败: entity_search - Expected object of type bytes or bytearray, got: <class 'str'>
2025-07-18 17:03:12 | ERROR    | app.service.multi_strategy_search_engine:_single_strategy_search:104 | 搜索策略执行失败: entity_search - Expected object of type bytes or bytearray, got: <class 'str'>
2025-07-18 17:03:12 | ERROR    | app.service.multi_strategy_search_engine:_single_strategy_search:104 | 搜索策略执行失败: generated_query - Expected object of type bytes or bytearray, got: <class 'str'>
2025-07-18 17:03:12 | ERROR    | app.service.multi_strategy_search_engine:_single_strategy_search:104 | 搜索策略执行失败: generated_query - Expected object of type bytes or bytearray, got: <class 'str'>
2025-07-18 17:03:12 | ERROR    | app.service.multi_strategy_search_engine:_single_strategy_search:104 | 搜索策略执行失败: generated_query - Expected object of type bytes or bytearray, got: <class 'str'>
2025-07-18 17:03:12 | ERROR    | app.service.multi_strategy_search_engine:_single_strategy_search:104 | 搜索策略执行失败: generated_query - Expected object of type bytes or bytearray, got: <class 'str'>
2025-07-18 17:05:52 | INFO     | __main__:lifespan:35 | RAG系统关闭中...
2025-07-18 17:05:55 | INFO     | app.database.qdrant_manager:_initialize_client:41 | Qdrant客户端初始化成功
2025-07-18 17:05:58 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-18 17:05:58 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-18 17:05:58 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-18 17:05:58 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-18 17:06:03 | INFO     | __main__:lifespan:22 | RAG系统启动中...
2025-07-18 17:06:03 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-18 17:06:03 | INFO     | __main__:lifespan:26 | PostgreSQL数据库连接成功
2025-07-18 17:06:03 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-18 17:06:04 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-18 17:06:04 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-18 17:06:04 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-18 17:06:11 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:06:11 | ERROR    | app.service.multi_strategy_search_engine:_single_strategy_search:104 | 搜索策略执行失败: exact_topic - 'QdrantManager' object has no attribute 'search_vectors'
2025-07-18 17:06:11 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:06:11 | ERROR    | app.service.multi_strategy_search_engine:_single_strategy_search:104 | 搜索策略执行失败: entity_search - 'QdrantManager' object has no attribute 'search_vectors'
2025-07-18 17:06:11 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:06:11 | ERROR    | app.service.multi_strategy_search_engine:_single_strategy_search:104 | 搜索策略执行失败: entity_search - 'QdrantManager' object has no attribute 'search_vectors'
2025-07-18 17:06:11 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:06:11 | ERROR    | app.service.multi_strategy_search_engine:_single_strategy_search:104 | 搜索策略执行失败: entity_search - 'QdrantManager' object has no attribute 'search_vectors'
2025-07-18 17:06:11 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:06:11 | ERROR    | app.service.multi_strategy_search_engine:_single_strategy_search:104 | 搜索策略执行失败: generated_query - 'QdrantManager' object has no attribute 'search_vectors'
2025-07-18 17:06:11 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:06:11 | ERROR    | app.service.multi_strategy_search_engine:_single_strategy_search:104 | 搜索策略执行失败: generated_query - 'QdrantManager' object has no attribute 'search_vectors'
2025-07-18 17:06:11 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:06:11 | ERROR    | app.service.multi_strategy_search_engine:_single_strategy_search:104 | 搜索策略执行失败: generated_query - 'QdrantManager' object has no attribute 'search_vectors'
2025-07-18 17:06:11 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:06:11 | ERROR    | app.service.multi_strategy_search_engine:_single_strategy_search:104 | 搜索策略执行失败: generated_query - 'QdrantManager' object has no attribute 'search_vectors'
2025-07-18 17:06:11 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:06:11 | ERROR    | app.service.multi_strategy_search_engine:_single_strategy_search:104 | 搜索策略执行失败: generated_query - 'QdrantManager' object has no attribute 'search_vectors'
2025-07-18 17:08:29 | INFO     | __main__:lifespan:35 | RAG系统关闭中...
2025-07-18 17:08:33 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-18 17:08:35 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-18 17:08:35 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-18 17:08:35 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-18 17:08:35 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-18 17:08:40 | INFO     | __main__:lifespan:22 | RAG系统启动中...
2025-07-18 17:08:40 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-18 17:08:40 | INFO     | __main__:lifespan:26 | PostgreSQL数据库连接成功
2025-07-18 17:08:40 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-18 17:08:41 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-18 17:08:41 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-18 17:08:41 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-18 17:08:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:08:47 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 0 个结果
2025-07-18 17:08:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:08:47 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 0 个结果
2025-07-18 17:08:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:08:47 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 0 个结果
2025-07-18 17:08:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:08:47 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 0 个结果
2025-07-18 17:08:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:08:47 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 0 个结果
2025-07-18 17:08:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:08:47 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 0 个结果
2025-07-18 17:08:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:08:47 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 0 个结果
2025-07-18 17:08:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:08:47 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 0 个结果
2025-07-18 17:08:47 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:08:47 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 0 个结果
2025-07-18 17:10:16 | INFO     | __main__:lifespan:35 | RAG系统关闭中...
2025-07-18 17:10:20 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-18 17:10:23 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-18 17:10:23 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-18 17:10:23 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-18 17:10:23 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-18 17:10:28 | INFO     | __main__:lifespan:22 | RAG系统启动中...
2025-07-18 17:10:28 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-18 17:10:28 | INFO     | __main__:lifespan:26 | PostgreSQL数据库连接成功
2025-07-18 17:10:28 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-18 17:10:29 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-18 17:10:29 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-18 17:10:29 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-18 17:10:39 | ERROR    | app.service.multi_strategy_search_engine:_single_strategy_search:105 | 搜索策略执行失败: exact_topic - Expected object of type bytes or bytearray, got: <class 'str'>
2025-07-18 17:10:39 | ERROR    | app.service.multi_strategy_search_engine:_single_strategy_search:105 | 搜索策略执行失败: entity_search - Expected object of type bytes or bytearray, got: <class 'str'>
2025-07-18 17:10:39 | ERROR    | app.service.multi_strategy_search_engine:_single_strategy_search:105 | 搜索策略执行失败: entity_search - Expected object of type bytes or bytearray, got: <class 'str'>
2025-07-18 17:10:39 | ERROR    | app.service.multi_strategy_search_engine:_single_strategy_search:105 | 搜索策略执行失败: entity_search - Expected object of type bytes or bytearray, got: <class 'str'>
2025-07-18 17:10:39 | ERROR    | app.service.multi_strategy_search_engine:_single_strategy_search:105 | 搜索策略执行失败: generated_query - Expected object of type bytes or bytearray, got: <class 'str'>
2025-07-18 17:10:39 | ERROR    | app.service.multi_strategy_search_engine:_single_strategy_search:105 | 搜索策略执行失败: generated_query - Expected object of type bytes or bytearray, got: <class 'str'>
2025-07-18 17:10:39 | ERROR    | app.service.multi_strategy_search_engine:_single_strategy_search:105 | 搜索策略执行失败: generated_query - Expected object of type bytes or bytearray, got: <class 'str'>
2025-07-18 17:10:39 | ERROR    | app.service.multi_strategy_search_engine:_single_strategy_search:105 | 搜索策略执行失败: generated_query - Expected object of type bytes or bytearray, got: <class 'str'>
2025-07-18 17:11:07 | INFO     | __main__:lifespan:35 | RAG系统关闭中...
2025-07-18 17:11:10 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-18 17:11:12 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-18 17:11:12 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-18 17:11:12 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-18 17:11:12 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-18 17:11:17 | INFO     | __main__:lifespan:22 | RAG系统启动中...
2025-07-18 17:11:17 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-18 17:11:17 | INFO     | __main__:lifespan:26 | PostgreSQL数据库连接成功
2025-07-18 17:11:17 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-18 17:11:19 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-18 17:11:19 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-18 17:11:19 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-18 17:11:27 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:11:27 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 0 个结果
2025-07-18 17:11:27 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:11:27 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 0 个结果
2025-07-18 17:11:27 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:11:27 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 0 个结果
2025-07-18 17:11:27 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:11:27 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 0 个结果
2025-07-18 17:11:27 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:11:27 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 0 个结果
2025-07-18 17:11:27 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:11:28 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 0 个结果
2025-07-18 17:11:28 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:11:28 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 0 个结果
2025-07-18 17:12:17 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:12:17 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 0 个结果
2025-07-18 17:12:17 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:12:17 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 0 个结果
2025-07-18 17:12:17 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:12:17 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 0 个结果
2025-07-18 17:12:17 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:12:17 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 0 个结果
2025-07-18 17:12:17 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:12:17 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 0 个结果
2025-07-18 17:12:17 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:12:17 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 0 个结果
2025-07-18 17:12:17 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:12:17 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 0 个结果
2025-07-18 17:13:22 | INFO     | __main__:lifespan:35 | RAG系统关闭中...
2025-07-18 17:13:25 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-18 17:13:28 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-18 17:13:28 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-18 17:13:28 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-18 17:13:28 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-18 17:13:33 | INFO     | __main__:lifespan:22 | RAG系统启动中...
2025-07-18 17:13:33 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-18 17:13:33 | INFO     | __main__:lifespan:26 | PostgreSQL数据库连接成功
2025-07-18 17:13:33 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-18 17:13:35 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-18 17:13:35 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-18 17:13:35 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-18 17:14:08 | INFO     | __main__:lifespan:35 | RAG系统关闭中...
2025-07-18 17:14:11 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-18 17:14:13 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-18 17:14:13 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-18 17:14:13 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-18 17:14:13 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-18 17:14:18 | INFO     | __main__:lifespan:22 | RAG系统启动中...
2025-07-18 17:14:18 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-18 17:14:18 | INFO     | __main__:lifespan:26 | PostgreSQL数据库连接成功
2025-07-18 17:14:18 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-18 17:14:20 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-18 17:14:20 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-18 17:14:20 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-18 17:14:28 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:14:28 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 0 个结果
2025-07-18 17:14:28 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:14:28 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 0 个结果
2025-07-18 17:14:28 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:14:28 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 0 个结果
2025-07-18 17:14:28 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:14:28 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 0 个结果
2025-07-18 17:14:28 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:14:28 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 0 个结果
2025-07-18 17:14:28 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:14:28 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 0 个结果
2025-07-18 17:14:28 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:14:28 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 0 个结果
2025-07-18 17:14:43 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:15:04 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:15:21 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:15:41 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:16:18 | INFO     | __main__:lifespan:35 | RAG系统关闭中...
2025-07-18 17:16:22 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-18 17:16:25 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-18 17:16:25 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-18 17:16:25 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-18 17:16:25 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-18 17:16:30 | INFO     | __main__:lifespan:22 | RAG系统启动中...
2025-07-18 17:16:30 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-18 17:16:30 | INFO     | __main__:lifespan:26 | PostgreSQL数据库连接成功
2025-07-18 17:16:30 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-18 17:16:31 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-18 17:16:31 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-18 17:16:31 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-18 17:16:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:16:37 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 0 个结果
2025-07-18 17:16:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:16:37 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 0 个结果
2025-07-18 17:16:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:16:37 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 0 个结果
2025-07-18 17:16:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:16:37 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 0 个结果
2025-07-18 17:16:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:16:37 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 0 个结果
2025-07-18 17:16:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:16:37 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 0 个结果
2025-07-18 17:16:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:16:37 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 0 个结果
2025-07-18 17:17:19 | INFO     | __main__:lifespan:35 | RAG系统关闭中...
2025-07-18 17:17:22 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-18 17:17:25 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-18 17:17:25 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-18 17:17:25 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-18 17:17:25 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-18 17:17:30 | INFO     | __main__:lifespan:22 | RAG系统启动中...
2025-07-18 17:17:30 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-18 17:17:30 | INFO     | __main__:lifespan:26 | PostgreSQL数据库连接成功
2025-07-18 17:17:30 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-18 17:17:33 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-18 17:17:33 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-18 17:17:33 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-18 17:17:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:17:37 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 10 个结果
2025-07-18 17:17:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:17:37 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 10 个结果
2025-07-18 17:17:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:17:37 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 10 个结果
2025-07-18 17:17:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:17:37 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-18 17:17:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:17:37 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-18 17:17:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:17:37 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-18 17:19:12 | INFO     | __main__:lifespan:35 | RAG系统关闭中...
2025-07-18 17:19:16 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-18 17:19:18 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-18 17:19:18 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-18 17:19:18 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-18 17:19:18 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-18 17:19:23 | INFO     | __main__:lifespan:22 | RAG系统启动中...
2025-07-18 17:19:23 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-18 17:19:24 | INFO     | __main__:lifespan:26 | PostgreSQL数据库连接成功
2025-07-18 17:19:24 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-18 17:19:25 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-18 17:19:25 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-18 17:19:25 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-18 17:19:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:19:37 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 10 个结果
2025-07-18 17:19:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:19:37 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 10 个结果
2025-07-18 17:19:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:19:37 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 10 个结果
2025-07-18 17:19:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:19:37 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-18 17:19:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:19:37 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-18 17:19:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:19:37 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-18 17:19:37 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:19:37 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-18 17:24:16 | INFO     | __main__:lifespan:35 | RAG系统关闭中...
2025-07-18 17:24:19 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-18 17:24:21 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-18 17:24:21 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-18 17:24:21 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-18 17:24:21 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-18 17:24:27 | INFO     | __main__:lifespan:22 | RAG系统启动中...
2025-07-18 17:24:27 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-18 17:24:27 | INFO     | __main__:lifespan:26 | PostgreSQL数据库连接成功
2025-07-18 17:24:27 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-18 17:24:29 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-18 17:24:29 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-18 17:24:29 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-18 17:24:35 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:24:35 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 10 个结果
2025-07-18 17:24:35 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:24:35 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 10 个结果
2025-07-18 17:24:35 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:24:36 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 10 个结果
2025-07-18 17:24:36 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:24:36 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-18 17:24:36 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:24:36 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-18 17:24:36 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:24:36 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-18 17:25:30 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:25:30 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 10 个结果
2025-07-18 17:25:30 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:25:30 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 10 个结果
2025-07-18 17:25:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:25:31 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 10 个结果
2025-07-18 17:25:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:25:31 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-18 17:25:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:25:31 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-18 17:25:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:25:31 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-18 17:25:31 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:25:31 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-18 17:27:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:27:42 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 10 个结果
2025-07-18 17:27:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:27:42 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 10 个结果
2025-07-18 17:27:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:27:42 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 10 个结果
2025-07-18 17:27:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:27:42 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-18 17:27:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:27:42 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-18 17:27:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:27:42 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-18 17:27:42 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:27:42 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-18 17:29:00 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:29:00 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 10 个结果
2025-07-18 17:29:00 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:29:00 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 10 个结果
2025-07-18 17:29:00 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:29:00 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 10 个结果
2025-07-18 17:29:00 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:29:00 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-18 17:29:00 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:29:00 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-18 17:29:00 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:29:00 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-18 17:29:00 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:29:00 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-18 17:31:10 | INFO     | __main__:lifespan:35 | RAG系统关闭中...
2025-07-18 17:31:14 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-18 17:31:17 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-18 17:31:17 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-18 17:31:17 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-18 17:31:17 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-18 17:31:22 | INFO     | __main__:lifespan:22 | RAG系统启动中...
2025-07-18 17:31:22 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-18 17:31:22 | INFO     | __main__:lifespan:26 | PostgreSQL数据库连接成功
2025-07-18 17:31:22 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-18 17:31:25 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-18 17:31:25 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-18 17:31:25 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-18 17:31:29 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:31:29 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 10 个结果
2025-07-18 17:31:29 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:31:29 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 10 个结果
2025-07-18 17:31:29 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:31:29 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 10 个结果
2025-07-18 17:31:29 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:31:29 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-18 17:31:29 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:31:29 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-18 17:31:29 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:31:30 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-18 17:31:30 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:31:30 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-18 17:39:02 | INFO     | __main__:lifespan:35 | RAG系统关闭中...
2025-07-18 17:39:06 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-18 17:39:08 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-18 17:39:08 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-18 17:39:08 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-18 17:39:08 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-18 17:39:14 | INFO     | __main__:lifespan:22 | RAG系统启动中...
2025-07-18 17:39:14 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-18 17:39:14 | INFO     | __main__:lifespan:26 | PostgreSQL数据库连接成功
2025-07-18 17:39:14 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-18 17:39:30 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-18 17:39:30 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-18 17:39:30 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-18 17:39:31 | INFO     | __main__:lifespan:35 | RAG系统关闭中...
2025-07-18 17:39:35 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-18 17:39:37 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-18 17:39:37 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-18 17:39:37 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-18 17:39:37 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-18 17:39:43 | INFO     | __main__:lifespan:22 | RAG系统启动中...
2025-07-18 17:39:43 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-18 17:39:43 | INFO     | __main__:lifespan:26 | PostgreSQL数据库连接成功
2025-07-18 17:39:43 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-18 17:39:44 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-18 17:39:44 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-18 17:39:44 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-18 17:40:14 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:40:14 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 10 个结果
2025-07-18 17:40:14 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:40:14 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 10 个结果
2025-07-18 17:40:14 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:40:14 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 10 个结果
2025-07-18 17:40:14 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:40:14 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-18 17:40:14 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:40:14 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-18 17:40:14 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:40:14 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-18 17:40:14 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:40:14 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-18 17:40:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:40:52 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 10 个结果
2025-07-18 17:40:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:40:52 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 10 个结果
2025-07-18 17:40:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:40:52 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 10 个结果
2025-07-18 17:40:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:40:52 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-18 17:40:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:40:52 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-18 17:40:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:40:52 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-18 17:40:52 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:40:52 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-18 17:45:13 | INFO     | __main__:lifespan:35 | RAG系统关闭中...
2025-07-18 17:45:16 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-18 17:45:19 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-18 17:45:19 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-18 17:45:19 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-18 17:45:19 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-18 17:45:24 | INFO     | __main__:lifespan:22 | RAG系统启动中...
2025-07-18 17:45:24 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-18 17:45:24 | INFO     | __main__:lifespan:26 | PostgreSQL数据库连接成功
2025-07-18 17:45:24 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-18 17:45:25 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-18 17:45:25 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-18 17:45:25 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-18 17:45:34 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:45:34 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 10 个结果
2025-07-18 17:45:34 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:45:34 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 10 个结果
2025-07-18 17:45:34 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:45:34 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 10 个结果
2025-07-18 17:45:34 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:45:34 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-18 17:45:34 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:45:34 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-18 17:45:34 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:45:34 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-18 17:45:34 | INFO     | app.service.model_client:get_embeddings:129 | 嵌入生成成功，文本数量: 1
2025-07-18 17:45:34 | INFO     | app.database.qdrant_manager:search_vectors:128 | 向量搜索完成，返回 6 个结果
2025-07-18 17:52:26 | INFO     | __main__:lifespan:35 | RAG系统关闭中...
2025-07-18 17:52:30 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-18 17:52:32 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-18 17:52:32 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-18 17:52:32 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-18 17:52:32 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-18 17:52:38 | INFO     | __main__:lifespan:22 | RAG系统启动中...
2025-07-18 17:52:38 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-18 17:52:38 | INFO     | __main__:lifespan:26 | PostgreSQL数据库连接成功
2025-07-18 17:52:38 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-18 17:52:41 | INFO     | app.service.model_client:check_health:76 | LLM 模型服务正常
2025-07-18 17:52:41 | INFO     | app.service.model_client:check_health:85 | Embedding 模型服务正常
2025-07-18 17:52:41 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-18 18:18:23 | INFO     | __main__:lifespan:35 | RAG系统关闭中...
