2025-07-16 12:01:09 | INFO     | __main__:lifespan:17 | RAG系统启动中...
2025-07-16 12:01:09 | ERROR    | __main__:lifespan:23 | PostgreSQL数据库连接失败: 'PostgresManager' object has no attribute 'init_db'
2025-07-16 12:01:50 | INFO     | __main__:lifespan:17 | RAG系统启动中...
2025-07-16 12:01:50 | ERROR    | app.database.postgres_manager:initialize:45 | Tortoise ORM初始化失败: Unknown DB scheme: postgresql
2025-07-16 12:01:50 | ERROR    | __main__:lifespan:23 | PostgreSQL数据库连接失败: 数据库初始化失败: Unknown DB scheme: postgresql
2025-07-16 12:04:10 | INFO     | __main__:lifespan:17 | RAG系统启动中...
2025-07-16 12:04:10 | ERROR    | app.database.postgres_manager:initialize:45 | Tortoise ORM初始化失败: Unknown DB scheme: postgresql
2025-07-16 12:04:10 | ERROR    | __main__:lifespan:23 | PostgreSQL数据库连接失败: 数据库初始化失败: Unknown DB scheme: postgresql
2025-07-16 12:14:20 | INFO     | __main__:lifespan:17 | RAG系统启动中...
2025-07-16 12:14:20 | ERROR    | app.database.postgres_manager:initialize:45 | Tortoise ORM初始化失败: Unknown DB scheme: postgresql
2025-07-16 12:14:20 | ERROR    | __main__:lifespan:23 | PostgreSQL数据库连接失败: 数据库初始化失败: Unknown DB scheme: postgresql
2025-07-16 12:20:55 | INFO     | __main__:lifespan:17 | RAG系统启动中...
2025-07-16 12:20:55 | ERROR    | app.database.postgres_manager:initialize:45 | Tortoise ORM初始化失败: No module named 'asyncpg'
2025-07-16 12:20:55 | ERROR    | __main__:lifespan:23 | PostgreSQL数据库连接失败: 数据库初始化失败: No module named 'asyncpg'
2025-07-16 12:21:38 | INFO     | __main__:lifespan:17 | RAG系统启动中...
2025-07-16 12:21:38 | INFO     | app.database.postgres_manager:initialize:42 | Tortoise ORM初始化成功
2025-07-16 12:21:38 | INFO     | __main__:lifespan:21 | PostgreSQL数据库连接成功
2025-07-16 12:22:32 | INFO     | __main__:lifespan:26 | RAG系统关闭中...
2025-07-16 13:30:46 | INFO     | __main__:lifespan:17 | RAG系统启动中...
2025-07-16 13:30:46 | INFO     | app.database.postgres_manager:initialize:41 | Tortoise ORM初始化成功
2025-07-16 13:30:46 | INFO     | __main__:lifespan:21 | PostgreSQL数据库连接成功
2025-07-16 13:57:26 | INFO     | __main__:lifespan:26 | RAG系统关闭中...
2025-07-16 13:57:28 | INFO     | __main__:lifespan:18 | RAG系统启动中...
2025-07-16 13:57:28 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-16 13:57:28 | INFO     | __main__:lifespan:22 | PostgreSQL数据库连接成功
2025-07-16 13:57:28 | INFO     | app.database.minio_manager:initialize:31 | MinIO客户端初始化成功: localhost:19000
2025-07-16 13:57:33 | INFO     | __main__:lifespan:29 | RAG系统关闭中...
2025-07-16 13:58:10 | INFO     | __main__:lifespan:18 | RAG系统启动中...
2025-07-16 13:58:10 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-16 13:58:10 | INFO     | __main__:lifespan:22 | PostgreSQL数据库连接成功
2025-07-16 13:58:10 | INFO     | app.database.minio_manager:initialize:32 | MinIO客户端初始化成功: localhost:19000
2025-07-16 13:58:14 | INFO     | __main__:lifespan:29 | RAG系统关闭中...
2025-07-16 14:06:15 | INFO     | __main__:lifespan:18 | RAG系统启动中...
2025-07-16 14:06:15 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-16 14:06:15 | INFO     | __main__:lifespan:22 | PostgreSQL数据库连接成功
2025-07-16 14:06:15 | INFO     | app.database.minio_manager:initialize:31 | MinIO客户端初始化成功: localhost:19000
2025-07-16 15:00:17 | INFO     | __main__:lifespan:29 | RAG系统关闭中...
2025-07-16 15:00:19 | INFO     | __main__:lifespan:18 | RAG系统启动中...
2025-07-16 15:00:19 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-16 15:00:19 | INFO     | __main__:lifespan:22 | PostgreSQL数据库连接成功
2025-07-16 15:00:19 | INFO     | app.database.minio_manager:initialize:31 | MinIO客户端初始化成功: localhost:19000
2025-07-16 15:01:56 | INFO     | __main__:lifespan:29 | RAG系统关闭中...
2025-07-16 15:02:33 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-16 15:02:33 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-16 15:02:33 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-16 15:02:33 | INFO     | app.database.minio_manager:initialize:31 | MinIO客户端初始化成功: localhost:19000
2025-07-16 15:03:27 | INFO     | app.api.document_router:upload_document:28 | 开始处理上传文件: 文件501.txt
2025-07-16 15:03:27 | INFO     | app.service.document_upload:extract_document_metadata:44 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmpt0z1cgu1.txt
2025-07-16 15:03:27 | WARNING  | app.service.document_upload:extract_document_metadata:71 | 不支持的文件类型: .txt
2025-07-16 15:03:27 | INFO     | app.service.document_upload:extract_document_metadata:77 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmpt0z1cgu1.txt
2025-07-16 15:03:27 | INFO     | app.api.document_router:upload_document:48 | 成功提取元数据: 文件501.txt
2025-07-16 15:03:27 | ERROR    | app.database.minio_manager:upload_file:56 | 文件上传失败: C:\Users\<USER>\AppData\Local\Temp\tmpt0z1cgu1.txt -> documents/15dfa2343432fdc8d976fde5c9b1670d..txt, 错误: object bool can't be used in 'await' expression
2025-07-16 15:03:27 | ERROR    | app.api.document_router:upload_document:79 | 文档上传处理失败: object bool can't be used in 'await' expression
2025-07-16 15:08:49 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-16 15:08:52 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-16 15:08:52 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-16 15:08:52 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-16 15:08:52 | INFO     | app.database.minio_manager:initialize:31 | MinIO客户端初始化成功: localhost:19000
2025-07-16 15:08:59 | INFO     | app.api.document_router:upload_document:28 | 开始处理上传文件: 文件501.txt
2025-07-16 15:08:59 | INFO     | app.service.document_upload:extract_document_metadata:44 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmpu3r4io8l.txt
2025-07-16 15:08:59 | WARNING  | app.service.document_upload:extract_document_metadata:71 | 不支持的文件类型: .txt
2025-07-16 15:08:59 | INFO     | app.service.document_upload:extract_document_metadata:77 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmpu3r4io8l.txt
2025-07-16 15:08:59 | INFO     | app.api.document_router:upload_document:48 | 成功提取元数据: 文件501.txt
2025-07-16 15:08:59 | ERROR    | app.database.minio_manager:upload_file:56 | 文件上传失败: C:\Users\<USER>\AppData\Local\Temp\tmpu3r4io8l.txt -> documents/15dfa2343432fdc8d976fde5c9b1670d..txt, 错误: object NoneType can't be used in 'await' expression
2025-07-16 15:08:59 | ERROR    | app.api.document_router:upload_document:79 | 文档上传处理失败: object NoneType can't be used in 'await' expression
2025-07-16 15:09:35 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-16 15:09:37 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-16 15:09:37 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-16 15:09:37 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-16 15:09:37 | INFO     | app.database.minio_manager:initialize:31 | MinIO客户端初始化成功: localhost:19000
2025-07-16 15:09:41 | INFO     | app.api.document_router:upload_document:28 | 开始处理上传文件: 文件501.txt
2025-07-16 15:09:41 | INFO     | app.service.document_upload:extract_document_metadata:44 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmpodos8gxe.txt
2025-07-16 15:09:41 | WARNING  | app.service.document_upload:extract_document_metadata:71 | 不支持的文件类型: .txt
2025-07-16 15:09:41 | INFO     | app.service.document_upload:extract_document_metadata:77 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmpodos8gxe.txt
2025-07-16 15:09:41 | INFO     | app.api.document_router:upload_document:48 | 成功提取元数据: 文件501.txt
2025-07-16 15:09:41 | ERROR    | app.database.minio_manager:upload_file:56 | 文件上传失败: C:\Users\<USER>\AppData\Local\Temp\tmpodos8gxe.txt -> documents/15dfa2343432fdc8d976fde5c9b1670d..txt, 错误: object ObjectWriteResult can't be used in 'await' expression
2025-07-16 15:09:41 | ERROR    | app.api.document_router:upload_document:79 | 文档上传处理失败: object ObjectWriteResult can't be used in 'await' expression
2025-07-16 15:09:49 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-16 15:09:51 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-16 15:09:51 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-16 15:09:51 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-16 15:09:51 | INFO     | app.database.minio_manager:initialize:31 | MinIO客户端初始化成功: localhost:19000
2025-07-16 15:09:54 | INFO     | app.api.document_router:upload_document:28 | 开始处理上传文件: 文件501.txt
2025-07-16 15:09:54 | INFO     | app.service.document_upload:extract_document_metadata:44 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmpxi28xff_.txt
2025-07-16 15:09:54 | WARNING  | app.service.document_upload:extract_document_metadata:71 | 不支持的文件类型: .txt
2025-07-16 15:09:54 | INFO     | app.service.document_upload:extract_document_metadata:77 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmpxi28xff_.txt
2025-07-16 15:09:54 | INFO     | app.api.document_router:upload_document:48 | 成功提取元数据: 文件501.txt
2025-07-16 15:09:54 | INFO     | app.database.minio_manager:upload_file:51 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmpxi28xff_.txt -> documents/15dfa2343432fdc8d976fde5c9b1670d..txt
2025-07-16 15:11:00 | INFO     | app.api.document_router:upload_document:28 | 开始处理上传文件: 文件501.txt
2025-07-16 15:20:33 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-16 15:20:36 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-16 15:20:36 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-16 15:20:36 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-16 15:20:36 | INFO     | app.database.minio_manager:initialize:31 | MinIO客户端初始化成功: localhost:19000
2025-07-16 15:20:41 | INFO     | app.api.document_router:upload_document:25 | 开始处理上传文件: 文件501.txt
2025-07-16 15:20:48 | INFO     | app.api.document_router:upload_document:25 | 开始处理上传文件: 文件508.txt
2025-07-16 15:20:48 | INFO     | app.service.document_upload:extract_document_metadata:52 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmpp2u02e5q.txt
2025-07-16 15:20:48 | WARNING  | app.service.document_upload:extract_document_metadata:79 | 不支持的文件类型: .txt
2025-07-16 15:20:48 | INFO     | app.service.document_upload:extract_document_metadata:85 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmpp2u02e5q.txt
2025-07-16 15:20:48 | INFO     | app.service.document_upload:upload_document_service:175 | 成功提取元数据: 文件508.txt
2025-07-16 15:20:48 | INFO     | app.database.minio_manager:upload_file:51 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmpp2u02e5q.txt -> documents/6297507ae70e5972cdd39305e4921f22..txt
2025-07-16 15:23:24 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-16 15:23:26 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-16 15:23:26 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-16 15:23:26 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-16 15:23:26 | INFO     | app.database.minio_manager:initialize:31 | MinIO客户端初始化成功: localhost:19000
2025-07-16 15:23:31 | INFO     | app.api.document_router:upload_document:25 | 开始处理上传文件: 文件508.txt
2025-07-16 15:23:40 | INFO     | app.api.document_router:upload_document:25 | 开始处理上传文件: 文件510.txt
2025-07-16 15:23:40 | INFO     | app.service.document_upload:extract_document_metadata:52 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmpi6w8ze3h.txt
2025-07-16 15:23:40 | WARNING  | app.service.document_upload:extract_document_metadata:79 | 不支持的文件类型: .txt
2025-07-16 15:23:40 | INFO     | app.service.document_upload:extract_document_metadata:85 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmpi6w8ze3h.txt
2025-07-16 15:23:40 | INFO     | app.service.document_upload:upload_document_service:175 | 成功提取元数据: 文件510.txt
2025-07-16 15:23:40 | INFO     | app.database.minio_manager:upload_file:51 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmpi6w8ze3h.txt -> documents/4652cfbf4cd36f26fde6cb8e03282b9e..txt
2025-07-16 15:41:12 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-16 15:42:21 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-16 15:42:21 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-16 15:42:21 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-16 15:42:21 | INFO     | app.database.minio_manager:initialize:31 | MinIO客户端初始化成功: localhost:19000
2025-07-16 15:42:27 | INFO     | app.api.document_router:upload_document:26 | 开始处理上传文件: 文件510.txt
2025-07-16 15:42:27 | INFO     | app.service.document_upload:extract_document_metadata:52 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmpvp6w3rqi.txt
2025-07-16 15:42:27 | WARNING  | app.service.document_upload:extract_document_metadata:79 | 不支持的文件类型: .txt
2025-07-16 15:42:27 | INFO     | app.service.document_upload:extract_document_metadata:85 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmpvp6w3rqi.txt
2025-07-16 15:42:27 | INFO     | app.service.document_upload:upload_document_service:175 | 成功提取元数据: 文件510.txt
2025-07-16 15:42:27 | INFO     | app.database.minio_manager:upload_file:51 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmpvp6w3rqi.txt -> documents/4652cfbf4cd36f26fde6cb8e03282b9e..txt
2025-07-16 15:43:40 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-16 15:43:55 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-16 15:43:55 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-16 15:43:55 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-16 15:43:55 | INFO     | app.database.minio_manager:initialize:31 | MinIO客户端初始化成功: localhost:19000
2025-07-16 15:50:09 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-16 15:50:12 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-16 15:50:12 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-16 15:50:12 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-16 15:50:12 | INFO     | app.database.minio_manager:initialize:31 | MinIO客户端初始化成功: localhost:19000
2025-07-16 15:50:53 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: 'ParseDocumentRequest' object has no attribute 'file_hashes'
2025-07-16 15:51:05 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-16 15:51:07 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-16 15:51:07 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-16 15:51:07 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-16 15:51:07 | INFO     | app.database.minio_manager:initialize:31 | MinIO客户端初始化成功: localhost:19000
2025-07-16 15:57:56 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-16 16:52:28 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-16 16:52:28 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-16 16:52:28 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-16 16:52:28 | INFO     | app.database.minio_manager:initialize:31 | MinIO客户端初始化成功: localhost:19000
2025-07-16 16:53:28 | INFO     | app.api.document_router:upload_document:26 | 开始处理上传文件: 安全内参.xlsx
2025-07-16 16:53:28 | INFO     | app.service.document_upload:extract_document_metadata:52 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmp18mqte2m.xlsx
2025-07-16 16:53:28 | ERROR    | app.service.document_upload:_extract_excel_metadata:131 | Excel文档元数据提取失败: 'DocumentProperties' object has no attribute 'company'
2025-07-16 16:53:28 | INFO     | app.service.document_upload:extract_document_metadata:85 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmp18mqte2m.xlsx
2025-07-16 16:53:28 | INFO     | app.service.document_upload:upload_document_service:175 | 成功提取元数据: 安全内参.xlsx
2025-07-16 16:53:28 | INFO     | app.database.minio_manager:upload_file:51 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmp18mqte2m.xlsx -> documents/cc2b58dab59410f63cbee8e30f9201b6.xlsx
2025-07-16 16:53:28 | ERROR    | app.api.document_router:upload_document:32 | 文档上传处理失败: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp18mqte2m.xlsx'
2025-07-16 16:53:38 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-16 16:53:42 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-16 16:53:42 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-16 16:53:42 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-16 16:53:42 | INFO     | app.database.minio_manager:initialize:31 | MinIO客户端初始化成功: localhost:19000
2025-07-16 16:53:48 | INFO     | app.api.document_router:upload_document:26 | 开始处理上传文件: 安全内参.xlsx
2025-07-16 16:54:03 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: 3 validation errors for DocumentConverter.convert
1.lax-or-strict[lax=union[json-or-python[json=function-after[path_validator(), str],python=is-instance[Path]],function-after[path_validator(), str]],strict=json-or-python[json=function-after[path_validator(), str],python=is-instance[Path]]]
  Input should be an instance of Path [type=is_instance_of, input_value=<coroutine object MinioMa...e at 0x0000014AD4E47E20>, input_type=coroutine]
    For further information visit https://errors.pydantic.dev/2.11/v/is_instance_of
1.str
  Input should be a valid string [type=string_type, input_value=<coroutine object MinioMa...e at 0x0000014AD4E47E20>, input_type=coroutine]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
1.DocumentStream
  Input should be a valid dictionary or instance of DocumentStream [type=model_type, input_value=<coroutine object MinioMa...e at 0x0000014AD4E47E20>, input_type=coroutine]
    For further information visit https://errors.pydantic.dev/2.11/v/model_type
2025-07-16 16:56:46 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-16 16:56:50 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-16 16:56:50 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-16 16:56:50 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-16 16:56:50 | INFO     | app.database.minio_manager:initialize:31 | MinIO客户端初始化成功: localhost:19000
2025-07-16 16:57:01 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: 3 validation errors for DocumentConverter.convert
1.lax-or-strict[lax=union[json-or-python[json=function-after[path_validator(), str],python=is-instance[Path]],function-after[path_validator(), str]],strict=json-or-python[json=function-after[path_validator(), str],python=is-instance[Path]]]
  Input should be an instance of Path [type=is_instance_of, input_value=<urllib3.response.HTTPRes...t at 0x000001B46D303CD0>, input_type=HTTPResponse]
    For further information visit https://errors.pydantic.dev/2.11/v/is_instance_of
1.str
  Input should be a valid string [type=string_type, input_value=<urllib3.response.HTTPRes...t at 0x000001B46D303CD0>, input_type=HTTPResponse]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
1.DocumentStream
  Input should be a valid dictionary or instance of DocumentStream [type=model_type, input_value=<urllib3.response.HTTPRes...t at 0x000001B46D303CD0>, input_type=HTTPResponse]
    For further information visit https://errors.pydantic.dev/2.11/v/model_type
2025-07-16 17:07:15 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-16 17:07:19 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-16 17:07:19 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-16 17:07:19 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-16 17:07:19 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-16 17:07:41 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: File format not allowed: 安全内参.xlsx
2025-07-16 17:07:58 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-16 17:08:03 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-16 17:08:03 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-16 17:08:03 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-16 17:08:03 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-16 17:08:08 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: File format not allowed: cc2b58dab59410f63cbee8e30f9201b6.xlsx
2025-07-16 17:09:43 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-16 17:09:46 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-16 17:09:46 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-16 17:09:46 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-16 17:09:46 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-16 17:09:56 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: 'ConversionResult' object has no attribute 'export_to_markdown'
2025-07-16 17:13:37 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-16 17:13:40 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-16 17:13:40 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-16 17:13:40 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-16 17:13:40 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-16 17:14:31 | INFO     | app.api.document_router:upload_document:26 | 开始处理上传文件: 1.pdf
2025-07-16 17:14:31 | INFO     | app.service.document_upload:extract_document_metadata:52 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmppwz29gxu.pdf
2025-07-16 17:14:31 | WARNING  | app.service.document_upload:extract_document_metadata:79 | 不支持的文件类型: .pdf
2025-07-16 17:14:31 | INFO     | app.service.document_upload:extract_document_metadata:85 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmppwz29gxu.pdf
2025-07-16 17:14:31 | INFO     | app.service.document_upload:upload_document_service:175 | 成功提取元数据: 1.pdf
2025-07-16 17:14:31 | INFO     | app.database.minio_manager:upload_file:53 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmppwz29gxu.pdf -> documents/05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-16 17:14:46 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: tesserocr is not correctly installed. Please install it via `pip install tesserocr` to use this OCR engine. Note that tesserocr might have to be manually compiled for working with your Tesseract installation. The Docling documentation provides examples for it. Alternatively, Docling has support for other OCR engines. See the documentation: https://docling-project.github.io/docling/installation/
2025-07-16 17:15:18 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-16 17:32:27 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-16 17:32:27 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-16 17:32:27 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-16 17:32:27 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-16 17:34:27 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-16 17:34:27 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-16 17:34:27 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-16 17:34:27 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-16 17:49:45 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: (MaxRetryError("HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url: /api/models/ds4sd/docling-layout-old/revision/main (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1010)')))"), '(Request ID: 9849cf5e-2dec-459b-ac75-29be4392a8da)')
