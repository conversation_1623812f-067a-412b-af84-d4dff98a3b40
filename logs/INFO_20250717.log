2025-07-17 10:53:54 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 10:53:58 | ERROR    | app.database.postgres_manager:initialize:43 | Tortoise ORM初始化失败: [WinError 1225] 远程计算机拒绝网络连接。
2025-07-17 10:53:58 | ERROR    | __main__:lifespan:28 | 系统初始化失败: 数据库初始化失败: [WinError 1225] 远程计算机拒绝网络连接。
2025-07-17 10:54:09 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 10:54:09 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 10:54:09 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 10:54:09 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 10:55:28 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: 'PdfPipelineOptions' object has no attribute 'vlm_options'
2025-07-17 10:56:26 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 10:56:31 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 10:56:31 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 10:56:31 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 10:56:31 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 10:56:35 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: 'PdfPipelineOptions' object has no attribute 'vlm_options'
2025-07-17 10:56:58 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 10:57:02 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 10:57:02 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 10:57:02 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 10:57:02 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 10:57:08 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: 'PdfPipelineOptions' object has no attribute 'vlm_options'
2025-07-17 10:57:41 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 10:58:32 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 10:58:33 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 10:58:33 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 10:58:33 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 10:58:40 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: 'PdfPipelineOptions' object has no attribute 'vlm_options'
2025-07-17 10:59:03 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 10:59:06 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 10:59:06 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 10:59:06 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 10:59:06 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 10:59:11 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: 'PdfPipelineOptions' object has no attribute 'vlm_options'
2025-07-17 11:03:41 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 11:10:26 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 11:10:26 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 11:10:26 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 11:10:26 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 11:10:32 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: 'PdfPipelineOptions' object has no attribute 'vlm_options'
2025-07-17 11:11:12 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 11:11:15 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 11:11:15 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 11:11:15 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 11:11:15 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 11:11:48 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: An error happened while trying to locate the files on the Hub and we cannot find the appropriate snapshot folder for the specified revision on the local disk. Please check your internet connection and try again.
2025-07-17 11:12:19 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 11:12:23 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 11:12:23 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 11:12:23 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 11:12:23 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 11:12:56 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: An error happened while trying to locate the files on the Hub and we cannot find the appropriate snapshot folder for the specified revision on the local disk. Please check your internet connection and try again.
2025-07-17 11:13:25 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: Missing safe tensors file: C:\Users\<USER>\.cache\huggingface\hub\models--ds4sd--docling-layout-egret-large\snapshots\b48b7c9a8bf8157e32ad368c53b4f2ba60faca52\models\layout\egret-large.onnx\model.safetensors
2025-07-17 11:13:40 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 11:13:44 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 11:13:44 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 11:13:44 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 11:13:44 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 11:14:23 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: (MaxRetryError("HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url: /api/models/ds4sd/docling-layout-egret-large/revision/main (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1010)')))"), '(Request ID: 4eed535d-84c1-491b-8737-8bf14d6c0518)')
2025-07-17 11:17:45 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 11:21:19 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 11:21:19 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 11:21:19 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 11:21:19 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 11:21:59 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: (MaxRetryError("HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url: /api/models/ds4sd/docling-layout-old/revision/main (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1010)')))"), '(Request ID: 3e05b3f8-6b94-49e3-9d14-b7d24472db61)')
2025-07-17 11:22:43 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 11:22:48 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 11:22:48 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 11:22:48 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 11:22:48 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 11:23:29 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: (MaxRetryError("HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url: /api/models/ds4sd/docling-layout-old/revision/main (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1010)')))"), '(Request ID: 67d36db5-194e-4ef4-b1d3-b039ad55b291)')
2025-07-17 11:24:43 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 11:24:47 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 11:24:47 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 11:24:47 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 11:24:47 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 11:25:00 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: (MaxRetryError("HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url: /api/models/ds4sd/SmolDocling-256M-preview/revision/main (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1010)')))"), '(Request ID: 25c60e5d-148f-49dc-ad89-1c1bb9a9d646)')
2025-07-17 11:26:01 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 11:26:05 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 11:26:05 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 11:26:05 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 11:26:05 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 11:26:36 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: An error happened while trying to locate the files on the Hub and we cannot find the appropriate snapshot folder for the specified revision on the local disk. Please check your internet connection and try again.
2025-07-17 11:28:58 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: An error happened while trying to locate the file on the Hub and we cannot find the requested files in the local cache. Please check your connection and try again or make sure your Internet connection is on.
2025-07-17 11:30:10 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 11:30:14 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 11:30:14 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 11:30:14 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 11:30:14 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 11:30:57 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: (MaxRetryError("HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url: /api/models/ds4sd/docling-layout-old/revision/main (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1010)')))"), '(Request ID: 668b586d-7f26-4274-a485-265960467f77)')
2025-07-17 11:32:36 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 11:32:39 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 11:32:39 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 11:32:39 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 11:32:39 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 11:32:43 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: 'PdfPipelineOptions' object has no attribute 'execute'
2025-07-17 12:01:02 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 12:01:06 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 12:01:06 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 12:01:06 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 12:01:06 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 12:01:15 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: 'PdfPipelineOptions' object has no attribute 'execute'
2025-07-17 12:01:17 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: 'PdfPipelineOptions' object has no attribute 'execute'
2025-07-17 12:02:45 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 12:02:48 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 12:02:49 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 12:02:49 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 12:02:49 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 12:03:13 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: Repo id must use alphanumeric chars or '-', '_', '.', '--' and '..' are forbidden, '-' and '.' cannot start or end the name, max length is 96: ''.
2025-07-17 12:03:49 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 12:03:52 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 12:03:52 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 12:03:52 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 12:03:52 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 12:04:48 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: (MaxRetryError("HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url: /api/models/ds4sd/docling-layout-old/revision/main (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1010)')))"), '(Request ID: 9c1cc0a5-0337-478c-a67c-befbf363f31c)')
2025-07-17 12:05:40 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 12:05:44 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 12:05:44 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 12:05:44 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 12:05:44 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 12:06:02 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: Repo id must use alphanumeric chars or '-', '_', '.', '--' and '..' are forbidden, '-' and '.' cannot start or end the name, max length is 96: ''.
2025-07-17 12:06:31 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 12:06:34 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 12:06:35 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 12:06:35 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 12:06:35 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 12:07:13 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: (MaxRetryError("HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url: /api/models/ds4sd/docling-layout-egret-large/revision/main (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1010)')))"), '(Request ID: 59e48507-a093-44fa-ad29-198de1046a55)')
2025-07-17 12:09:28 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 12:12:41 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 12:12:41 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 12:12:41 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 12:12:41 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 12:12:52 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: Repo id must use alphanumeric chars or '-', '_', '.', '--' and '..' are forbidden, '-' and '.' cannot start or end the name, max length is 96: ''.
2025-07-17 12:13:01 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 12:13:05 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 12:13:05 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 12:13:05 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 12:13:05 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 12:13:21 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: Repo id must use alphanumeric chars or '-', '_', '.', '--' and '..' are forbidden, '-' and '.' cannot start or end the name, max length is 96: '-'.
2025-07-17 12:13:47 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 12:13:51 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 12:13:51 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 12:13:51 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 12:13:51 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 12:14:32 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: (MaxRetryError("HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url: /api/models/ds4sd/docling-layout-egret-large/revision/main (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1010)')))"), '(Request ID: 1710a1f6-aaee-4c53-ab1e-72ad9eaa44dc)')
2025-07-17 12:17:21 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 12:17:28 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 12:17:28 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 12:17:28 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 12:17:28 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 12:17:50 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: (ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None)), '(Request ID: 96772529-5d0d-4847-876c-a2556aea87af)')
2025-07-17 12:18:42 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 12:18:46 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 12:18:46 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 12:18:46 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 12:18:46 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 12:21:24 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 12:21:28 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 12:21:28 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 12:21:28 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 12:21:28 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 12:22:44 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: An error happened while trying to locate the file on the Hub and we cannot find the requested files in the local cache. Please check your connection and try again or make sure your Internet connection is on.
2025-07-17 12:22:49 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 12:24:11 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 12:24:11 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 12:24:11 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 12:24:11 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 12:24:21 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: Using a `device_map`, `tp_plan`, `torch.device` context manager or setting `torch.set_default_device(device)` requires `accelerate`. You can install it with `pip install accelerate`
2025-07-17 12:24:34 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 12:24:59 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 12:24:59 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 12:24:59 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 12:24:59 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 13:00:01 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 13:00:01 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 13:00:01 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 13:00:01 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 13:01:27 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 13:01:27 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 13:01:27 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 13:01:27 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 13:12:05 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 13:12:05 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 13:12:05 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 13:12:05 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 13:12:18 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: FlashAttention2 has been toggled on, but it cannot be used due to the following error: the package flash_attn seems to be not installed. Please refer to the documentation of https://huggingface.co/docs/transformers/perf_infer_gpu_one#flashattention-2 to install Flash Attention 2.
2025-07-17 13:13:02 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 13:13:06 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 13:13:06 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 13:13:06 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 13:13:06 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 13:30:58 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 13:30:58 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 13:30:58 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 13:30:58 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 13:31:06 | INFO     | app.api.document_router:upload_document:26 | 开始处理上传文件: 0630 (1).doc
2025-07-17 13:31:06 | INFO     | app.service.document_upload:extract_document_metadata:52 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmpprn3loio.doc
2025-07-17 13:31:06 | ERROR    | app.service.document_upload:_extract_word_metadata:108 | Word文档元数据提取失败: file 'C:\Users\<USER>\AppData\Local\Temp\tmpprn3loio.doc' is not a Word file, content type is 'application/vnd.openxmlformats-officedocument.themeManager+xml'
2025-07-17 13:31:06 | INFO     | app.service.document_upload:extract_document_metadata:85 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmpprn3loio.doc
2025-07-17 13:31:06 | INFO     | app.service.document_upload:upload_document_service:175 | 成功提取元数据: 0630 (1).doc
2025-07-17 13:31:06 | INFO     | app.database.minio_manager:upload_file:53 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmpprn3loio.doc -> documents/ca7c1d9981145adc6129d23350128fa3.doc
2025-07-17 13:31:22 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: File format not allowed: ca7c1d9981145adc6129d23350128fa3.doc
2025-07-17 13:32:09 | INFO     | app.api.document_router:upload_document:26 | 开始处理上传文件: 0630 (1).docx
2025-07-17 13:32:09 | INFO     | app.service.document_upload:extract_document_metadata:52 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmp3_gff456.docx
2025-07-17 13:32:09 | ERROR    | app.service.document_upload:_extract_word_metadata:108 | Word文档元数据提取失败: 'CoreProperties' object has no attribute 'company'
2025-07-17 13:32:09 | INFO     | app.service.document_upload:extract_document_metadata:85 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmp3_gff456.docx
2025-07-17 13:32:09 | INFO     | app.service.document_upload:upload_document_service:175 | 成功提取元数据: 0630 (1).docx
2025-07-17 13:32:09 | INFO     | app.database.minio_manager:upload_file:53 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmp3_gff456.docx -> documents/112b72453628fe8c00a8af8e0996ca91.docx
2025-07-17 13:37:01 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 13:37:06 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 13:37:06 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 13:37:06 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 13:37:06 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 13:39:35 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 13:39:39 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 13:39:39 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 13:39:39 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 13:39:39 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 13:39:43 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 13:46:43 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 13:46:43 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 13:46:43 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 13:46:43 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 13:48:00 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: An error happened while trying to locate the files on the Hub and we cannot find the appropriate snapshot folder for the specified revision on the local disk. Please check your internet connection and try again.
2025-07-17 13:49:17 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 13:49:21 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 13:49:21 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 13:49:21 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 13:49:21 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 13:49:33 | INFO     | app.service.parse_document:parse_document_service:92 | 开始解析文件: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-17 13:50:23 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: [WinError 1314] 客户端没有所需的特权。: '..\\..\\blobs\\a6344aac8c09253b3b630fb776ae94478aa0275b' -> 'C:\\Users\\<USER>\\.cache\\huggingface\\hub\\models--HuggingFaceTB--SmolVLM-256M-Instruct\\snapshots\\7e3e67edbbed1bf9888184d9df282b700a323964\\.gitattributes'
2025-07-17 13:50:54 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 13:52:23 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 13:52:23 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 13:52:23 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 13:52:23 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 13:52:28 | INFO     | app.service.parse_document:parse_document_service:92 | 开始解析文件: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-17 13:52:57 | INFO     | app.service.parse_document:parse_document_service:100 | 文件解析完成: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-17 13:52:57 | INFO     | app.service.parse_document:parse_document_service:102 | 文件转换为markdown: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-17 13:52:57 | INFO     | app.service.parse_document:parse_document_service:111 | 文件转换为markdown: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-17 13:56:49 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 13:56:53 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 13:56:54 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 13:56:54 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 13:56:54 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 13:57:02 | INFO     | app.service.parse_document:parse_document_service:92 | 开始解析文件: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-17 13:57:32 | INFO     | app.service.parse_document:parse_document_service:100 | 文件解析完成: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-17 13:57:32 | INFO     | app.service.parse_document:parse_document_service:102 | 文件转换为markdown: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-17 13:57:32 | INFO     | app.service.parse_document:parse_document_service:111 | 文件转换为markdown: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-17 14:06:34 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 14:06:38 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 14:06:38 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 14:06:38 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 14:06:38 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 14:06:41 | INFO     | app.service.parse_document:parse_document_service:104 | 开始解析文件: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-17 14:12:52 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 14:12:59 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 14:12:59 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 14:12:59 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 14:12:59 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 14:13:09 | INFO     | app.service.parse_document:parse_document_service:79 | 开始解析文件: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-17 14:14:58 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: We couldn't connect to 'https://huggingface.co' to load the files, and couldn't find them in the cached files.
Check your internet connection or see how to run the library in offline mode at 'https://huggingface.co/docs/transformers/installation#offline-mode'.
2025-07-17 14:15:25 | INFO     | app.service.parse_document:parse_document_service:79 | 开始解析文件: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-17 14:30:52 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 14:31:01 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 14:31:01 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 14:31:01 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 14:31:01 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 14:31:06 | INFO     | app.service.parse_document:parse_document_service:83 | 开始解析文件: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-17 14:31:31 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: module 'docling_core.transforms.chunker' has no attribute 'contextualize'
2025-07-17 14:32:23 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 14:32:31 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 14:32:31 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 14:32:31 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 14:32:31 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 14:32:37 | INFO     | app.service.parse_document:parse_document_service:83 | 开始解析文件: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-17 14:34:42 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 14:34:50 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 14:34:50 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 14:34:50 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 14:34:50 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 14:34:54 | INFO     | app.service.parse_document:parse_document_service:90 | 开始解析文件: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-17 14:38:21 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 14:38:29 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 14:38:29 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 14:38:29 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 14:38:29 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 14:38:39 | INFO     | app.service.parse_document:parse_document_service:90 | 开始解析文件: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-17 14:40:22 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 14:40:30 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 14:40:30 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 14:40:30 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 14:40:30 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 14:40:35 | INFO     | app.service.parse_document:parse_document_service:90 | 开始解析文件: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-17 14:45:23 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 14:45:32 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 14:45:32 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 14:45:32 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 14:45:32 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 14:45:40 | INFO     | app.service.parse_document:parse_document_service:90 | 开始解析文件: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-17 14:46:08 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: 1 validation error for ChunkingDocSerializer
doc
  Field required [type=missing, input_value={}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-07-17 14:47:07 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 14:47:15 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 14:47:15 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 14:47:15 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 14:47:15 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 14:47:24 | INFO     | app.service.parse_document:parse_document_service:90 | 开始解析文件: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-17 14:50:45 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 14:50:54 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 14:50:54 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 14:50:54 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 14:50:54 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 14:51:00 | INFO     | app.service.parse_document:parse_document_service:90 | 开始解析文件: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-17 14:59:03 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 14:59:11 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 14:59:11 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 14:59:11 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 14:59:11 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 14:59:16 | INFO     | app.service.parse_document:parse_document_service:90 | 开始解析文件: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-17 15:00:02 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 15:00:10 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 15:00:10 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 15:00:10 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 15:00:10 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 15:00:18 | INFO     | app.service.parse_document:parse_document_service:90 | 开始解析文件: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-17 15:03:41 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 15:03:50 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 15:03:50 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 15:03:50 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 15:03:50 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 15:03:59 | INFO     | app.service.parse_document:parse_document_service:90 | 开始解析文件: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-17 15:05:44 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 15:05:53 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 15:05:53 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 15:05:53 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 15:05:53 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 15:06:04 | INFO     | app.service.parse_document:parse_document_service:90 | 开始解析文件: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-17 15:11:26 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 15:11:35 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 15:11:35 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 15:11:35 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 15:11:35 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 15:11:52 | INFO     | app.service.parse_document:parse_document_service:91 | 开始解析文件: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-17 15:19:02 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 15:19:29 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 15:19:29 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 15:19:29 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 15:19:29 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 15:19:34 | INFO     | app.service.parse_document:parse_document_service:86 | 开始解析文件: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-17 15:22:06 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 15:22:15 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 15:22:15 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 15:22:15 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 15:22:15 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 15:22:34 | INFO     | app.service.parse_document:parse_document_service:86 | 开始解析文件: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-17 15:24:19 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 15:24:27 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 15:24:27 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 15:24:27 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 15:24:27 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 15:24:31 | INFO     | app.service.parse_document:parse_document_service:86 | 开始解析文件: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-17 15:25:57 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 15:26:05 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 15:26:05 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 15:26:05 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 15:26:05 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 15:26:15 | INFO     | app.service.parse_document:parse_document_service:86 | 开始解析文件: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-17 15:27:54 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 15:28:03 | INFO     | __main__:lifespan:20 | RAG系统启动中...
2025-07-17 15:28:03 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 15:28:03 | INFO     | __main__:lifespan:24 | PostgreSQL数据库连接成功
2025-07-17 15:28:03 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 15:28:11 | INFO     | app.service.parse_document:parse_document_service:86 | 开始解析文件: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-17 15:39:15 | INFO     | __main__:lifespan:31 | RAG系统关闭中...
2025-07-17 15:55:47 | INFO     | app.service.model_client:_init_clients:48 | 模型客户端初始化完成:
2025-07-17 15:55:47 | INFO     | app.service.model_client:_init_clients:49 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-17 15:55:47 | INFO     | app.service.model_client:_init_clients:50 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-17 15:55:47 | INFO     | app.service.model_client:_init_clients:51 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-17 15:55:47 | INFO     | __main__:lifespan:21 | RAG系统启动中...
2025-07-17 15:55:47 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 15:55:47 | INFO     | __main__:lifespan:25 | PostgreSQL数据库连接成功
2025-07-17 15:55:47 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 15:55:47 | ERROR    | app.service.model_client:check_health:81 | Embedding 模型服务不可用
2025-07-17 15:55:47 | WARNING  | __main__:lifespan:32 | vLLM服务连接失败，请检查服务状态
2025-07-17 15:55:56 | INFO     | __main__:lifespan:37 | RAG系统关闭中...
2025-07-17 15:58:20 | INFO     | app.service.model_client:_init_clients:48 | 模型客户端初始化完成:
2025-07-17 15:58:20 | INFO     | app.service.model_client:_init_clients:49 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-17 15:58:20 | INFO     | app.service.model_client:_init_clients:50 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-17 15:58:20 | INFO     | app.service.model_client:_init_clients:51 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-17 15:58:20 | INFO     | __main__:lifespan:21 | RAG系统启动中...
2025-07-17 15:58:20 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 15:58:20 | INFO     | __main__:lifespan:25 | PostgreSQL数据库连接成功
2025-07-17 15:58:20 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 15:58:22 | INFO     | app.service.model_client:check_health:69 | LLM 模型服务正常
2025-07-17 15:58:22 | INFO     | app.service.model_client:check_health:78 | Embedding 模型服务正常
2025-07-17 15:58:22 | WARNING  | app.service.model_client:check_health:84 | Rerank 模型服务不可用
2025-07-17 15:58:22 | INFO     | __main__:lifespan:30 | vLLM服务连接正常
2025-07-17 16:01:17 | INFO     | __main__:lifespan:37 | RAG系统关闭中...
2025-07-17 16:01:26 | INFO     | app.service.model_client:_init_clients:48 | 模型客户端初始化完成:
2025-07-17 16:01:26 | INFO     | app.service.model_client:_init_clients:49 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-17 16:01:26 | INFO     | app.service.model_client:_init_clients:50 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-17 16:01:26 | INFO     | app.service.model_client:_init_clients:51 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-17 16:01:26 | INFO     | __main__:lifespan:21 | RAG系统启动中...
2025-07-17 16:01:26 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 16:01:26 | INFO     | __main__:lifespan:25 | PostgreSQL数据库连接成功
2025-07-17 16:01:26 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 16:01:36 | INFO     | __main__:lifespan:34 | RAG系统关闭中...
2025-07-17 16:02:29 | INFO     | app.service.model_client:_init_clients:48 | 模型客户端初始化完成:
2025-07-17 16:02:29 | INFO     | app.service.model_client:_init_clients:49 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-17 16:02:29 | INFO     | app.service.model_client:_init_clients:50 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-17 16:02:29 | INFO     | app.service.model_client:_init_clients:51 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-17 16:02:29 | INFO     | __main__:lifespan:21 | RAG系统启动中...
2025-07-17 16:02:29 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 16:02:29 | INFO     | __main__:lifespan:25 | PostgreSQL数据库连接成功
2025-07-17 16:02:29 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 16:02:30 | INFO     | app.service.model_client:check_health:69 | LLM 模型服务正常
2025-07-17 16:02:30 | INFO     | app.service.model_client:check_health:78 | Embedding 模型服务正常
2025-07-17 16:02:30 | WARNING  | app.service.model_client:check_health:84 | Rerank 模型服务不可用
2025-07-17 16:02:36 | INFO     | app.service.parse_document:parse_document_service:87 | 开始解析文件: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-17 16:04:10 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: (MaxRetryError("HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url: /api/models/Qwen/Qwen3-Embedding-8B/tree/main/additional_chat_templates?recursive=False&expand=False (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001F71C5157C0>, 'Connection to huggingface.co timed out. (connect timeout=None)'))"), '(Request ID: 32b128a6-aa21-42b1-98f6-a4dc24bfa600)')
2025-07-17 16:06:56 | INFO     | __main__:lifespan:34 | RAG系统关闭中...
2025-07-17 16:07:06 | INFO     | app.service.model_client:_init_clients:48 | 模型客户端初始化完成:
2025-07-17 16:07:06 | INFO     | app.service.model_client:_init_clients:49 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-17 16:07:06 | INFO     | app.service.model_client:_init_clients:50 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-17 16:07:06 | INFO     | app.service.model_client:_init_clients:51 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-17 16:07:06 | INFO     | __main__:lifespan:21 | RAG系统启动中...
2025-07-17 16:07:07 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 16:07:07 | INFO     | __main__:lifespan:25 | PostgreSQL数据库连接成功
2025-07-17 16:07:07 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 16:07:08 | INFO     | app.service.model_client:check_health:69 | LLM 模型服务正常
2025-07-17 16:07:08 | INFO     | app.service.model_client:check_health:78 | Embedding 模型服务正常
2025-07-17 16:07:08 | WARNING  | app.service.model_client:check_health:84 | Rerank 模型服务不可用
2025-07-17 16:07:11 | INFO     | app.service.parse_document:parse_document_service:87 | 开始解析文件: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-17 16:08:15 | INFO     | app.service.parse_document:parse_document_service:105 | 共有15 片
2025-07-17 16:08:15 | ERROR    | app.service.model_client:get_embeddings:125 | 嵌入生成失败: 'Settings' object has no attribute 'embedding_model'
2025-07-17 16:08:15 | ERROR    | app.utils.exceptions:wrapper:85 | get_embeddings RAG系统异常: 嵌入生成失败: 'Settings' object has no attribute 'embedding_model'
2025-07-17 16:08:15 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: 嵌入生成失败: 'Settings' object has no attribute 'embedding_model'
2025-07-17 16:09:34 | INFO     | __main__:lifespan:34 | RAG系统关闭中...
2025-07-17 16:09:44 | INFO     | app.service.model_client:_init_clients:48 | 模型客户端初始化完成:
2025-07-17 16:09:44 | INFO     | app.service.model_client:_init_clients:49 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-17 16:09:44 | INFO     | app.service.model_client:_init_clients:50 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-17 16:09:44 | INFO     | app.service.model_client:_init_clients:51 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-17 16:09:44 | INFO     | __main__:lifespan:21 | RAG系统启动中...
2025-07-17 16:09:44 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 16:09:44 | INFO     | __main__:lifespan:25 | PostgreSQL数据库连接成功
2025-07-17 16:09:44 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 16:09:47 | INFO     | app.service.model_client:check_health:69 | LLM 模型服务正常
2025-07-17 16:09:47 | INFO     | app.service.model_client:check_health:78 | Embedding 模型服务正常
2025-07-17 16:09:47 | WARNING  | app.service.model_client:check_health:84 | Rerank 模型服务不可用
2025-07-17 16:09:58 | INFO     | app.service.parse_document:parse_document_service:87 | 开始解析文件: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-17 16:11:01 | INFO     | app.service.parse_document:parse_document_service:105 | 共有15 片
2025-07-17 16:11:01 | INFO     | app.service.model_client:get_embeddings:121 | 嵌入生成成功，文本数量: 1
2025-07-17 16:11:02 | INFO     | app.service.model_client:get_embeddings:121 | 嵌入生成成功，文本数量: 1
2025-07-17 16:11:02 | INFO     | app.service.model_client:get_embeddings:121 | 嵌入生成成功，文本数量: 1
2025-07-17 16:11:02 | INFO     | app.service.model_client:get_embeddings:121 | 嵌入生成成功，文本数量: 1
2025-07-17 16:11:02 | INFO     | app.service.model_client:get_embeddings:121 | 嵌入生成成功，文本数量: 1
2025-07-17 16:11:02 | INFO     | app.service.model_client:get_embeddings:121 | 嵌入生成成功，文本数量: 1
2025-07-17 16:11:03 | INFO     | app.service.model_client:get_embeddings:121 | 嵌入生成成功，文本数量: 1
2025-07-17 16:11:03 | INFO     | app.service.model_client:get_embeddings:121 | 嵌入生成成功，文本数量: 1
2025-07-17 16:11:03 | INFO     | app.service.model_client:get_embeddings:121 | 嵌入生成成功，文本数量: 1
2025-07-17 16:11:03 | INFO     | app.service.model_client:get_embeddings:121 | 嵌入生成成功，文本数量: 1
2025-07-17 16:11:04 | INFO     | app.service.model_client:get_embeddings:121 | 嵌入生成成功，文本数量: 1
2025-07-17 16:11:04 | INFO     | app.service.model_client:get_embeddings:121 | 嵌入生成成功，文本数量: 1
2025-07-17 16:11:04 | INFO     | app.service.model_client:get_embeddings:121 | 嵌入生成成功，文本数量: 1
2025-07-17 16:11:04 | INFO     | app.service.model_client:get_embeddings:121 | 嵌入生成成功，文本数量: 1
2025-07-17 16:11:04 | INFO     | app.service.model_client:get_embeddings:121 | 嵌入生成成功，文本数量: 1
2025-07-17 16:26:59 | INFO     | __main__:lifespan:34 | RAG系统关闭中...
2025-07-17 16:27:23 | INFO     | app.service.model_client:_init_clients:48 | 模型客户端初始化完成:
2025-07-17 16:27:23 | INFO     | app.service.model_client:_init_clients:49 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-17 16:27:23 | INFO     | app.service.model_client:_init_clients:50 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-17 16:27:23 | INFO     | app.service.model_client:_init_clients:51 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-17 16:27:23 | INFO     | __main__:lifespan:21 | RAG系统启动中...
2025-07-17 16:27:23 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 16:27:23 | INFO     | __main__:lifespan:25 | PostgreSQL数据库连接成功
2025-07-17 16:27:23 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 16:27:24 | INFO     | app.service.model_client:check_health:69 | LLM 模型服务正常
2025-07-17 16:27:24 | INFO     | app.service.model_client:check_health:78 | Embedding 模型服务正常
2025-07-17 16:27:24 | WARNING  | app.service.model_client:check_health:84 | Rerank 模型服务不可用
2025-07-17 16:27:28 | INFO     | app.service.parse_document:parse_document_service:87 | 开始解析文件: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-17 16:28:35 | INFO     | app.service.parse_document:parse_document_service:105 | 共有15 片
2025-07-17 16:28:35 | INFO     | app.service.model_client:get_embeddings:123 | 嵌入维度: 2560
2025-07-17 16:28:35 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-17 16:28:35 | INFO     | app.service.model_client:get_embeddings:123 | 嵌入维度: 2560
2025-07-17 16:28:35 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-17 16:28:36 | INFO     | app.service.model_client:get_embeddings:123 | 嵌入维度: 2560
2025-07-17 16:28:36 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-17 16:28:36 | INFO     | app.service.model_client:get_embeddings:123 | 嵌入维度: 2560
2025-07-17 16:28:36 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-17 16:28:36 | INFO     | app.service.model_client:get_embeddings:123 | 嵌入维度: 2560
2025-07-17 16:28:36 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-17 16:28:36 | INFO     | app.service.model_client:get_embeddings:123 | 嵌入维度: 2560
2025-07-17 16:28:36 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-17 16:28:37 | INFO     | app.service.model_client:get_embeddings:123 | 嵌入维度: 2560
2025-07-17 16:28:37 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-17 16:28:37 | INFO     | app.service.model_client:get_embeddings:123 | 嵌入维度: 2560
2025-07-17 16:28:37 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-17 16:28:37 | INFO     | app.service.model_client:get_embeddings:123 | 嵌入维度: 2560
2025-07-17 16:28:37 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-17 16:28:37 | INFO     | app.service.model_client:get_embeddings:123 | 嵌入维度: 2560
2025-07-17 16:28:37 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-17 16:28:38 | INFO     | app.service.model_client:get_embeddings:123 | 嵌入维度: 2560
2025-07-17 16:28:38 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-17 16:28:38 | INFO     | app.service.model_client:get_embeddings:123 | 嵌入维度: 2560
2025-07-17 16:28:38 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-17 16:28:38 | INFO     | app.service.model_client:get_embeddings:123 | 嵌入维度: 2560
2025-07-17 16:28:38 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-17 16:28:38 | INFO     | app.service.model_client:get_embeddings:123 | 嵌入维度: 2560
2025-07-17 16:28:38 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-17 16:28:38 | INFO     | app.service.model_client:get_embeddings:123 | 嵌入维度: 2560
2025-07-17 16:28:38 | INFO     | app.service.model_client:get_embeddings:124 | 嵌入生成成功，文本数量: 1
2025-07-17 16:29:45 | INFO     | __main__:lifespan:34 | RAG系统关闭中...
2025-07-17 17:14:42 | INFO     | app.service.model_client:_init_clients:48 | 模型客户端初始化完成:
2025-07-17 17:14:42 | INFO     | app.service.model_client:_init_clients:49 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-17 17:14:42 | INFO     | app.service.model_client:_init_clients:50 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-17 17:14:42 | INFO     | app.service.model_client:_init_clients:51 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-17 17:14:42 | INFO     | __main__:lifespan:21 | RAG系统启动中...
2025-07-17 17:14:42 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 17:14:42 | INFO     | __main__:lifespan:25 | PostgreSQL数据库连接成功
2025-07-17 17:14:42 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 17:14:44 | INFO     | app.service.model_client:check_health:69 | LLM 模型服务正常
2025-07-17 17:14:45 | INFO     | app.service.model_client:check_health:78 | Embedding 模型服务正常
2025-07-17 17:14:45 | WARNING  | app.service.model_client:check_health:84 | Rerank 模型服务不可用
2025-07-17 17:14:53 | INFO     | app.service.parse_document:parse_document_service:89 | 开始解析文件: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-17 17:15:58 | INFO     | app.service.parse_document:parse_document_service:107 | 共有15 片
2025-07-17 17:15:58 | ERROR    | app.api.document_router:parse_document:42 | 文档解析处理失败: 'DocChunk' object has no attribute 'id'
2025-07-17 17:18:12 | INFO     | __main__:lifespan:34 | RAG系统关闭中...
2025-07-17 17:23:10 | INFO     | app.service.model_client:_init_clients:48 | 模型客户端初始化完成:
2025-07-17 17:23:10 | INFO     | app.service.model_client:_init_clients:49 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-17 17:23:10 | INFO     | app.service.model_client:_init_clients:50 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-17 17:23:10 | INFO     | app.service.model_client:_init_clients:51 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-17 17:23:10 | INFO     | __main__:lifespan:21 | RAG系统启动中...
2025-07-17 17:23:10 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 17:23:10 | INFO     | __main__:lifespan:25 | PostgreSQL数据库连接成功
2025-07-17 17:23:10 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 17:23:28 | INFO     | app.service.model_client:check_health:69 | LLM 模型服务正常
2025-07-17 17:23:28 | INFO     | app.service.model_client:check_health:78 | Embedding 模型服务正常
2025-07-17 17:23:28 | WARNING  | app.service.model_client:check_health:84 | Rerank 模型服务不可用
2025-07-17 17:23:31 | INFO     | app.service.parse_document:parse_document_service:90 | 开始解析文件: 05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-17 17:24:36 | INFO     | app.service.parse_document:parse_document_service:108 | 共有15 片
2025-07-17 17:24:37 | INFO     | app.service.model_client:get_embeddings:122 | 嵌入生成成功，文本数量: 1
2025-07-17 17:24:37 | INFO     | app.service.model_client:get_embeddings:122 | 嵌入生成成功，文本数量: 1
2025-07-17 17:24:37 | INFO     | app.service.model_client:get_embeddings:122 | 嵌入生成成功，文本数量: 1
2025-07-17 17:24:37 | INFO     | app.service.model_client:get_embeddings:122 | 嵌入生成成功，文本数量: 1
2025-07-17 17:24:37 | INFO     | app.service.model_client:get_embeddings:122 | 嵌入生成成功，文本数量: 1
2025-07-17 17:24:38 | INFO     | app.service.model_client:get_embeddings:122 | 嵌入生成成功，文本数量: 1
2025-07-17 17:24:38 | INFO     | app.service.model_client:get_embeddings:122 | 嵌入生成成功，文本数量: 1
2025-07-17 17:24:38 | INFO     | app.service.model_client:get_embeddings:122 | 嵌入生成成功，文本数量: 1
2025-07-17 17:24:38 | INFO     | app.service.model_client:get_embeddings:122 | 嵌入生成成功，文本数量: 1
2025-07-17 17:24:39 | INFO     | app.service.model_client:get_embeddings:122 | 嵌入生成成功，文本数量: 1
2025-07-17 17:24:39 | INFO     | app.service.model_client:get_embeddings:122 | 嵌入生成成功，文本数量: 1
2025-07-17 17:24:39 | INFO     | app.service.model_client:get_embeddings:122 | 嵌入生成成功，文本数量: 1
2025-07-17 17:24:39 | INFO     | app.service.model_client:get_embeddings:122 | 嵌入生成成功，文本数量: 1
2025-07-17 17:24:39 | INFO     | app.service.model_client:get_embeddings:122 | 嵌入生成成功，文本数量: 1
2025-07-17 17:24:39 | INFO     | app.service.model_client:get_embeddings:122 | 嵌入生成成功，文本数量: 1
2025-07-17 17:35:22 | INFO     | app.api.document_router:upload_document:26 | 开始处理上传文件: 0630 (1).docx
2025-07-17 17:35:26 | INFO     | __main__:lifespan:34 | RAG系统关闭中...
2025-07-17 17:36:35 | INFO     | app.database.qdrant_manager:_create_collection_if_not_exists:62 | 创建Qdrant集合: documents
2025-07-17 17:36:35 | INFO     | app.database.qdrant_manager:_initialize_client:41 | Qdrant客户端初始化成功
2025-07-17 17:36:36 | INFO     | app.service.model_client:_init_clients:48 | 模型客户端初始化完成:
2025-07-17 17:36:36 | INFO     | app.service.model_client:_init_clients:49 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-17 17:36:36 | INFO     | app.service.model_client:_init_clients:50 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-17 17:36:36 | INFO     | app.service.model_client:_init_clients:51 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-17 17:36:36 | INFO     | __main__:lifespan:21 | RAG系统启动中...
2025-07-17 17:36:36 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 17:36:36 | INFO     | __main__:lifespan:25 | PostgreSQL数据库连接成功
2025-07-17 17:36:36 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 17:36:38 | INFO     | app.service.model_client:check_health:69 | LLM 模型服务正常
2025-07-17 17:36:38 | INFO     | app.service.model_client:check_health:78 | Embedding 模型服务正常
2025-07-17 17:36:38 | WARNING  | app.service.model_client:check_health:84 | Rerank 模型服务不可用
2025-07-17 17:36:53 | INFO     | app.api.document_router:upload_document:26 | 开始处理上传文件: 0630 (1).docx
2025-07-17 17:39:28 | INFO     | __main__:lifespan:34 | RAG系统关闭中...
2025-07-17 17:39:38 | INFO     | app.database.qdrant_manager:_initialize_client:41 | Qdrant客户端初始化成功
2025-07-17 17:39:40 | INFO     | app.service.model_client:_init_clients:48 | 模型客户端初始化完成:
2025-07-17 17:39:40 | INFO     | app.service.model_client:_init_clients:49 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-17 17:39:40 | INFO     | app.service.model_client:_init_clients:50 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-17 17:39:40 | INFO     | app.service.model_client:_init_clients:51 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-17 17:39:40 | INFO     | __main__:lifespan:21 | RAG系统启动中...
2025-07-17 17:39:40 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 17:39:40 | INFO     | __main__:lifespan:25 | PostgreSQL数据库连接成功
2025-07-17 17:39:40 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 17:39:42 | INFO     | app.service.model_client:check_health:69 | LLM 模型服务正常
2025-07-17 17:39:42 | INFO     | app.service.model_client:check_health:78 | Embedding 模型服务正常
2025-07-17 17:39:42 | WARNING  | app.service.model_client:check_health:84 | Rerank 模型服务不可用
2025-07-17 17:39:47 | INFO     | app.api.document_router:upload_document:26 | 开始处理上传文件: 0630 (1).docx
2025-07-17 17:39:47 | INFO     | app.service.document_upload:extract_document_metadata:52 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmpg16sn8lo.docx
2025-07-17 17:39:47 | ERROR    | app.service.document_upload:_extract_word_metadata:108 | Word文档元数据提取失败: 'CoreProperties' object has no attribute 'company'
2025-07-17 17:39:47 | INFO     | app.service.document_upload:extract_document_metadata:85 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmpg16sn8lo.docx
2025-07-17 17:39:47 | INFO     | app.service.document_upload:upload_document_service:175 | 成功提取元数据: 0630 (1).docx
2025-07-17 17:39:47 | ERROR    | app.database.minio_manager:upload_file:58 | 文件上传失败: C:\Users\<USER>\AppData\Local\Temp\tmpg16sn8lo.docx -> <coroutine object PostgresManager.calculate_file_hash at 0x000001ECCB35D2F0>/112b72453628fe8c00a8af8e0996ca91.docx, 错误: expected string or bytes-like object, got 'coroutine'
2025-07-17 17:39:47 | ERROR    | app.api.document_router:upload_document:32 | 文档上传处理失败: expected string or bytes-like object, got 'coroutine'
2025-07-17 17:40:34 | INFO     | __main__:lifespan:34 | RAG系统关闭中...
2025-07-17 17:42:58 | INFO     | app.database.qdrant_manager:_initialize_client:41 | Qdrant客户端初始化成功
2025-07-17 17:43:00 | INFO     | app.service.model_client:_init_clients:48 | 模型客户端初始化完成:
2025-07-17 17:43:00 | INFO     | app.service.model_client:_init_clients:49 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-17 17:43:00 | INFO     | app.service.model_client:_init_clients:50 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-17 17:43:00 | INFO     | app.service.model_client:_init_clients:51 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-17 17:43:00 | INFO     | __main__:lifespan:21 | RAG系统启动中...
2025-07-17 17:43:00 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 17:43:00 | INFO     | __main__:lifespan:25 | PostgreSQL数据库连接成功
2025-07-17 17:43:00 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 17:43:01 | INFO     | app.service.model_client:check_health:69 | LLM 模型服务正常
2025-07-17 17:43:01 | INFO     | app.service.model_client:check_health:78 | Embedding 模型服务正常
2025-07-17 17:43:01 | WARNING  | app.service.model_client:check_health:84 | Rerank 模型服务不可用
2025-07-17 17:43:03 | INFO     | app.api.document_router:upload_document:26 | 开始处理上传文件: 0630 (1).docx
2025-07-17 17:43:03 | INFO     | app.service.document_upload:extract_document_metadata:53 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmpjjg9kjmw.docx
2025-07-17 17:43:03 | ERROR    | app.service.document_upload:_extract_word_metadata:109 | Word文档元数据提取失败: 'CoreProperties' object has no attribute 'company'
2025-07-17 17:43:03 | INFO     | app.service.document_upload:extract_document_metadata:86 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmpjjg9kjmw.docx
2025-07-17 17:43:03 | INFO     | app.service.document_upload:upload_document_service:176 | 成功提取元数据: 0630 (1).docx
2025-07-17 17:43:03 | ERROR    | app.database.minio_manager:upload_file:58 | 文件上传失败: C:\Users\<USER>\AppData\Local\Temp\tmpjjg9kjmw.docx -> <coroutine object PostgresManager.calculate_file_hash at 0x000001A8CD39D2F0>/112b72453628fe8c00a8af8e0996ca91.docx, 错误: expected string or bytes-like object, got 'coroutine'
2025-07-17 17:43:03 | ERROR    | app.api.document_router:upload_document:32 | 文档上传处理失败: expected string or bytes-like object, got 'coroutine'
2025-07-17 18:14:12 | INFO     | __main__:lifespan:34 | RAG系统关闭中...
2025-07-17 18:14:22 | INFO     | app.database.qdrant_manager:_initialize_client:41 | Qdrant客户端初始化成功
2025-07-17 18:14:24 | INFO     | app.service.model_client:_init_clients:48 | 模型客户端初始化完成:
2025-07-17 18:14:24 | INFO     | app.service.model_client:_init_clients:49 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-17 18:14:24 | INFO     | app.service.model_client:_init_clients:50 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-17 18:14:24 | INFO     | app.service.model_client:_init_clients:51 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-17 18:14:24 | INFO     | __main__:lifespan:21 | RAG系统启动中...
2025-07-17 18:14:24 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 18:14:24 | INFO     | __main__:lifespan:25 | PostgreSQL数据库连接成功
2025-07-17 18:14:24 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 18:14:25 | INFO     | app.service.model_client:check_health:69 | LLM 模型服务正常
2025-07-17 18:14:25 | INFO     | app.service.model_client:check_health:78 | Embedding 模型服务正常
2025-07-17 18:14:25 | WARNING  | app.service.model_client:check_health:84 | Rerank 模型服务不可用
2025-07-17 18:14:35 | INFO     | __main__:lifespan:34 | RAG系统关闭中...
2025-07-17 18:14:44 | INFO     | app.database.qdrant_manager:_initialize_client:41 | Qdrant客户端初始化成功
2025-07-17 18:14:46 | INFO     | app.service.model_client:_init_clients:48 | 模型客户端初始化完成:
2025-07-17 18:14:46 | INFO     | app.service.model_client:_init_clients:49 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-17 18:14:46 | INFO     | app.service.model_client:_init_clients:50 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-17 18:14:46 | INFO     | app.service.model_client:_init_clients:51 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-17 18:14:46 | INFO     | __main__:lifespan:21 | RAG系统启动中...
2025-07-17 18:14:46 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-17 18:14:46 | INFO     | __main__:lifespan:25 | PostgreSQL数据库连接成功
2025-07-17 18:14:46 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-17 18:14:49 | INFO     | app.service.model_client:check_health:69 | LLM 模型服务正常
2025-07-17 18:14:49 | INFO     | app.service.model_client:check_health:78 | Embedding 模型服务正常
2025-07-17 18:14:49 | WARNING  | app.service.model_client:check_health:84 | Rerank 模型服务不可用
2025-07-17 18:14:59 | INFO     | app.api.document_router:upload_document:26 | 开始处理上传文件: 0630 (1).docx
2025-07-17 18:14:59 | INFO     | app.service.document_upload:extract_document_metadata:54 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmpewohtn2c.docx
2025-07-17 18:14:59 | ERROR    | app.service.document_upload:_extract_word_metadata:110 | Word文档元数据提取失败: 'CoreProperties' object has no attribute 'company'
2025-07-17 18:14:59 | INFO     | app.service.document_upload:extract_document_metadata:87 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmpewohtn2c.docx
2025-07-17 18:14:59 | INFO     | app.service.document_upload:upload_document_service:177 | 成功提取元数据: 0630 (1).docx
2025-07-17 18:14:59 | INFO     | app.database.minio_manager:upload_file:49 | 存储桶 21f64da1e5792c8295b964d159a14491 不存在，已自动创建
2025-07-17 18:14:59 | INFO     | app.database.minio_manager:upload_file:53 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmpewohtn2c.docx -> 21f64da1e5792c8295b964d159a14491/112b72453628fe8c00a8af8e0996ca91.docx
2025-07-17 18:16:45 | INFO     | app.api.document_router:upload_document:26 | 开始处理上传文件: 1.pdf
2025-07-17 18:16:45 | INFO     | app.service.document_upload:extract_document_metadata:54 | 从文档中提取元数据: C:\Users\<USER>\AppData\Local\Temp\tmp4do19vta.pdf
2025-07-17 18:16:45 | WARNING  | app.service.document_upload:extract_document_metadata:81 | 不支持的文件类型: .pdf
2025-07-17 18:16:45 | INFO     | app.service.document_upload:extract_document_metadata:87 | Successfully extracted metadata for: C:\Users\<USER>\AppData\Local\Temp\tmp4do19vta.pdf
2025-07-17 18:16:45 | INFO     | app.service.document_upload:upload_document_service:177 | 成功提取元数据: 1.pdf
2025-07-17 18:16:45 | INFO     | app.database.minio_manager:upload_file:53 | 文件上传成功: C:\Users\<USER>\AppData\Local\Temp\tmp4do19vta.pdf -> 21f64da1e5792c8295b964d159a14491/05e29b901db4cb06e0808fd8ad16d3a4.pdf
2025-07-17 18:19:28 | INFO     | __main__:lifespan:34 | RAG系统关闭中...
