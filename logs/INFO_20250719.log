2025-07-19 09:10:31 | ERROR    | app.database.qdrant_manager:_create_collection_if_not_exists:66 | 创建Qdrant集合失败: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-19 09:10:31 | ERROR    | app.database.qdrant_manager:_initialize_client:45 | Qdrant客户端初始化失败: 集合创建失败: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-19 09:14:03 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-19 09:14:06 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-19 09:14:06 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-19 09:14:06 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-19 09:14:06 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-19 09:14:15 | INFO     | __main__:lifespan:22 | RAG系统启动中...
2025-07-19 09:14:15 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-19 09:14:15 | INFO     | __main__:lifespan:26 | PostgreSQL数据库连接成功
2025-07-19 09:14:15 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-19 09:14:39 | ERROR    | app.service.model_client:check_health:97 | 模型服务健康检查失败: All connection attempts failed
2025-07-19 09:14:39 | ERROR    | app.utils.exceptions:wrapper:85 | check_health RAG系统异常: 模型服务健康检查失败: All connection attempts failed
2025-07-19 09:14:39 | ERROR    | __main__:lifespan:32 | 系统初始化失败: 模型服务健康检查失败: All connection attempts failed
2025-07-19 18:13:08 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-19 18:13:12 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-19 18:13:12 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://*************:15555/chat/v1 (qwen3-8b-fp8)
2025-07-19 18:13:12 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://*************:15555/embedding/v1 (Qwen3-Embedding-4B)
2025-07-19 18:13:12 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://*************:15555/rerank/v1 (qwen3-8b-fp8)
2025-07-19 18:13:23 | INFO     | __main__:lifespan:22 | RAG系统启动中...
2025-07-19 18:13:23 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-19 18:13:23 | INFO     | __main__:lifespan:26 | PostgreSQL数据库连接成功
2025-07-19 18:13:23 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-19 18:13:47 | ERROR    | app.service.model_client:check_health:97 | 模型服务健康检查失败: All connection attempts failed
2025-07-19 18:13:47 | ERROR    | app.utils.exceptions:wrapper:85 | check_health RAG系统异常: 模型服务健康检查失败: All connection attempts failed
2025-07-19 18:13:47 | ERROR    | __main__:lifespan:32 | 系统初始化失败: 模型服务健康检查失败: All connection attempts failed
2025-07-19 18:14:49 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-19 18:14:51 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-19 18:14:51 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: https://js1.blockelite.cn:28582/chat/v1 (qwen3-8b-fp8)
2025-07-19 18:14:51 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: https://js1.blockelite.cn:28582/embedding/v1 (Qwen3-Embedding-4B)
2025-07-19 18:14:51 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: https://js1.blockelite.cn:28582/rerank/v1 (qwen3-8b-fp8)
2025-07-19 18:14:56 | INFO     | __main__:lifespan:22 | RAG系统启动中...
2025-07-19 18:14:56 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-19 18:14:56 | INFO     | __main__:lifespan:26 | PostgreSQL数据库连接成功
2025-07-19 18:14:56 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-19 18:14:58 | ERROR    | app.service.model_client:check_health:97 | 模型服务健康检查失败: [SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate (_ssl.c:1010)
2025-07-19 18:14:58 | ERROR    | app.utils.exceptions:wrapper:85 | check_health RAG系统异常: 模型服务健康检查失败: [SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate (_ssl.c:1010)
2025-07-19 18:14:58 | ERROR    | __main__:lifespan:32 | 系统初始化失败: 模型服务健康检查失败: [SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate (_ssl.c:1010)
2025-07-19 18:15:24 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-19 18:15:27 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-19 18:15:27 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://js1.blockelite.cn:28583/chat/v1 (qwen3-8b-fp8)
2025-07-19 18:15:27 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://js1.blockelite.cn:28583/embedding/v1 (Qwen3-Embedding-4B)
2025-07-19 18:15:27 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://js1.blockelite.cn:28583/rerank/v1 (qwen3-8b-fp8)
2025-07-19 18:15:32 | INFO     | __main__:lifespan:22 | RAG系统启动中...
2025-07-19 18:15:32 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-19 18:15:32 | INFO     | __main__:lifespan:26 | PostgreSQL数据库连接成功
2025-07-19 18:15:32 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-19 18:15:33 | WARNING  | app.service.model_client:check_health:74 | LLM 模型服务不可用
2025-07-19 18:15:33 | WARNING  | app.service.model_client:check_health:83 | Embedding 模型服务不可用
2025-07-19 18:15:33 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
2025-07-19 18:17:40 | INFO     | __main__:lifespan:35 | RAG系统关闭中...
2025-07-19 18:17:43 | INFO     | app.database.qdrant_manager:_initialize_client:42 | Qdrant客户端初始化成功
2025-07-19 18:17:46 | INFO     | app.service.model_client:_init_clients:55 | 模型客户端初始化完成:
2025-07-19 18:17:46 | INFO     | app.service.model_client:_init_clients:56 |   - LLM: http://js1.blockelite.cn:28583/chat/v1 (qwen3-8b-fp8)
2025-07-19 18:17:46 | INFO     | app.service.model_client:_init_clients:57 |   - Embedding: http://js1.blockelite.cn:28583/embedding/v1 (Qwen3-Embedding-4B)
2025-07-19 18:17:46 | INFO     | app.service.model_client:_init_clients:58 |   - Reranker: http://js1.blockelite.cn:28583/rerank/v1 (qwen3-8b-fp8)
2025-07-19 18:17:51 | INFO     | __main__:lifespan:22 | RAG系统启动中...
2025-07-19 18:17:51 | INFO     | app.database.postgres_manager:initialize:40 | Tortoise ORM初始化成功
2025-07-19 18:17:51 | INFO     | __main__:lifespan:26 | PostgreSQL数据库连接成功
2025-07-19 18:17:51 | INFO     | app.database.minio_manager:initialize:33 | MinIO客户端初始化成功: localhost:19000
2025-07-19 18:17:55 | WARNING  | app.service.model_client:check_health:74 | LLM 模型服务不可用
2025-07-19 18:17:55 | WARNING  | app.service.model_client:check_health:83 | Embedding 模型服务不可用
2025-07-19 18:17:55 | WARNING  | app.service.model_client:check_health:91 | Rerank 模型服务不可用
