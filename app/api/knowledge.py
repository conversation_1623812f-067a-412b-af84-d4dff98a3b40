from fastapi import APIRouter, HTTPException, Response
# from fastapi.responses import StreamingResponse
import hashlib
from typing import List, Dict, Any

from sse_starlette.sse import EventSourceResponse

from app.models.database import KnowledgeBase, FileInfo
from app.models.knowledge import ReportRequest, CreateKnowledgeResponse, CreateKnowledgeRequest, \
    DeleteKnowledgeResponse, KnowledgeListResponse, ChatRequest
from app.service.intelligent_chat_service import intelligent_chat_service
from app.service.report_generator import ReportGenerator
from app.database.minio_manager import minio_manager
from app.database.qdrant_manager import qdrant_manager
from app.utils.logger import setup_logger

logger = setup_logger(__name__)

knowledge_router = APIRouter(prefix="/v1/knowledge", tags=["知识库管理"])





@knowledge_router.post("/create", response_model=CreateKnowledgeResponse)
async def create_knowledge(request: CreateKnowledgeRequest):
    """创建知识库"""
    try:

        # 检查知识库名称是否已存在
        existing = await KnowledgeBase.filter(name=request.name).first()
        if existing:
            raise HTTPException(status_code=400, detail="知识库名称已存在")
        # 创建MD5
        md5_hash = hashlib.md5()
        md5_hash.update(request.name.encode("utf-8"))
        name_md5 = md5_hash.hexdigest()

        # 创建新知识库
        knowledge = await KnowledgeBase.create(
            name=request.name,
            name_md5=name_md5,
            description=request.description,
            public=request.public
        )

        logger.info(f"成功创建知识库: {request.name}")

        return CreateKnowledgeResponse(
            knowledge=knowledge.to_dict()
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建知识库失败: {e}")
        return CreateKnowledgeResponse(
            code=-1,
            message=f"创建知识库失败: {str(e)}",
             knowledge={}
        )


@knowledge_router.delete("/delete")
async def delete_knowledge(knowledge_hash: str):
    """删除知识库"""
    try:
        # 查找知识库
        knowledge = await KnowledgeBase.filter(name_md5=knowledge_hash).first()
        if not knowledge:
            raise HTTPException(status_code=404, detail="知识库不存在")

        # 删除知识库
        await knowledge.delete()

        #删除Fileinfo中国相关的文档数据
        await FileInfo.filter(knowledge_name=knowledge_hash).delete()
        #删除minio存储桶
        await minio_manager.delete_bucket(knowledge_hash, True)
        #删除qdrant数据库
        qdrant_manager.delete_collection(knowledge_hash)
        logger.info(f"成功删除知识库: {knowledge.name} (ID: {knowledge.id})")

        return DeleteKnowledgeResponse(
            code=0,
            message="success",
            data={
                "deleted_id": knowledge.id,
                "deleted_name": knowledge.name
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除知识库失败: {e}")
        return DeleteKnowledgeResponse(
            code=-1,
            message=f"删除知识库失败: {str(e)}",
            data={}
        )


@knowledge_router.get("/list", response_model=KnowledgeListResponse)
async def list_knowledge():
    """列出知识库"""
    try:

        # 查询知识库列表
        knowledge_list = await KnowledgeBase.all().order_by('-created_at')
        # total = await KnowledgeBase.all().count()

        # 转换为字典格式
        data = []
        for knowledge in knowledge_list:
            # 查询与当前知识库关联的文档数量
            doc_count = await FileInfo.filter(knowledge_name=knowledge.name_md5).count()

            # 将知识库信息转为字典，并添加文档数量字段
            knowledge_dict = knowledge.to_dict()
            knowledge_dict['doc_count'] = doc_count
            data.append(knowledge_dict)

        # logger.info(f"查询知识库列表成功，共{total}个知识库，当前页{page}")

        return KnowledgeListResponse(
            data=data,
            # total=total
        )

    except Exception as e:
        logger.error(f"查询知识库列表失败: {e}")
        return KnowledgeListResponse(
            code=-1,
            message=f"查询知识库列表失败: {str(e)}",
            data=[],
        )


@knowledge_router.post("/generate_report")
async def generate_intelligent_report(request: ReportRequest, response: Response):
    """智能报告生成 - 完整展示研究过程

    实现类似Gemini深度研究的功能：
    1. 思考规划阶段 - 分析主题，制定研究计划
    2. 多轮搜索阶段 - 执行不同类型的搜索任务
    3. 内容分析阶段 - 评估收集内容的完整性
    4. 补充搜索阶段 - 填补知识空白
    5. 报告生成阶段 - 生成结构化专业报告

    通过SSE实时展示：
    - AI的思考过程和决策逻辑
    - 每次搜索的策略和结果
    - 找到的相关文档和内容片段
    - 内容质量评估和改进建议
    - 最终的专业报告
    """

    if not request.title.strip():
        raise HTTPException(status_code=400, detail="报告标题不能为空")

    try:
        response.headers["Content-Type"] = "text/event-stream"
        response.headers["Cache-Control"] = "no-cache"
        generator = ReportGenerator()

        return EventSourceResponse(
            generator.generate_report_stream(request.title, request.collection_name)
        )

    except Exception as e:
        logger.error(f"启动报告生成失败: {e}")
        raise HTTPException(status_code=500, detail=f"报告生成启动失败: {str(e)}")


# 对话
@knowledge_router.post("/chat")
async def chat_with_knowledge(request: ChatRequest):
    """
        与知识库进行智能对话

        功能特点：
        1. 智能判断是否需要检索知识库
        2. 混合检索策略确保准确性
        3. 流式响应展示思考过程
        4. 提供引用来源和置信度评分
        5. 支持多轮对话上下文
        """

    if not request.question.strip():
        raise HTTPException(status_code=400, detail="问题不能为空")
    try:
        return EventSourceResponse(
            intelligent_chat_service.chat_stream(
                question=request.question,
                collection_name=request.collection_name,
                conversation_history=request.conversation_history[-request.max_history:]
            )
        )
    except Exception as e:
        logger.error(f"对话处理失败: {e}")
        raise HTTPException(status_code=500, detail=f"对话处理失败: {str(e)}")