# 关于文档相关路由，包括文档的上传， 删除，列表
import asyncio
from typing import Annotated, Callable

from fastapi import APIRouter, UploadFile, File, Form
from qdrant_client.http.models import models

from app.database.minio_manager import minio_manager
from app.database.qdrant_manager import qdrant_manager
from app.models.database import FileInfo
from app.models.document_upload import DocumentUploadResponse, ParseDocumentRequest, ParseDocumentResponse, \
    DocumentListResponse  # 导入新的响应模型
from app.service.document.document_list import list_documents
from app.service.document.document_upload import upload_document_service
from app.service.document.parse_document import parse_document_service
from app.utils.logger import setup_logger

document_router = APIRouter(prefix="/v1/document", tags=["文档管理"])
logger = setup_logger(__name__)

@document_router.post("/upload", response_model=DocumentUploadResponse)
async def upload_document(
        file: UploadFile = File(...),
        knowledge_hash: Annotated[str, Form(description="知识库hash")] = None
) -> DocumentUploadResponse:
    """上传文档到minio"""
    # 提取文档元数据
    """上传文档到minio"""
    logger.info(f"开始处理上传文件: {file.filename}")

    try:
       return await upload_document_service(knowledge_hash, file)

    except Exception as e:
        logger.error(f"文档上传处理失败: {str(e)}")
        return DocumentUploadResponse(code=-1, message=str(e), file_info={})

# 解析文档
@document_router.post("/parse", response_model=ParseDocumentResponse)
async def parse_document(request: ParseDocumentRequest) -> ParseDocumentResponse:
    # 遍历文档hash
    try:
        return await parse_document_service(request)
    except Exception as e:
        logger.error(f"文档解析处理失败: {str(e)}")
        return ParseDocumentResponse(code=-1, message=str(e))


# 获取文档列表
@document_router.get("/list", response_model=DocumentListResponse)
async def list_document(collection_hash: str, page: int = 1, page_size: int = 10) -> DocumentListResponse:
    try:
        return await list_documents(collection_hash, page, page_size)
    except Exception as e:
        logger.error(f"获取文档列表失败: {str(e)}")
        return DocumentListResponse(code=-1, message=str(e), documents=[], total=0)


@document_router.delete("/delete")
async def delete_document(file_hash: str, knowledge_hash: str):
    """删除文档"""
    try:

        # 安全地获取文件信息
        try:
            file_info = await FileInfo.get(file_hash=file_hash, knowledge_name=knowledge_hash)
        except Exception as get_error:
            # 如果 get() 失败，使用 filter().first()
            file_info = await FileInfo.filter(file_hash=file_hash, knowledge_name=knowledge_hash).first()

        if not file_info:
            return {
                "code": -1,
                "message": "文件不存在"
            }
        await minio_manager.delete_object(file_info.bucket_name, file_info.minio_name)
        # 删除qdrant中的向量
        # 删除qdrant中的向量
        filter_obj = models.Filter(
            must=[
                models.FieldCondition(
                    key="file_hash",
                    match=models.MatchValue(value=file_hash)
                )
            ]
        )
        qdrant_manager.delete_vectors_by_filter(knowledge_hash, filter_obj)
        # 删除数据库中的记录
        await file_info.delete()
        return {
            "code": 0,
            "message": "success"
        }
    except Exception as e:
        logger.error(f"删除文档失败: {str(e)}")
        return {
            "code": -1,
            "message": str(e)
        }