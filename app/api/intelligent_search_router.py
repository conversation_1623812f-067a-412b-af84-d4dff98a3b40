from fastapi import APIRouter

from app.models.database import FileInfo
from app.service.search.intelligent_search_planner import intelligent_planner
from app.service.multi_strategy_search_engine import multi_strategy_engine

search_router = APIRouter(prefix="/v1/intelligent_search", tags=["智能搜索"])

@search_router.post("/analyze_intent")
async def analyze_search_intent(query: str):
    """分析搜索意图"""
    intent = await intelligent_planner.analyze_search_intent(query)
    return intent

@search_router.get("/search")
async def intelligent_search(
    collection_name: str,
    query: str,
    top_k: int = 20,
    include_plan: bool = False
):
    """智能搜索"""
    results = await multi_strategy_engine.execute_intelligent_search(collection_name,query, top_k)
    
    if not include_plan:
        # 只返回搜索结果，不包含计划详情
        return {
            "query": query,
            "results": results["final_results"],
            "total_found": results["total_found"]
        }
    
    return results

# 关键词查询，直接字符串匹配text字段
@search_router.get("/text_search")
async def text_search(
        collection_name: str,
        query: str,
        top_k: int = 20
):
    """关键词文本搜索，直接字符串匹配text字段"""
    from app.database.qdrant_manager import qdrant_manager

    # 使用Qdrant的文本过滤功能进行关键词搜索
    search_results = qdrant_manager.client.scroll(
        collection_name=collection_name,
        scroll_filter={
            "must": [
                {
                    "key": "text",
                    "match": {
                        "text": query
                    }
                }
            ]
        },
        limit=top_k,
        with_payload=True,
        with_vectors=False
    )

    # 按file_hash分组
    file_groups = {}
    for point in search_results[0]:
        file_hash = point.payload.get("file_hash")
        if file_hash not in file_groups:
            file_groups[file_hash] = []

        file_groups[file_hash].append({
            "doc_id": point.id,
            "score": 1.0,  # 文本匹配没有相似度分数，设为1.0
            "payload": {
                "id": point.payload.get("id"),
                "file_hash": point.payload.get("file_hash"),
                "text": point.payload.get("text")
            }
        })

    # 对每个文档内的结果按id排序，然后构建最终结果
    final_results = []
    for file_hash, chunks in file_groups.items():
        try:
            # 文档内按id排序
            chunks.sort(key=lambda x: x["payload"]["id"])

            # 安全地获取文件信息 - 使用复合查询条件
            try:
                file_info = await FileInfo.get(file_hash=file_hash, knowledge_name=collection_name)
            except Exception as get_error:
                # 如果 get() 失败，使用 filter().first()
                file_info = await FileInfo.filter(file_hash=file_hash, knowledge_name=collection_name).first()
                if not file_info:
                    # 如果仍然找不到，跳过这个文档
                    continue

            final_results.append({
                "file_hash": file_hash,
                "file_name": file_info.original_filename,
                "summary": file_info.summary,
                "metadata": file_info.metadata,
                "max_score": 1.0,  # 文本搜索没有相似度分数
                "chunks": chunks
            })

        except Exception as e:
            # 记录错误但继续处理其他文档
            print(f"处理文档 {file_hash} 时出错: {e}")
            continue

    return {
        "query": query,
        "results": final_results,
        "total_found": len(final_results)
    }