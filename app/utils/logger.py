"""
日志配置模块
支持控制台和文件日志，适配Windows环境
"""

import logging
import sys
from pathlib import Path
from datetime import datetime
from typing import Optional
from loguru import logger as loguru_logger

from app.config.settings import settings


class Logger:
    """统一日志管理器"""
    
    def __init__(self, name: str, level: str = None):
        self.name = name
        self.level = level or settings.app_config.log_level
        self._setup_logger()
    
    def _setup_logger(self):
        """配置日志器"""
        # 移除默认处理器
        loguru_logger.remove()
        
        # 控制台处理器
        loguru_logger.add(
            sys.stdout,
            level=self.level,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
                   "<level>{level: <8}</level> | "
                   "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
                   "<level>{message}</level>",
            colorize=True
        )
        
        # 文件处理器 - 所有日志
        # log_file = Path("logs") / f"{self.name}_{datetime.now().strftime('%Y%m%d')}.log"
        # loguru_logger.add(
        #     str(log_file),
        #     level="DEBUG",
        #     format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
        #     rotation="1 day",
        #     retention="30 days",
        #     compression="zip",
        #     encoding="utf-8"
        # )
        
        # 错误日志文件
        error_log_file = Path("logs") / f"{self.level}_{datetime.now().strftime('%Y%m%d')}.log"
        loguru_logger.add(
            str(error_log_file),
            level=self.level,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
            rotation="1 day",
            retention="30 days",
            compression="zip",
            encoding="utf-8"
        )
    
    def get_logger(self):
        """获取配置好的日志器"""
        return loguru_logger.bind(name=self.name)


def setup_logger(name: str, level: Optional[str] = None) -> loguru_logger:
    """
    设置并返回日志器
    
    Args:
        name: 日志器名称
        level: 日志级别
    
    Returns:
        配置好的日志器
    """
    logger_instance = Logger(name, level)
    return logger_instance.get_logger()


# 创建默认日志器
default_logger = setup_logger("rag_system")


def log_function_call(func):
    """函数调用日志装饰器"""
    def wrapper(*args, **kwargs):
        logger = setup_logger(func.__module__)
        logger.info(f"调用函数: {func.__name__}")
        try:
            result = func(*args, **kwargs)
            logger.info(f"函数 {func.__name__} 执行成功")
            return result
        except Exception as e:
            logger.error(f"函数 {func.__name__} 执行失败: {e}")
            raise
    return wrapper


async def log_async_function_call(func):
    """异步函数调用日志装饰器"""
    async def wrapper(*args, **kwargs):
        logger = setup_logger(func.__module__)
        logger.info(f"调用异步函数: {func.__name__}")
        try:
            result = await func(*args, **kwargs)
            logger.info(f"异步函数 {func.__name__} 执行成功")
            return result
        except Exception as e:
            logger.error(f"异步函数 {func.__name__} 执行失败: {e}")
            raise
    return wrapper
