"""
自定义异常类和错误处理
"""

from typing import Optional, Dict, Any
from fastapi import HTTPException
import functools
from app.utils.logger import setup_logger

logger = setup_logger(__name__)


class RAGSystemException(Exception):
    """RAG系统基础异常类"""
    
    def __init__(self, message: str, error_code: Optional[str] = None, details: Optional[Dict[str, Any]] = None):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class DocumentProcessingError(RAGSystemException):
    """文档处理异常"""
    pass


class EntityExtractionError(RAGSystemException):
    """实体提取异常"""
    pass


class VectorStoreError(RAGSystemException):
    """向量存储异常"""
    pass


class GraphStoreError(RAGSystemException):
    """图存储异常"""
    pass


class ModelClientError(RAGSystemException):
    """模型客户端异常"""
    pass


class FileUploadError(RAGSystemException):
    """文件上传异常"""
    pass


class ValidationError(RAGSystemException):
    """数据验证异常"""
    pass


class ConfigurationError(RAGSystemException):
    """配置错误异常"""
    pass


def handle_exceptions(func):
    """同步函数异常处理装饰器"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except RAGSystemException as e:
            logger.error(f"{func.__name__} RAG系统异常: {e.message}")
            raise e
        except Exception as e:
            logger.error(f"{func.__name__} 未知异常: {str(e)}")
            raise RAGSystemException(f"操作失败: {str(e)}")
    return wrapper


def handle_async_exceptions(func):
    """异步函数异常处理装饰器"""
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except RAGSystemException as e:
            logger.error(f"{func.__name__} RAG系统异常: {e.message}")
            raise e
        except Exception as e:
            logger.error(f"{func.__name__} 未知异常: {str(e)}")
            raise RAGSystemException(f"操作失败: {str(e)}")
    return wrapper


def create_http_exception(
    status_code: int,
    message: str,
    error_code: Optional[str] = None,
    details: Optional[Dict[str, Any]] = None
) -> HTTPException:
    """创建HTTP异常"""
    detail = {
        "message": message,
        "error_code": error_code,
        "details": details or {}
    }
    return HTTPException(status_code=status_code, detail=detail)


# 常用HTTP异常
def bad_request(message: str, error_code: Optional[str] = None) -> HTTPException:
    """400 Bad Request"""
    return create_http_exception(400, message, error_code)


def unauthorized(message: str = "未授权访问") -> HTTPException:
    """401 Unauthorized"""
    return create_http_exception(401, message, "UNAUTHORIZED")


def forbidden(message: str = "禁止访问") -> HTTPException:
    """403 Forbidden"""
    return create_http_exception(403, message, "FORBIDDEN")


def not_found(message: str = "资源未找到") -> HTTPException:
    """404 Not Found"""
    return create_http_exception(404, message, "NOT_FOUND")


def internal_server_error(message: str = "内部服务器错误") -> HTTPException:
    """500 Internal Server Error"""
    return create_http_exception(500, message, "INTERNAL_ERROR")


def service_unavailable(message: str = "服务不可用") -> HTTPException:
    """503 Service Unavailable"""
    return create_http_exception(503, message, "SERVICE_UNAVAILABLE")
