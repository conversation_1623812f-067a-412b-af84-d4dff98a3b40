from pydantic_settings import BaseSettings
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from pathlib import Path

try:
    import yaml
    YAML_AVAILABLE = True
except ImportError:
    YAML_AVAILABLE = False
    print("警告: PyYAML未安装，无法解析YAML配置文件。请运行: pip install PyYAML")


class VLLMServiceConfig(BaseModel):
    """VLLM服务配置"""
    base_url: str = Field(..., description="服务基础URL")
    api_key: str = Field(..., description="API密钥")
    model: str = Field(..., description="模型名称")


class VLLMConfig(BaseModel):
    """VLLM配置"""
    chat: VLLMServiceConfig = Field(..., description="聊天服务配置")
    embedding: VLLMServiceConfig = Field(..., description="嵌入服务配置")
    rerank: VLLMServiceConfig = Field(..., description="重排序服务配置")


class QdrantConfig(BaseModel):
    """Qdrant数据库配置"""
    host: str = Field(default="localhost", description="Qdrant主机")
    port: int = Field(default=6333, description="Qdrant端口")
    vector_size: int = Field(default=4096, description="向量维度")
    collection_name: str = Field(default="documents", description="集合名称")


class PostgresConfig(BaseModel):
    """PostgreSQL数据库配置"""
    host: str = Field(default="localhost", description="PostgreSQL主机")
    port: int = Field(default=5432, description="PostgreSQL端口")
    dbname: str = Field(default="postgres", description="数据库名称")
    user: str = Field(default="postgres", description="用户名")
    password: str = Field(default="postgres", description="密码")


class Neo4jConfig(BaseModel):
    """Neo4j图数据库配置"""
    uri: str = Field(default="bolt://localhost:7687", description="Neo4j连接URI")
    user: str = Field(default="neo4j", description="用户名")
    password: str = Field(default="neo4j", description="密码")
    database: str = Field(default="neo4j", description="数据库名称")
    max_connection_lifetime: int = Field(default=3600, description="连接最大生命周期(秒)")
    max_connection_pool_size: int = Field(default=50, description="连接池最大大小")


class AppConfig(BaseModel):
    """应用配置"""
    name: str = Field(default="RAG Document System", description="应用名称")
    version: str = Field(default="1.0.0", description="应用版本")
    debug: bool = Field(default=False, description="调试模式")
    log_level: str = Field(default="INFO", description="日志级别")

class MinioConfig(BaseModel):
    """Minio配置"""
    endpoint: str = Field(default="localhost:9000", description="Minio端点")
    access_key: str = Field(default="minioadmin", description="访问密钥")
    secret_key: str = Field(default="minioadmin", description="秘密密钥")
    secure: bool = Field(default=False, description="是否使用安全连接")
    default_bucket: str = Field(default="documents", description="默认存储桶名称")

class DoclingConfig(BaseModel):
    """Docling配置"""
    embedding_model: str = Field(default="sentence-transformers/all-MiniLM-L6-v2", description="分词模型")

class RedisConfig(BaseModel):
    """Redis配置"""
    host: str = Field(default="localhost", description="Redis主机")
    port: int = Field(default=6379, description="Redis端口")
    password: str = Field(default=None, description="Redis密码")
    db: int = Field(default=0, description="Redis数据库")

class Settings(BaseSettings):
    """系统配置类"""

    # 应用配置
    app_config: Optional[AppConfig] = Field(default=None, description="应用配置")

    # VLLM配置
    vllm_config: Optional[VLLMConfig] = Field(default=None, description="VLLM配置")

    # 数据库配置
    qdrant_config: Optional[QdrantConfig] = Field(default=None, description="Qdrant配置")
    postgres_config: Optional[PostgresConfig] = Field(default=None, description="PostgreSQL配置")
    neo4j_config: Optional[Neo4jConfig] = Field(default=None, description="Neo4j配置")
    minio_config: Optional[MinioConfig] = Field(default=None, description="Minio配置")
    docling_config: Optional[DoclingConfig] = Field(default=None, description="Docling配置")
    redis_config: Optional[RedisConfig] = Field(default=None, description="Redis配置")

    def __init__(self, **kwargs):
        # 从YAML文件加载配置
        yaml_config = self._load_yaml_config()

        # 合并YAML配置和传入的参数
        merged_config = {**yaml_config, **kwargs}

        super().__init__(**merged_config)

    def _load_yaml_config(self) -> dict:
        """从conf.yaml文件加载配置"""
        if not YAML_AVAILABLE:
            print("警告: PyYAML未安装，跳过YAML配置文件加载")
            return {}

        config_file = Path("conf.yaml")

        if not config_file.exists():
            print(f"警告: 配置文件 {config_file} 不存在")
            return {}

        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                yaml_data = yaml.safe_load(f)

            if not yaml_data:
                return {}

            # 转换YAML数据为Pydantic模型格式
            config = {}

            # 处理app_config
            if 'app_config' in yaml_data:
                try:
                    app_data = yaml_data['app_config']
                    # 处理可能的拼写错误：log_leve -> log_level
                    if 'log_leve' in app_data and 'log_level' not in app_data:
                        app_data['log_level'] = app_data.pop('log_leve')

                    config['app_config'] = AppConfig(**app_data)
                    print("✅ 应用配置加载成功")
                except Exception as e:
                    print(f"❌ 应用配置解析失败: {e}")

            # 处理vllm_config
            if 'vllm_config' in yaml_data:
                vllm_data = yaml_data['vllm_config']
                try:
                    config['vllm_config'] = VLLMConfig(
                        chat=VLLMServiceConfig(**vllm_data['chat']),
                        embedding=VLLMServiceConfig(**vllm_data['embedding']),
                        rerank=VLLMServiceConfig(**vllm_data['rerank'])
                    )
                    print("✅ VLLM配置加载成功")
                except Exception as e:
                    print(f"❌ VLLM配置解析失败: {e}")

            # 处理qdrant_config
            if 'qdrant_config' in yaml_data:
                try:
                    config['qdrant_config'] = QdrantConfig(**yaml_data['qdrant_config'])
                    print("✅ Qdrant配置加载成功")
                except Exception as e:
                    print(f"❌ Qdrant配置解析失败: {e}")
                    
            # 处理postgres_config
            if 'postgres_config' in yaml_data:
                try:
                    config['postgres_config'] = PostgresConfig(**yaml_data['postgres_config'])
                    print("✅ PostgreSQL配置加载成功")
                except Exception as e:
                    print(f"❌ PostgreSQL配置解析失败: {e}")
                    
            # 处理neo4j_config
            if 'neo4j_config' in yaml_data:
                try:
                    config['neo4j_config'] = Neo4jConfig(**yaml_data['neo4j_config'])
                    print("✅ Neo4j配置加载成功")
                except Exception as e:
                    print(f"❌ Neo4j配置解析失败: {e}")

            if 'minio_config' in yaml_data:
                try:
                    config['minio_config'] = MinioConfig(**yaml_data['minio_config'])
                    print("✅ Minio配置加载成功")
                except Exception as e:
                    print(f"❌ Minio配置解析失败: {e}")

            if 'docling_config' in yaml_data:
                try:
                    config['docling_config'] = DoclingConfig(**yaml_data['docling_config'])
                    print("✅ Docling配置加载成功")
                except Exception as e:
                    print(f"❌ Docling配置解析失败: {e}")

            if 'redis_config' in yaml_data:
                try:
                    config['redis_config'] = RedisConfig(**yaml_data['redis_config'])
                    print("✅ Redis配置加载成功")
                except Exception as e:
                    print(f"❌ Redis配置解析失败: {e}")
            return config

        except Exception as e:
            print(f"❌ 加载配置文件失败: {e}")
            return {}

    # 便利方法
    def get_app_name(self) -> str:
        """获取应用名称"""
        return self.app_config.name if self.app_config else "RAG Document System"

    def get_app_version(self) -> str:
        """获取应用版本"""
        return self.app_config.version if self.app_config else "1.0.0"

    def is_debug_mode(self) -> bool:
        """是否为调试模式"""
        return self.app_config.debug if self.app_config else False

    def get_log_level(self) -> str:
        """获取日志级别"""
        return self.app_config.log_level if self.app_config else "INFO"

    def get_chat_config(self) -> Optional[VLLMServiceConfig]:
        """获取聊天服务配置"""
        return self.vllm_config.chat if self.vllm_config else None

    def get_embedding_config(self) -> Optional[VLLMServiceConfig]:
        """获取嵌入服务配置"""
        return self.vllm_config.embedding if self.vllm_config else None

    def get_rerank_config(self) -> Optional[VLLMServiceConfig]:
        """获取重排序服务配置"""
        return self.vllm_config.rerank if self.vllm_config else None

    def get_qdrant_url(self) -> str:
        """获取Qdrant连接URL"""
        if self.qdrant_config:
            return f"http://{self.qdrant_config.host}:{self.qdrant_config.port}"
        return "http://localhost:6333"
        
    def get_postgres_dsn(self) -> str:
        """获取PostgreSQL连接DSN"""
        if self.postgres_config:
            return f"postgres://{self.postgres_config.user}:{self.postgres_config.password}@{self.postgres_config.host}:{self.postgres_config.port}/{self.postgres_config.dbname}"
        return "postgres://postgres:postgres@localhost:5432/postgres"
        
    def get_neo4j_config(self) -> Dict[str, Any]:
        """获取Neo4j连接配置"""
        if self.neo4j_config:
            return {
                'uri': self.neo4j_config.uri,
                'user': self.neo4j_config.user,
                'password': self.neo4j_config.password,
                'database': self.neo4j_config.database,
                'max_connection_lifetime': self.neo4j_config.max_connection_lifetime,
                'max_connection_pool_size': self.neo4j_config.max_connection_pool_size
            }
        return {
            'uri': 'bolt://localhost:7687',
            'user': 'neo4j',
            'password': 'neo4j',
            'database': 'neo4j',
            'max_connection_lifetime': 3600,
            'max_connection_pool_size': 50
        }


settings = Settings()