import asyncio
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn

from app.api.document_router import document_router
from app.api.intelligent_search_router import search_router
from app.api.knowledge import knowledge_router
from app.config.settings import settings
from app.service.document.parse_document import initialize_document_queue
from app.service.model_client import model_client
from app.utils.logger import setup_logger
from app.database.postgres_manager import postgres_manager
from app.database.minio_manager import minio_manager


logger = setup_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    logger.info("RAG系统启动中...")
    try:
        #     初始化postgreSql数据库
        await postgres_manager.initialize()
        logger.info("PostgreSQL数据库连接成功")
        # 初始化Minio
        await minio_manager.initialize()
        # 检查vLLM服务连接
        await model_client.check_health()
        # 初始化文档处理队列
        await initialize_document_queue()
    except Exception as e:
        logger.error(f"系统初始化失败: {e}")
        raise HTTPException(status_code=500, detail="系统初始化失败")
    yield
    logger.info("RAG系统关闭中...")

# 创建FastAPI应用
app = FastAPI(
    title=settings.app_config.name,
    version=settings.app_config.version,
    description="基于RAG技术的智能文档分析与报告生成系统",
    lifespan=lifespan
)

app.include_router(document_router)
app.include_router(search_router)
app.include_router(knowledge_router)

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
