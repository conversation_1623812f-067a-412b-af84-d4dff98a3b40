from pydantic import BaseModel
from datetime import datetime
from typing import Dict, Any

class BaseResponse(BaseModel):
    """所有响应模型的基础类，包含通用字段"""
    code: int = 0
    message: str = "success"

class DocumentUploadResponse(BaseResponse):
    """文档上传响应数据模型"""
    file_info: Dict[str, Any]  # 包含文件基本信息的字典
    upload_time: str = datetime.now().isoformat()  # 上传时间戳



class ParseDocumentRequest(BaseModel):
    file_hash: list[str]

class ParseDocumentResponse(BaseResponse):
    """文档解析响应数据模型"""

# 文档列表
class DocumentListResponse(BaseResponse):
    """文档列表响应数据模型"""
    documents: list[Dict[str, Any]]
    total: int