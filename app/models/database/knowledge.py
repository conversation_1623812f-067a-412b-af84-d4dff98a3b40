from tortoise import fields
from tortoise.models import Model


class KnowledgeBase(Model):
    """知识库表"""
    id = fields.IntField(primary_key=True)
    name = fields.CharField(max_length=255, unique=True, index=True)
    name_md5 = fields.CharField(max_length=32, unique=True, index=True)
    description = fields.TextField(null=True)
    public = fields.BooleanField(default=False)
    created_at = fields.DatetimeField(auto_now_add=True)
    updated_at = fields.DatetimeField(auto_now=True)

    class Meta:
        table = "knowledge_base"

    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "name": self.name,
            "name_md5": self.name_md5,
            "description": self.description,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "public": self.public,
        }