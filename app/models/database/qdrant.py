from datetime import datetime
from typing import List

from pydantic import BaseModel, Field


class DocumentEmbedding(BaseModel):
    """文档嵌入向量"""
    id: int = Field(description="文档切片id")
    file_hash: str = Field(description="文件哈希值")
    text: str = Field(description="文档内容")
    embedding: List[float] = Field(description="嵌入向量")
    created_at: datetime = Field(default_factory=datetime.now)


    def to_dict(self) -> dict:
        return {
            "id": self.id,
            "file_hash": self.file_hash,
            "text": self.text,
            "embedding": self.embedding,
            "created_at": self.created_at.isoformat()
        }