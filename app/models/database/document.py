"""
数据库模型定义
使用Tortoise ORM
"""
import json

from tortoise import fields
from tortoise.models import Model
from app.utils.logger import setup_logger
from enum import Enum

class AnalysisStatus(str, Enum):
    NOT_STARTED = "not_started"  # 未分析
    ANALYZING = "analyzing"      # 分析中
    FAILED = "failed"            # 分析失败
    COMPLETED = "completed"      # 分析完成
logger = setup_logger(__name__)


class FileInfo(Model):
    """文件信息表"""

    id = fields.IntField(primary_key=True,  generated=True)
    file_hash = fields.CharField(max_length=32, unique=True, index=True)
    original_filename = fields.CharField(max_length=255, index=True)
    bucket_name = fields.CharField(max_length=255, index=True)
    minio_name = fields.CharField(max_length=500)
    file_size = fields.BigIntField()
    file_type = fields.CharField(max_length=10)
    etag = fields.CharField(max_length=36, null=True, index=True)
    metadata = fields.JSONField(null=True)
    upload_time = fields.DatetimeField(auto_now_add=True)
    created_at = fields.DatetimeField(auto_now_add=True)
    updated_at = fields.DatetimeField(auto_now=True)
    summary = fields.TextField(null=True)
    knowledge_name = fields.CharField(max_length=255, index=True)
    analysis_status = fields.CharField(
        max_length=20,
        default=AnalysisStatus.NOT_STARTED.value,
        index=True
    )
    processed_status = fields.TextField(null=True)

    class Meta:
        table = "file_info"

    def to_dict(self) -> dict:
        """转换为字典"""

        try:
            # 尝试将 metadata 转换为 JSON 格式，验证其可序列化
            json.dumps(self.metadata)
        except (TypeError, OverflowError):
            # 如果 metadata 无法被序列化，则记录错误并提供默认值或清理后的数据
            logger.warning("Metadata contains non-serializable data.")
              # 可选：提取可序列化的部分或清理不可序列化字段
            self.metadata = {k: v for k, v in self.metadata.items() if isinstance(v, (str, int, float, bool, type(None)))}
        return {
            "id": self.id,
            "file_hash": self.file_hash,
            "original_filename": self.original_filename,
            "bucket_name": self.bucket_name,
            "minio_name": self.minio_name,
            "file_size": self.file_size,
            "file_type": self.file_type,
            "etag": self.etag,
            "metadata": self.metadata,
            "upload_time": self.upload_time.isoformat() if self.upload_time else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "summary": self.summary,
            "knowledge_name": self.knowledge_name,
            "analysis_status": self.analysis_status
            , "processed_status": self.processed_status
        }