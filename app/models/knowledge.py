# 创建知识库请求
from typing import Dict, Any, List, Optional

from pydantic import BaseModel, Field

from app.models.document_upload import BaseResponse


class CreateKnowledgeRequest(BaseModel):
    name: str = Field(description="知识库名称")
    description: str = Field(description="知识库描述")
    public: bool = Field(description="是否公开", default=False)

# 创建知识库响应
class CreateKnowledgeResponse(BaseResponse):
    knowledge: Dict[str, Any]  # 包含知识库基本信息的字典


class DeleteKnowledgeResponse(BaseResponse):
    data: Dict[str, Any]

class KnowledgeListResponse(BaseResponse):
    data: List[dict]
    # total: int

class ReportRequest(BaseModel):
    title: str
    collection_name: str

class ChatMessage(BaseModel):
    role: str = Field(description="消息角色: user/assistant")
    content: str = Field(description="消息内容")
    timestamp: Optional[str] = Field(default=None, description="时间戳")

class ChatRequest(BaseModel):
    question: str = Field(description="用户问题", min_length=1)
    collection_name: str = Field(default="documents", description="知识库名称")
    conversation_history: List[ChatMessage] = Field(default=[], description="对话历史")
    max_history: int = Field(default=5, description="最大历史记录数")

class SourceReference(BaseModel):
    file_name: str = Field(description="文件名")
    file_hash: str = Field(description="文件哈希")
    chunk_text: str = Field(description="相关文本片段")
    relevance_score: float = Field(description="相关性评分")

class ChatResponse(BaseModel):
    answer: str = Field(description="回答内容")
    confidence_score: float = Field(description="置信度评分 0-1")
    knowledge_used: bool = Field(description="是否使用了知识库")
    sources: List[SourceReference] = Field(default=[], description="引用来源")
    reasoning_process: str = Field(description="推理过程")