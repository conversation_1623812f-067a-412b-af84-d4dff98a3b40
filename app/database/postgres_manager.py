"""
PostgreSQL数据库管理器
处理文件信息存储和重复检测
"""

import hashlib
from typing import Optional, Dict, Any

from app.models.database.document import FileInfo
from tortoise import Tortoise, connections
from tortoise.exceptions import DoesNotExist

from app.config.settings import settings
from app.utils.exceptions import RAGSystemException
from app.utils.logger import setup_logger

logger = setup_logger(__name__)


class PostgresManager:
    """PostgreSQL管理器"""

    def __init__(self):
        self.is_initialized = False
        self.logger = logger

    async def initialize(self):
        """初始化Tortoise ORM连接"""
        try:
            # 初始化Tortoise ORM
            await Tortoise.init(
                db_url=settings.get_postgres_dsn(),
                modules={"models": ["app.models.database"]},
            )

            # 生成数据库架构
            await Tortoise.generate_schemas()

            self.is_initialized = True
            self.logger.info("Tortoise ORM初始化成功")

        except Exception as e:
            self.logger.error(f"Tortoise ORM初始化失败: {e}")
            raise RAGSystemException(f"数据库初始化失败: {str(e)}")

    async def calculate_file_hash(self, file_content: bytes) -> str:
        """计算文件MD5哈希"""
        return hashlib.md5(file_content).hexdigest()

    async def check_file_exists(self, file_hash: str, knowledge_hash: str) -> Optional[Dict[str, Any]]:
        """
        检查文件是否已存在

        Args:
            file_hash: 文件MD5哈希
            knowledge_hash:

        Returns:
            如果存在返回文件信息，否则返回None
            :param
        """
        try:
            # 首先尝试使用 get() 方法
            try:
                file_info = await FileInfo.get(file_hash=file_hash, knowledge_name=knowledge_hash)
                return file_info.to_dict()
            except Exception as get_error:
                # 如果 get() 失败（可能是多个对象），使用 filter().first()
                file_info = await FileInfo.filter(file_hash=file_hash, knowledge_name=knowledge_hash).first()
                if file_info:
                    # 检查是否存在重复记录
                    duplicate_count = await FileInfo.filter(file_hash=file_hash, knowledge_name=knowledge_hash).count()
                    if duplicate_count > 1:
                        self.logger.warning(f"发现重复的文件记录: file_hash={file_hash}, knowledge_hash={knowledge_hash}, 数量={duplicate_count}")
                    return file_info.to_dict()
                else:
                    return None
        except DoesNotExist:
            return None
        except Exception as e:
            self.logger.error(f"检查文件存在性时发生异常: file_hash={file_hash}, knowledge_hash={knowledge_hash}, 错误={e}")
            return None
    async def close(self):
        """关闭Tortoise ORM连接"""
        if self.is_initialized:
            await connections.close_all()
            self.logger.info("Tortoise ORM连接已关闭")
            self.is_initialized = False


# 全局PostgreSQL管理器实例
postgres_manager = PostgresManager()