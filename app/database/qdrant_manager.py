"""
Qdrant向量数据库管理器
高性能向量搜索的替代方案
"""
import time
from typing import List

from qdrant_client import QdrantClient
from qdrant_client.http.models import Distance, VectorParams, PointStruct, models

from app.config.settings import settings

from app.utils.logger import setup_logger
from app.utils.exceptions import VectorStoreError

logger = setup_logger(__name__)


class QdrantManager:
    """Qdrant向量数据库管理器"""

    def __init__(self):
        self.settings = settings
        self.logger = logger
        self.client = None
        self.collection_name = settings.qdrant_config.collection_name
        self.vector_size = settings.qdrant_config.vector_size
        self._initialize_client()

    def _initialize_client(self):
        """初始化Qdrant客户端"""
        try:
            # 支持本地和远程部署
            self.client = QdrantClient(
                host=self.settings.qdrant_config.host,
                port=self.settings.qdrant_config.port,
            )

            # 创建集合
            self._create_collection_if_not_exists()

            self.logger.info("Qdrant客户端初始化成功")

        except Exception as e:
            self.logger.error(f"Qdrant客户端初始化失败: {e}")
            raise VectorStoreError(f"Qdrant初始化失败: {str(e)}")
    def _create_collection_if_not_exists(self):
        """创建集合（如果不存在）"""
        try:
            # 检查集合是否存在
            collections = self.client.get_collections()
            collection_names = [col.name for col in collections.collections]

            if self.collection_name not in collection_names:
                # 创建集合
                self.client.create_collection(
                    collection_name=self.collection_name,
                    vectors_config=VectorParams(
                        size=self.vector_size,
                        distance=Distance.COSINE
                    )
                )
                self.logger.info(f"创建Qdrant集合: {self.collection_name}")

        except Exception as e:
            self.logger.error(f"创建Qdrant集合失败: {e}")
            raise VectorStoreError(f"集合创建失败: {str(e)}")

    def store_vectors(self, points: list[PointStruct], collection_name: str, vector_size: int):
        """存储向量，并添加错误处理和日志调试信息"""
        start_time = time.time()  # 记录开始时间
        
        try:
            # 检查集合是否存在
            self.collection_name = collection_name
            self.vector_size = vector_size
            self._create_collection_if_not_exists()
            
            # 将数据拆分成批次
            def chunk_data(data, batch_size):
                for i in range(0, len(data), batch_size):
                    yield data[i:i + batch_size]

            # 记录批次处理开始
            logger.info(f"开始插入 {len(points)} 个向量，批次大小: 100")
            
            for batch in chunk_data(points, 100):
                try:
                    qdrant_manager.client.upsert(
                        collection_name=collection_name,  # 目标集合名称
                        points=batch  # 当前批次的向量
                    )
                    # 记录每个批次插入成功的信息
                    logger.info(f"成功插入批次，大小: {len(batch)}")
                except Exception as e:
                    # 记录每个批次插入失败的错误信息
                    logger.error(f"向量插入失败，错误信息: {e}", exc_info=True)
                    raise  # 重新抛出异常以终止整个操作
            
            # 记录整个插入过程结束
            elapsed_time = time.time() - start_time
            logger.info(f"向量插入完成，总耗时: {elapsed_time:.2f}s")
        except Exception as e:
            # 记录整体插入过程中的错误信息
            logger.error(f"向量插入过程中发生错误: {e}", exc_info=True)
            raise

    async def search_vectors(
            self,
            collection_name: str,
            query_vector: List[float],
            limit: int = 10,
            score_threshold: float = 0.0,
            filter_conditions: dict = None
    ) -> List:
        """搜索向量"""
        try:
            # 构建搜索请求
            search_result = self.client.search(
                collection_name=collection_name,
                query_vector=query_vector,
                limit=limit,
                score_threshold=score_threshold,
                with_payload=True,
                with_vectors=False
            )

            self.logger.info(f"向量搜索完成，返回 {len(search_result)} 个结果")
            return search_result

        except Exception as e:
            self.logger.error(f"向量搜索失败: {e}")
            raise VectorStoreError(f"搜索失败: {str(e)}")

    def get_collection_info(self, collection_name: str) -> dict:
        """获取集合信息"""
        try:
            collection_info = self.client.get_collection(collection_name)
            return {
                "name": collection_info.config.params.vectors.size,
                "vectors_count": collection_info.vectors_count,
                "points_count": collection_info.points_count
            }
        except Exception as e:
            self.logger.error(f"获取集合信息失败: {e}")
            return {}

    def delete_vectors(self, collection_name: str, point_ids: List[int]):
        """删除向量"""
        try:
            self.client.delete(
                collection_name=collection_name,
                points_selector=point_ids
            )
            self.logger.info(f"删除了 {len(point_ids)} 个向量")
        except Exception as e:
            self.logger.error(f"删除向量失败: {e}")
            raise VectorStoreError(f"删除失败: {str(e)}")

    def delete_collection(self, collection_name: str) -> bool:
        """删除整个集合
        :param collection_name: 集合名称
        :return: 删除是否成功
        """
        try:
            # 检查集合是否存在
            collections = self.client.get_collections()
            collection_names = [col.name for col in collections.collections]

            if collection_name not in collection_names:
                self.logger.warning(f"集合不存在: {collection_name}")
                return True

            # 删除集合
            self.client.delete_collection(collection_name)
            self.logger.info(f"成功删除Qdrant集合: {collection_name}")
            return True

        except Exception as e:
            self.logger.error(f"删除Qdrant集合失败: {collection_name}, 错误: {e}")
            return False

    def delete_vectors_by_filter(self, collection_name: str, filter_conditions) -> bool:
        """根据过滤条件删除向量
        :param collection_name: 集合名称
        :param filter_conditions: Qdrant Filter 对象或字典
        :return: 删除是否成功
        """
        try:
            # 检查集合是否存在
            collections = self.client.get_collections()
            collection_names = [col.name for col in collections.collections]

            if collection_name not in collection_names:
                self.logger.warning(f"集合不存在: {collection_name}")
                return True

            # 如果是字典，转换为 Filter 对象
            if isinstance(filter_conditions, dict):
                filter_obj = self._build_filter_from_dict(filter_conditions)
            else:
                filter_obj = filter_conditions

            # 根据过滤条件删除向量
            self.client.delete(
                collection_name=collection_name,
                points_selector=models.FilterSelector(filter=filter_obj)
            )
            self.logger.info(f"成功根据过滤条件删除向量: {collection_name}")
            return True

        except Exception as e:
            self.logger.error(f"根据过滤条件删除向量失败: {collection_name}, 错误: {e}")
            return False

    def _build_filter_from_dict(self, filter_dict: dict) -> models.Filter:
        """将字典格式的过滤条件转换为 Qdrant Filter 对象"""
        conditions = []

        if "must" in filter_dict:
            for condition in filter_dict["must"]:
                if "key" in condition and "match" in condition:
                    field_condition = models.FieldCondition(
                        key=condition["key"],
                        match=models.MatchValue(value=condition["match"]["value"])
                    )
                    conditions.append(field_condition)

        return models.Filter(must=conditions)
    def get_collection_points_count(self, collection_name: str) -> int:
        """获取集合中的点数量
        :param collection_name: 集合名称
        :return: 点数量，如果集合不存在返回0
        """
        try:
            collections = self.client.get_collections()
            collection_names = [col.name for col in collections.collections]

            if collection_name not in collection_names:
                return 0

            collection_info = self.client.get_collection(collection_name)
            return collection_info.points_count

        except Exception as e:
            self.logger.error(f"获取集合点数量失败: {collection_name}, 错误: {e}")
            return 0


qdrant_manager = QdrantManager()