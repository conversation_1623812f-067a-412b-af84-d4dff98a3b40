from typing import Any, Dict

from minio import Minio
from urllib3 import BaseHTTPResponse

from app.config.settings import settings
from app.utils.logger import setup_logger

logger = setup_logger(__name__)


class MinioManager:
    """Minio对象存储管理器"""

    def __init__(self):
        self.client = None
        self.logger = logger

    async def initialize(self):
        """初始化MinIO客户端"""
        minio_config = settings.minio_config
        if not minio_config:
            raise ValueError("MinIO配置未正确加载")

        try:
            # 创建MinIO客户端实例
            self.client = Minio(
                minio_config.endpoint,
                access_key=minio_config.access_key,
                secret_key=minio_config.secret_key,
                secure=minio_config.secure
            )
            self.logger.info(f"MinIO客户端初始化成功: {minio_config.endpoint}")
        except Exception as e:
            self.logger.error(f"MinIO客户端初始化失败: {str(e)}")
            raise

    async def upload_file(self, bucket_name: str, object_name: str, file_path: str) -> str:
        """上传文件到MinIO并返回ETag
        :param bucket_name: 存储桶名称
        :param object_name: MinIO中的对象名称
        :param file_path: 本地文件路径
        :return: 文件的ETag
        """
        try:
            # 检查存储桶是否存在，如果不存在则创建
            if not self.client.bucket_exists(bucket_name):
                self.client.make_bucket(bucket_name)
                self.logger.info(f"存储桶 {bucket_name} 不存在，已自动创建")

            # 上传文件并获取ETag
            result =  self.client.fput_object(bucket_name, object_name, file_path)
            self.logger.info(f"文件上传成功: {file_path} -> {bucket_name}/{object_name}")
            
            # 返回ETag
            return result.etag
        except Exception as e:
            self.logger.error(f"文件上传失败: {file_path} -> {bucket_name}/{object_name}, 错误: {str(e)}")
            raise

    async def get_file(self, bucket_name: str, object_name: str) -> BaseHTTPResponse:
        """从MinIO中获取文件
        :param bucket_name: 存储桶名称
        :param object_name: MinIO中的对象名称
        :return: 文件内容
        """
        try:
            return self.client.get_object(bucket_name, object_name)
        except Exception as e:
            self.logger.error(f"文件获取失败: {bucket_name}/{object_name}, 错误: {str(e)}")
            raise

    async def delete_object(self, bucket_name: str, object_name: str) -> bool:
        """删除MinIO中的单个对象
        :param bucket_name: 存储桶名称
        :param object_name: 对象名称
        :return: 删除是否成功
        """
        try:
            self.client.remove_object(bucket_name, object_name)
            self.logger.info(f"成功删除对象: {bucket_name}/{object_name}")
            return True
        except Exception as e:
            self.logger.error(f"删除对象失败: {bucket_name}/{object_name}, 错误: {str(e)}")
            return False

    async def delete_bucket(self, bucket_name: str, force: bool = False) -> bool:
        """删除MinIO存储桶
        :param bucket_name: 存储桶名称
        :param force: 是否强制删除（先删除所有对象）
        :return: 删除是否成功
        """
        try:
            # 检查存储桶是否存在
            if not self.client.bucket_exists(bucket_name):
                self.logger.warning(f"存储桶不存在: {bucket_name}")
                return True

            if force:
                # 先删除存储桶中的所有对象
                objects = self.client.list_objects(bucket_name, recursive=True)
                deleted_count = 0
                failed_count = 0

                for obj in objects:
                    try:
                        self.client.remove_object(bucket_name, obj.object_name)
                        deleted_count += 1
                        self.logger.debug(f"删除对象: {bucket_name}/{obj.object_name}")
                    except Exception as e:
                        failed_count += 1
                        self.logger.error(f"删除对象失败: {bucket_name}/{obj.object_name}, 错误: {str(e)}")

                self.logger.info(f"存储桶 {bucket_name} 中删除了 {deleted_count} 个对象，{failed_count} 个失败")

            # 删除存储桶
            self.client.remove_bucket(bucket_name)
            self.logger.info(f"成功删除存储桶: {bucket_name}")
            return True

        except Exception as e:
            self.logger.error(f"删除存储桶失败: {bucket_name}, 错误: {str(e)}")
            return False

    async def list_objects(self, bucket_name: str) -> list:
        """列出存储桶中的所有对象
        :param bucket_name: 存储桶名称
        :return: 对象列表
        """
        try:
            if not self.client.bucket_exists(bucket_name):
                self.logger.warning(f"存储桶不存在: {bucket_name}")
                return []

            objects = self.client.list_objects(bucket_name, recursive=True)
            object_list = []
            for obj in objects:
                object_list.append({
                    'name': obj.object_name,
                    'size': obj.size,
                    'last_modified': obj.last_modified,
                    'etag': obj.etag
                })

            self.logger.info(f"存储桶 {bucket_name} 中有 {len(object_list)} 个对象")
            return object_list

        except Exception as e:
            self.logger.error(f"列出对象失败: {bucket_name}, 错误: {str(e)}")
            return []

minio_manager = MinioManager()
