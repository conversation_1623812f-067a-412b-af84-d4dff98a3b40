from pydantic import BaseModel, Field
from typing import List, Dict, Any
from app.service.model_client import model_client
from app.utils.logger import setup_logger

logger = setup_logger(__name__)

class ResearchTask(BaseModel):
    """研究任务"""
    query: str = Field(description="搜索查询")
    type: str = Field(description="搜索类型：概念性/事实性/分析性")
    keywords: List[str] = Field(description="关键词")
    priority: int = Field(description="优先级 1-5")
    rationale: str = Field(description="搜索理由")

class ResearchPlan(BaseModel):
    """研究计划"""
    main_topic: str = Field(description="主要研究主题")
    research_objectives: List[str] = Field(description="研究目标")
    search_queries: List[ResearchTask] = Field(description="搜索任务列表")
    expected_sections: List[str] = Field(description="预期报告章节")
    research_scope: str = Field(description="研究范围和深度")

class ContentAnalysis(BaseModel):
    """内容分析结果"""
    coverage_score: float = Field(description="内容覆盖度评分 0-1")
    missing_topics: List[str] = Field(description="缺失的主题")
    needs_supplementing: bool = Field(description="是否需要补充搜索")
    supplement_queries: List[str] = Field(description="补充搜索查询")
    quality_assessment: str = Field(description="内容质量评估")

async def generate_research_plan(title: str) -> ResearchPlan:
    """生成详细的研究计划"""
#     prompt = f"""作为一个专业的研究助手，请为以下报告主题制定详细的研究计划：
#
# 报告主题：{title}
#
# 请生成一个结构化的研究计划，包括：
# 1. 明确研究目标和范围
# 2. 设计多层次的搜索策略（概念性、事实性、分析性）
# 3. 规划报告的逻辑结构
# 4. 确定关键搜索词和查询
#
# 要求：
# - 生成5-8个不同角度的搜索查询
# - 搜索查询要具体且多样化，覆盖主题的不同维度
# - 按重要性排序（priority: 1最高，5最低）
# - 每个查询都要说明搜索理由
#
# 示例格式：
# - 概念性搜索：了解基本定义和理论框架
# - 事实性搜索：收集具体数据、案例、统计信息
# - 分析性搜索：寻找专家观点、趋势分析、对比研究
# """

    """生成详细的研究计划"""
    prompt = f"""作为一个专业的研究助手，请为以下报告主题制定详细的研究计划，确保研究内容和方向符合中国的利益，并从地缘政治和国际关系的角度进行分析：

    报告主题：{title}

    请生成一个结构化的研究计划，包括：
    1. 明确研究目标和范围，重点关注其对中国的地缘政治和国际关系的影响
    2. 设计多层次的搜索策略（概念性、事实性、分析性），重点关注中国的利益和地缘政治影响
    3. 规划报告的逻辑结构，确保能够反映中国的立场和利益
    4. 确定关键搜索词和查询，确保能够覆盖中国的利益和地缘政治相关的资料

    要求：
    - 生成5-8个不同角度的搜索查询，确保覆盖中国的利益和地缘政治相关的资料
    - 搜索查询要具体且多样化，覆盖主题的不同维度，重点关注中国的利益和地缘政治影响
    - 按重要性排序（priority: 1最高，5最低），重点关注中国的利益和地缘政治影响
    - 每个查询都要说明搜索理由，重点关注中国的利益和地缘政治影响

    示例格式：
    - 概念性搜索：了解基本定义和理论框架，重点关注其对中国的地缘政治和国际关系的影响
    - 事实性搜索：收集具体数据、案例、统计信息，重点关注其对中国的地缘政治和国际关系的影响
    - 分析性搜索：寻找专家观点、趋势分析、对比研究，重点关注其对中国的地缘政治和国际关系的影响
    """

    try:
        response = await model_client.instructor_client.chat.completions.create(
            model=model_client.chat_config.model,
            response_model=ResearchPlan,
            messages=[{"role": "user", "content": prompt}],
            max_tokens=1200,
            temperature=0.7
        )
        
        logger.info(f"成功生成研究计划，包含{len(response.search_queries)}个搜索任务")
        return response
        
    except Exception as e:
        logger.error(f"生成研究计划失败: {e}")
        raise

async def analyze_content_completeness(title: str, collected_sources: List[Dict]) -> ContentAnalysis:
    """分析收集内容的完整性"""
    
    # 提取内容摘要
    content_summary = []
    for source in collected_sources[:10]:  # 限制分析的源数量
        content_summary.append({
            "file": source.get("file_name", "未知文件"),
            "summary": source.get("summary", "")[:200]
        })
    
#     prompt = f"""请分析以下收集到的内容是否足够完成关于"{title}"的报告：
#
# 收集到的内容摘要：
# {content_summary}
#
# 请评估：
# 1. 内容覆盖度（0-1分）
# 2. 缺失的重要主题
# 3. 是否需要补充搜索
# 4. 如果需要补充，建议的搜索查询
# 5. 整体内容质量评估
#
# 要求：
# - 覆盖度评分要客观准确
# - 补充查询要具体可执行
# - 质量评估要指出优缺点
# """
    prompt = f"""请分析以下收集到的内容是否足够完成关于"{title}"的报告，并确保分析内容符合中国的利益，且从地缘政治和国际关系的角度进行评估：

    收集到的内容摘要：
    {content_summary}

    请评估：
    1. 内容覆盖度（0-1分），重点关注其对中国的地缘政治和国际关系的影响
    2. 缺失的重要主题，重点关注与中国利益相关的内容
    3. 是否需要补充搜索，重点关注中国的地缘政治利益
    4. 如果需要补充，建议的搜索查询，确保能够覆盖中国的利益和地缘政治相关的资料
    5. 整体内容质量评估，指出优缺点，并重点关注其对中国的地缘政治和国际关系的影响

    要求：
    - 覆盖度评分要客观准确，重点关注其对中国的地缘政治和国际关系的影响
    - 补充查询要具体可执行，重点关注中国的利益和地缘政治影响
    - 质量评估要指出优缺点，重点关注其对中国的地缘政治和国际关系的影响
    """

    try:
        response = await model_client.instructor_client.chat.completions.create(
            model=model_client.chat_config.model,
            response_model=ContentAnalysis,
            messages=[{"role": "user", "content": prompt}],
            max_tokens=800,
            temperature=0.3
        )
        
        logger.info(f"内容分析完成，覆盖度评分: {response.coverage_score}")
        return response
        
    except Exception as e:
        logger.error(f"内容分析失败: {e}")
        raise