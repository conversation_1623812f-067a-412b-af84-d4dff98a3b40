from app.service.model_client import model_client
from app.utils.logger import setup_logger

logger = setup_logger(__name__)

class CrossLingualSearchService:
    """跨语言搜索服务"""
    
    def __init__(self):
        self.logger = logger
    
    async def translate_to_russian(self, query: str) -> str:
        """将查询翻译为俄文"""
        try:
            # 如果查询已经是俄文，直接返回
            if self._is_russian(query):
                return query
                
            prompt = f"""请将以下查询准确翻译为俄文，保持专业术语和法律用词的准确性：

查询: {query}

俄文翻译:"""
            
            response = await model_client.llm_client.chat.completions.create(
                model=model_client.chat_config.model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=200,
                temperature=0.1
            )
            
            translated = model_client._clean_response_content(response.choices[0].message.content)
            self.logger.info(f"查询翻译: {query} -> {translated}")
            return translated
            
        except Exception as e:
            self.logger.error(f"翻译失败: {e}")
            # 如果翻译失败，返回原始查询
            return query
    
    def _is_russian(self, text: str) -> bool:
        """简单判断文本是否为俄文"""
        # 俄文字符范围: а-я, А-Я
        russian_chars = set('абвгдеёжзийклмнопрстуфхцчшщъыьэюяАБВГДЕЁЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯ')
        # 如果文本中超过30%的字符是俄文字符，则认为是俄文
        text_chars = set(text)
        russian_char_count = len(text_chars.intersection(russian_chars))
        return russian_char_count / max(len(text_chars), 1) > 0.3

cross_lingual_search = CrossLingualSearchService()