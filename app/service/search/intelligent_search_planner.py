from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
from app.service.model_client import model_client
import instructor

class SearchIntent(BaseModel):
    """搜索意图分析结果"""
    main_topic: str = Field(description="主要搜索主题")
    key_entities: List[str] = Field(description="关键实体列表")
    related_concepts: List[str] = Field(description="相关概念")
    search_scope: str = Field(description="搜索范围：specific/broad/comprehensive")
    search_queries: List[str] = Field(description="生成的搜索查询列表")
    filter_conditions: Dict[str, Any] = Field(default={}, description="过滤条件")

class IntelligentSearchPlanner:
    """智能搜索规划器"""
    
    async def analyze_search_intent(self, user_input: str) -> SearchIntent:
        """分析用户搜索意图"""
        
        prompt = f"""请分析以下用户输入的搜索意图，并生成结构化的搜索策略：

用户输入: {user_input}

请分析：
1. 主要搜索主题是什么？
2. 涉及哪些关键实体（人物、组织、地点、法律条文等）？
3. 有哪些相关概念需要一并搜索？
4. 搜索范围是精确查找还是广泛探索？
5. 应该生成哪些具体的搜索查询？
6. 需要什么过滤条件？

请用结构化格式回答。"""

        # 使用Instructor确保结构化输出
        response = await model_client.instructor_client.chat.completions.create(
            model=model_client.chat_config.model,
            response_model=SearchIntent,
            messages=[{"role": "user", "content": prompt}],
            max_tokens=800,
            temperature=0.3
        )
        
        return response
    
    async def generate_search_plan(self, user_input: str) -> Dict[str, Any]:
        """生成完整搜索计划"""
        
        # 分析搜索意图
        intent = await self.analyze_search_intent(user_input)
        
        # 生成多层次搜索策略
        search_plan = {
            "original_query": user_input,
            "intent_analysis": intent,
            "search_strategies": await self._create_search_strategies(intent),
            "execution_order": self._determine_execution_order(intent)
        }
        
        return search_plan
    
    async def _create_search_strategies(self, intent: SearchIntent) -> List[Dict[str, Any]]:
        """创建多维搜索策略"""
        
        strategies = []
        
        # 1. 主题精确搜索
        strategies.append({
            "type": "exact_topic",
            "query": intent.main_topic,
            "weight": 1.0,
            "description": "主题精确匹配"
        })
        
        # 2. 实体相关搜索
        for entity in intent.key_entities:
            strategies.append({
                "type": "entity_search",
                "query": entity,
                "weight": 0.8,
                "description": f"实体搜索: {entity}"
            })
        
        # 3. 概念扩展搜索
        for concept in intent.related_concepts:
            strategies.append({
                "type": "concept_search", 
                "query": concept,
                "weight": 0.6,
                "description": f"概念搜索: {concept}"
            })
        
        # 4. 组合查询搜索
        if len(intent.search_queries) > 1:
            for query in intent.search_queries:
                strategies.append({
                    "type": "generated_query",
                    "query": query,
                    "weight": 0.9,
                    "description": f"生成查询: {query}"
                })
        
        # 5. 语义相似搜索
        strategies.append({
            "type": "semantic_search",
            "query": intent.main_topic + " " + " ".join(intent.key_entities[:3]),
            "weight": 0.7,
            "description": "语义相似性搜索"
        })
        
        return strategies
    
    def _determine_execution_order(self, intent: SearchIntent) -> List[str]:
        """确定搜索执行顺序"""
        
        if intent.search_scope == "specific":
            # 精确搜索：先主题，再实体
            return ["exact_topic", "entity_search", "generated_query"]
        elif intent.search_scope == "broad":
            # 广泛搜索：并行多种策略
            return ["exact_topic", "entity_search", "concept_search", "semantic_search"]
        else:  # comprehensive
            # 全面搜索：所有策略
            return ["exact_topic", "generated_query", "entity_search", "concept_search", "semantic_search"]

intelligent_planner = IntelligentSearchPlanner()