import asyncio
from enum import Enum
from typing import AsyncGenerator, Dict, Any, List
import json
from app.service.multi_strategy_search_engine import MultiStrategySearchEngine
from app.service.research_planner import generate_research_plan, analyze_content_completeness
from app.service.model_client import model_client
from app.utils.logger import setup_logger

logger = setup_logger(__name__)


class ReportEventType(Enum):
    THINKING = "thinking"  # 思考规划阶段
    PLANNING = "planning"  # 生成搜索计划
    SEARCHING = "searching"  # 执行搜索
    FOUND_SOURCES = "found_sources"  # 发现相关文档
    ANALYZING = "analyzing"  # 分析内容
    REFLECTING = "reflecting"  # 反思评估
    SUPPLEMENTING = "supplementing"  # 补充搜索
    GENERATING = "generating"  # 生成报告
    STREAMING = "streaming"  # 流式输出报告内容
    PROGRESS = "progress"  # 进度更新
    COMPLETED = "completed"  # 完成
    ERROR = "error"  # 错误


class ReportGenerator:
    """智能报告生成器"""

    def __init__(self):
        self.search_engine = MultiStrategySearchEngine()
        self.collected_sources = []
        self.search_history = []

    async def generate_report_stream(
            self,
            title: str,
            collection_name: str
    ):
        """流式生成报告 - 完整展示思考过程"""

        try:
            logger.info(f"开始生成报告: {title}")

            # 阶段1: 思考分析
            yield self._emit_event(ReportEventType.THINKING, {
                "status": "正在分析报告主题...",
                "content": f"开始深入分析报告主题：{title}，重点关注其对中国的地缘政治和国际关系的影响",
                "progress": 5
            })

            # 生成详细的研究计划
            yield self._emit_event(ReportEventType.THINKING, {
                "status": "制定研究策略...",
                "content": "基于主题特点，设计多维度搜索策略",
                "progress": 10
            })
            research_plan = await generate_research_plan(title)
            yield self._emit_event(ReportEventType.PLANNING, {
                "status": "研究计划已生成",
                "plan": {
                    "main_topic": research_plan.main_topic,
                    "objectives": research_plan.research_objectives,
                    "expected_sections": research_plan.expected_sections,
                    "search_tasks_count": len(research_plan.search_queries),
                    "research_scope": research_plan.research_scope
                },
                "progress": 15
            })
            # 阶段2: 执行多轮搜索
            total_searches = len(research_plan.search_queries)
            for i, search_task in enumerate(research_plan.search_queries):
                current_progress = 20 + (i * 40 // total_searches)

                # 显示当前搜索任务
                yield self._emit_event(ReportEventType.SEARCHING, {
                    "status": f"执行搜索任务 {i + 1}/{total_searches}",
                    "query": search_task.query,
                    "search_type": search_task.type,
                    "keywords": search_task.keywords,
                    "rationale": search_task.rationale,
                    "priority": search_task.priority,
                    "progress": current_progress
                })
                # 执行搜索
                search_results = await self.search_engine.execute_intelligent_search(
                    collection_name, search_task.query, top_k=10
                )
                # 展示找到的文档
                found_docs = self._extract_document_info(search_results["final_results"])
                yield self._emit_event(ReportEventType.FOUND_SOURCES, {
                    "query": search_task.query,
                    "search_type": search_task.type,
                    "documents": found_docs,
                    "total_found": len(found_docs),
                    "search_strategies": list(search_results.get("strategy_results", {}).keys()) if search_results.get(
                        "strategy_results") else []
                })
                self.collected_sources.extend(search_results["final_results"])
                self.search_history.append({
                    "task": search_task.dict(),
                    "results_count": len(found_docs),
                    "documents": found_docs
                })

                logger.info(f"搜索任务 {i + 1} 完成，找到 {len(found_docs)} 个相关文档")

            # 阶段3: 分析和反思
            yield self._emit_event(ReportEventType.ANALYZING, {
                "status": "分析收集到的内容...",
                "total_sources": len(self.collected_sources),
                "unique_files": len(set(s.get("file_name", "") for s in self.collected_sources)),
                "progress": 65
            })
            content_analysis = await analyze_content_completeness(title, self.collected_sources)
            yield self._emit_event(ReportEventType.REFLECTING, {
                "status": "评估内容完整性",
                "analysis": {
                    "coverage_score": content_analysis.coverage_score,
                    "missing_topics": content_analysis.missing_topics,
                    "quality_assessment": content_analysis.quality_assessment,
                    "needs_supplementing": content_analysis.needs_supplementing
                },
                "progress": 75
            })
            # 阶段4: 补充搜索（如果需要）
            if content_analysis.needs_supplementing and content_analysis.supplement_queries:
                for supplement_query in content_analysis.supplement_queries[:3]:  # 限制补充搜索次数
                    yield self._emit_event(ReportEventType.SUPPLEMENTING, {
                        "status": f"补充搜索: {supplement_query}",
                        "reason": "填补知识空白，提升报告完整性"
                    })
                    supplement_results = await self.search_engine.execute_intelligent_search(
                        collection_name, supplement_query, top_k=5
                    )

                    additional_docs = self._extract_document_info(supplement_results["final_results"])
                    if additional_docs:
                        yield self._emit_event(ReportEventType.FOUND_SOURCES, {
                            "query": supplement_query,
                            "documents": additional_docs,
                            "is_supplement": True,
                            "total_found": len(additional_docs)
                        })
                        self.collected_sources.extend(supplement_results["final_results"])
                        logger.info(f"补充搜索找到 {len(additional_docs)} 个额外文档")

            # 阶段5: 生成最终报告
            yield self._emit_event(ReportEventType.GENERATING, {
                "status": "基于收集的资料生成专业报告...",
                "source_count": len(self.collected_sources),
                "progress": 85
            })
            # 流式处理报告生成
            final_report = ""
            async for report_chunk in self._generate_structured_report(title, self.collected_sources, research_plan):
                if report_chunk["type"] == "content":
                    # 发送流式报告内容
                    yield self._emit_event(ReportEventType.STREAMING, {
                        "content": report_chunk["content"],
                        "full_content": report_chunk["full_content"],
                        "progress": min(85 + len(report_chunk["full_content"]) // 50, 99)
                    })
                elif report_chunk["type"] == "final":
                    final_report = report_chunk["report"]
                elif report_chunk["type"] == "error":
                    final_report = report_chunk["report"]

            # 完成
            yield self._emit_event(ReportEventType.COMPLETED, {
                "report": final_report,
                "metadata": {
                    "total_sources": len(self.collected_sources),
                    "unique_files": len(set(s.get("file_name", "") for s in self.collected_sources)),
                    "search_queries_used": len(self.search_history),
                    "research_plan": research_plan.model_dump(),
                    "content_analysis": content_analysis.model_dump(),
                    "sources_by_file": self._group_sources_by_file()
                },
                "progress": 100
            })

            logger.info(f"报告生成完成: {title}")

        except Exception as e:
            logger.error(f"报告生成失败: {e}")
            yield self._emit_event(ReportEventType.ERROR, {
                "error": str(e),
                "error_type": type(e).__name__
            })

    def _extract_document_info(self, search_results: List[Dict]) -> List[Dict]:
        """提取文档信息用于前端展示"""
        docs = []
        seen_files = set()
        for result in search_results:
            try:
                file_name = result.get("file_name", "未知文件")
                if file_name in seen_files:
                    continue
                seen_files.add(file_name)

                docs.append({
                    "file_name": file_name,
                    "file_hash": result.get("file_hash", ""),
                    "summary": result.get("summary", "")[:200] + "..." if len(
                        result.get("summary", "")) > 200 else result.get("summary", ""),
                    "max_score": result.get("max_score", 0),
                    "chunk_count": len(result.get("chunks", [])),
                    "relevant_chunks": [
                        {
                            "text": chunk.get("payload", {}).get("text", "")[:150] + "...",
                            "score": chunk.get("score", 0)
                        } for chunk in result.get("chunks", [])[:3]  # 只显示前3个最相关的片段
                    ]
                })
            except Exception as e:
                logger.error(f"提取文档信息失败: {e}")
                print(result)

        return docs

    def _group_sources_by_file(self) -> Dict[str, int]:
        """按文件分组统计源数量"""
        file_counts = {}
        for source in self.collected_sources:
            file_name = source.get("file_name", "未知文件")
            file_counts[file_name] = file_counts.get(file_name, 0) + 1
        return file_counts

    async def _generate_structured_report(
            self,
            title: str,
            sources: List[Dict],
            research_plan
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """流式生成结构化报告"""

        # 准备内容摘要
        content_summary = []
        for source in sources[:20]:  # 限制用于生成的源数量
            content_summary.append({
                "file": source.get("file_name", ""),
                "content": source.get("summary", "")[:300]
            })

        #     prompt = f"""基于以下研究资料，生成一份关于"{title}"的专业报告。
        #
        # 研究计划：
        # - 主题：{research_plan.main_topic}
        # - 目标：{research_plan.research_objectives}
        # - 预期章节：{research_plan.expected_sections}
        #
        # 收集到的资料：
        # {content_summary}
        #
        # 要求：
        # 1. 使用Markdown格式
        # 2. 结构清晰，逻辑严谨
        # 3. 包含执行摘要、主体内容、结论建议
        # 4. 适当引用资料来源
        # 5. 字数控制在2000-3000字
        # 6. 专业性强，可读性好
        #
        # 报告结构建议：
        # # {title}
        #
        # ## 执行摘要
        # ## 背景与现状
        # ## 主要发现
        # ## 分析与讨论
        # ## 结论与建议
        # ## 参考资料
        # """
        prompt = f"""基于以下研究资料，生成一份关于"{title}"的专业报告，确保报告内容符合中国的利益，并从地缘政治和国际关系的角度进行分析。

        研究计划：
        - 主题：{research_plan.main_topic}
        - 目标：{research_plan.research_objectives}
        - 预期章节：{research_plan.expected_sections}

        收集到的资料：
        {content_summary}

        要求：
        1. 使用Markdown格式，确保结构清晰
        2. 结构清晰，逻辑严谨，反映中国的立场和利益
        3. 包含执行摘要、主体内容、结论建议，重点关注中国的地缘政治和国际关系
        4. 适当引用资料来源，确保引用内容符合中国的利益
        5. 字数控制在3000-4000字，内容专业性强，可读性好
        6. 报告内容要体现中国的立场，分析内容要符合中国的利益，并从地缘政治和国际关系的角度进行讨论

        报告结构建议：
        # {title}

        ## 执行摘要
        ## 背景与现状，重点关注其对中国的地缘政治和国际关系的影响
        ## 主要发现，重点关注其对中国的地缘政治和国际关系的影响
        ## 分析与讨论，重点关注中国的利益和地缘政治影响
        ## 结论与建议，确保结论符合中国的利益
        ## 参考资料
        """

        try:
            # 使用流式API
            stream = await model_client.llm_client.chat.completions.create(
                model=model_client.chat_config.model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=3000,
                temperature=0.7,
                stream=True
            )

            full_report = ""
            async for chunk in stream:
                if chunk.choices[0].delta.content:
                    content = chunk.choices[0].delta.content
                    full_report += content

                    # 发送流式内容
                    yield {
                        "type": "content",
                        "content": content,
                        "full_content": full_report
                    }

            # 发送最终结果
            yield {
                "type": "final",
                "report": full_report
            }

        except Exception as e:
            logger.error(f"生成报告失败: {e}")
            yield {
                "type": "error",
                "report": f"# {title}\n\n报告生成过程中出现错误：{str(e)}"
            }

    def _emit_event(self, event_type: ReportEventType, data: Dict[str, Any]):
        """发送SSE事件"""
        # return f"event: {event_type.value}\ndata: {json.dumps(data, ensure_ascii=False)}\n\n"
        return {
            "data": json.dumps(data, ensure_ascii=False),
            "event": str(event_type.value)
        }