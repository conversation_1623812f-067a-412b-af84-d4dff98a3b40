import asyncio
from enum import Enum
from typing import AsyncGenerator, Dict, Any, List
import json
import time
from datetime import datetime, timedelta
from app.service.multi_strategy_search_engine import MultiStrategySearchEngine
from app.service.research_planner import generate_research_plan, analyze_content_completeness
from app.service.model_client import model_client
from app.utils.logger import setup_logger

logger = setup_logger(__name__)

class ReportQualityMonitor:
    """报告质量实时监控器"""

    def __init__(self):
        self.quality_history = []
        self.coverage_threshold = 0.7
        self.quality_warnings = []
        self.monitoring_start_time = None

    async def monitor_search_progress(self, title: str, search_results: List[Dict], strategy_group: str) -> Dict[str, Any]:
        """监控搜索进度和质量"""
        try:
            if self.monitoring_start_time is None:
                self.monitoring_start_time = time.time()

            # 计算基础质量指标
            metrics = {
                "source_diversity": self._calculate_source_diversity(search_results),
                "content_relevance": await self._assess_content_relevance(search_results, title),
                "information_freshness": self._check_information_freshness(search_results),
                "coverage_completeness": await self._evaluate_coverage(search_results, title),
                "strategy_group": strategy_group,
                "timestamp": time.time() - self.monitoring_start_time
            }

            # 记录质量历史
            self.quality_history.append(metrics)

            # 生成改进建议
            recommendations = await self._generate_improvement_recommendations(metrics)

            # 检查是否需要质量预警
            quality_warnings = self._check_quality_warnings(metrics)
            if quality_warnings:
                self.quality_warnings.extend(quality_warnings)

            return {
                "quality_metrics": metrics,
                "recommendations": recommendations,
                "warnings": quality_warnings,
                "continue_search": metrics["coverage_completeness"] < self.coverage_threshold,
                "overall_score": self._calculate_overall_quality_score(metrics)
            }

        except Exception as e:
            logger.error(f"质量监控失败: {e}")
            return {
                "quality_metrics": {"error": str(e)},
                "recommendations": ["监控系统异常，建议手动检查结果质量"],
                "warnings": ["质量监控系统故障"],
                "continue_search": True,
                "overall_score": 0.5
            }

    def _calculate_source_diversity(self, search_results: List[Dict]) -> float:
        """计算信息源多样性"""
        if not search_results:
            return 0.0

        unique_files = set()
        file_types = set()

        for result in search_results:
            file_name = result.get("file_name", "")
            unique_files.add(file_name)

            if "." in file_name:
                file_types.add(file_name.split(".")[-1].lower())

        # 多样性评分：文件数量 + 文件类型多样性
        diversity_score = min(1.0, len(unique_files) / 10.0 + len(file_types) / 5.0)
        return round(diversity_score, 3)

    async def _assess_content_relevance(self, search_results: List[Dict], title: str) -> float:
        """评估内容相关性"""
        if not search_results:
            return 0.0

        total_score = 0.0
        valid_results = 0

        for result in search_results:
            max_score = result.get("max_score", 0)
            if max_score > 0:
                total_score += max_score
                valid_results += 1

        if valid_results == 0:
            return 0.0

        avg_relevance = total_score / valid_results
        return round(min(1.0, avg_relevance), 3)

    def _check_information_freshness(self, search_results: List[Dict]) -> float:
        """检查信息时效性"""
        if not search_results:
            return 0.0

        current_year = datetime.now().year
        fresh_content_count = 0
        total_content = 0

        for result in search_results:
            # 检查摘要和内容中的时间信息
            content = str(result.get("summary", "")) + str(result.get("chunks", []))
            total_content += 1

            # 简单的时效性检查
            if str(current_year) in content or str(current_year - 1) in content:
                fresh_content_count += 1
            elif "最新" in content or "近期" in content or "当前" in content:
                fresh_content_count += 0.5

        if total_content == 0:
            return 0.0

        freshness_score = fresh_content_count / total_content
        return round(min(1.0, freshness_score), 3)

    async def _evaluate_coverage(self, search_results: List[Dict], title: str) -> float:
        """评估内容覆盖度"""
        if not search_results:
            return 0.0

        # 基于结果数量和质量的覆盖度评估
        result_count_score = min(1.0, len(search_results) / 15.0)  # 15个结果为满分

        # 基于内容长度的评估
        total_content_length = 0
        for result in search_results:
            chunks = result.get("chunks", [])
            for chunk in chunks:
                text = chunk.get("payload", {}).get("text", "")
                total_content_length += len(text)

        content_length_score = min(1.0, total_content_length / 10000.0)  # 10000字符为满分

        # 综合评分
        coverage_score = (result_count_score * 0.4 + content_length_score * 0.6)
        return round(coverage_score, 3)

    async def _generate_improvement_recommendations(self, metrics: Dict[str, Any]) -> List[str]:
        """生成改进建议"""
        recommendations = []

        if metrics["source_diversity"] < 0.5:
            recommendations.append("建议增加更多不同类型的信息源")

        if metrics["content_relevance"] < 0.6:
            recommendations.append("当前搜索结果相关性较低，建议优化搜索查询")

        if metrics["information_freshness"] < 0.4:
            recommendations.append("缺乏最新信息，建议增加时效性搜索")

        if metrics["coverage_completeness"] < 0.7:
            recommendations.append("内容覆盖度不足，建议执行补充搜索")

        if not recommendations:
            recommendations.append("当前搜索质量良好，可继续进行")

        return recommendations

    def _check_quality_warnings(self, metrics: Dict[str, Any]) -> List[str]:
        """检查质量预警"""
        warnings = []

        if metrics["coverage_completeness"] < 0.4:
            warnings.append("严重警告：内容覆盖度过低")

        if metrics["content_relevance"] < 0.3:
            warnings.append("警告：搜索结果相关性不足")

        if metrics["source_diversity"] < 0.3:
            warnings.append("警告：信息源多样性不足")

        return warnings

    def _calculate_overall_quality_score(self, metrics: Dict[str, Any]) -> float:
        """计算综合质量评分"""
        weights = {
            "source_diversity": 0.2,
            "content_relevance": 0.3,
            "information_freshness": 0.2,
            "coverage_completeness": 0.3
        }

        total_score = 0.0
        for metric, weight in weights.items():
            if metric in metrics and isinstance(metrics[metric], (int, float)):
                total_score += metrics[metric] * weight

        return round(total_score, 3)

    def get_quality_summary(self) -> Dict[str, Any]:
        """获取质量监控总结"""
        if not self.quality_history:
            return {"error": "无质量监控数据"}

        latest_metrics = self.quality_history[-1]
        avg_scores = {}

        # 计算平均分数
        for metric in ["source_diversity", "content_relevance", "information_freshness", "coverage_completeness"]:
            scores = [m.get(metric, 0) for m in self.quality_history if isinstance(m.get(metric), (int, float))]
            avg_scores[metric] = round(sum(scores) / len(scores), 3) if scores else 0.0

        return {
            "monitoring_duration": time.time() - self.monitoring_start_time if self.monitoring_start_time else 0,
            "total_monitoring_points": len(self.quality_history),
            "latest_scores": {k: v for k, v in latest_metrics.items() if isinstance(v, (int, float))},
            "average_scores": avg_scores,
            "total_warnings": len(self.quality_warnings),
            "warning_list": self.quality_warnings,
            "quality_trend": self._analyze_quality_trend()
        }

    def _analyze_quality_trend(self) -> str:
        """分析质量趋势"""
        if len(self.quality_history) < 2:
            return "数据不足"

        recent_scores = [self._calculate_overall_quality_score(m) for m in self.quality_history[-3:]]
        early_scores = [self._calculate_overall_quality_score(m) for m in self.quality_history[:3]]

        recent_avg = sum(recent_scores) / len(recent_scores)
        early_avg = sum(early_scores) / len(early_scores)

        if recent_avg > early_avg + 0.1:
            return "质量持续改善"
        elif recent_avg < early_avg - 0.1:
            return "质量有所下降"
        else:
            return "质量保持稳定"

    def validate_quality_data_structure(self, metrics: Dict[str, Any]) -> Dict[str, Any]:
        """验证质量数据结构的完整性"""
        required_metrics = ["source_diversity", "content_relevance", "information_freshness", "coverage_completeness"]
        validated_metrics = {}

        for metric in required_metrics:
            value = metrics.get(metric)
            if isinstance(value, (int, float)) and 0 <= value <= 1:
                validated_metrics[metric] = value
            else:
                logger.warning(f"质量指标 {metric} 数据异常: {value}，使用默认值 0.5")
                validated_metrics[metric] = 0.5

        # 保留其他有效字段
        for key, value in metrics.items():
            if key not in required_metrics:
                validated_metrics[key] = value

        return validated_metrics

    def get_safe_quality_metrics(self) -> Dict[str, float]:
        """安全地获取最新的质量指标 - 只返回核心数值指标"""
        # 定义核心质量指标
        core_metrics = ["source_diversity", "content_relevance", "information_freshness", "coverage_completeness"]
        default_values = {metric: 0.5 for metric in core_metrics}

        try:
            if not self.quality_history:
                logger.debug("质量历史为空，返回默认质量指标")
                return default_values

            latest_metrics = self.quality_history[-1]
            safe_metrics = {}

            # 只提取和验证核心质量指标
            for metric in core_metrics:
                value = latest_metrics.get(metric)
                if isinstance(value, (int, float)) and 0 <= value <= 1:
                    safe_metrics[metric] = float(value)  # 确保返回float类型
                else:
                    logger.warning(f"质量指标 {metric} 数据异常: {value}，使用默认值 0.5")
                    safe_metrics[metric] = 0.5

            logger.debug(f"成功获取安全质量指标: {safe_metrics}")
            return safe_metrics

        except Exception as e:
            logger.error(f"获取质量指标失败: {e}")
            return default_values

    def extract_core_quality_metrics(self, metrics: Dict[str, Any]) -> Dict[str, float]:
        """从完整的质量数据中提取核心数值指标"""
        core_metrics = ["source_diversity", "content_relevance", "information_freshness", "coverage_completeness"]
        extracted_metrics = {}

        for metric in core_metrics:
            value = metrics.get(metric)
            if isinstance(value, (int, float)) and 0 <= value <= 1:
                extracted_metrics[metric] = float(value)
            else:
                logger.warning(f"提取核心指标时发现异常数据 {metric}: {value}，使用默认值 0.5")
                extracted_metrics[metric] = 0.5

        return extracted_metrics

class DynamicQualityThresholdManager:
    """动态质量阈值管理器"""

    def __init__(self):
        self.base_thresholds = {
            "coverage_completeness": 0.7,
            "content_relevance": 0.6,
            "source_diversity": 0.5,
            "information_freshness": 0.4
        }
        self.complexity_adjustments = {
            "simple": -0.1,
            "moderate": 0.0,
            "complex": 0.15
        }
        self.current_thresholds = self.base_thresholds.copy()

    def determine_topic_complexity(self, title: str, search_results: List[Dict]) -> str:
        """判断主题复杂度"""
        try:
            # 基于标题长度和关键词复杂度
            title_complexity = len(title.split()) / 10.0

            # 基于搜索结果的多样性
            if search_results:
                unique_files = len(set(r.get("file_name", "") for r in search_results))
                result_complexity = unique_files / 20.0
            else:
                result_complexity = 0.0

            # 综合复杂度评分
            complexity_score = (title_complexity + result_complexity) / 2.0

            if complexity_score < 0.3:
                return "simple"
            elif complexity_score < 0.7:
                return "moderate"
            else:
                return "complex"

        except Exception as e:
            logger.error(f"复杂度判断失败: {e}")
            return "moderate"  # 默认中等复杂度

    def adjust_thresholds(self, topic_complexity: str, available_sources: int, search_round: int) -> Dict[str, float]:
        """动态调整质量阈值"""
        try:
            adjusted_thresholds = {}

            for metric, base_threshold in self.base_thresholds.items():
                # 基于复杂度调整
                complexity_adj = self.complexity_adjustments.get(topic_complexity, 0.0)

                # 基于可用资源调整
                if available_sources < 5:
                    resource_adj = -0.15  # 资源不足时降低要求
                elif available_sources > 20:
                    resource_adj = 0.1   # 资源充足时提高要求
                else:
                    resource_adj = 0.0

                # 基于搜索轮次调整（避免无限搜索）
                if search_round > 3:
                    round_adj = -0.1  # 多轮搜索后适当降低要求
                else:
                    round_adj = 0.0

                # 计算最终阈值
                final_threshold = base_threshold + complexity_adj + resource_adj + round_adj
                adjusted_thresholds[metric] = max(0.2, min(0.9, final_threshold))  # 限制在合理范围内

            self.current_thresholds = adjusted_thresholds
            logger.info(f"阈值已调整 - 复杂度: {topic_complexity}, 资源: {available_sources}, 轮次: {search_round}")

            return adjusted_thresholds

        except Exception as e:
            logger.error(f"阈值调整失败: {e}")
            return self.base_thresholds

    def should_continue_search(self, quality_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """判断是否需要继续搜索"""
        try:
            continue_reasons = []
            stop_reasons = []

            for metric, threshold in self.current_thresholds.items():
                current_value = quality_metrics.get(metric, 0.0)

                if current_value < threshold:
                    continue_reasons.append(f"{metric}: {current_value:.3f} < {threshold:.3f}")
                else:
                    stop_reasons.append(f"{metric}: {current_value:.3f} >= {threshold:.3f}")

            should_continue = len(continue_reasons) > 0

            return {
                "should_continue": should_continue,
                "continue_reasons": continue_reasons,
                "stop_reasons": stop_reasons,
                "current_thresholds": self.current_thresholds,
                "quality_gap": self._calculate_quality_gap(quality_metrics)
            }

        except Exception as e:
            logger.error(f"搜索决策判断失败: {e}")
            return {
                "should_continue": True,
                "continue_reasons": ["决策系统异常，建议继续搜索"],
                "stop_reasons": [],
                "current_thresholds": self.current_thresholds,
                "quality_gap": 0.5
            }

    def _calculate_quality_gap(self, quality_metrics: Dict[str, Any]) -> float:
        """计算质量缺口"""
        total_gap = 0.0
        metric_count = 0

        for metric, threshold in self.current_thresholds.items():
            current_value = quality_metrics.get(metric, 0.0)
            if current_value < threshold:
                total_gap += (threshold - current_value)
                metric_count += 1

        return round(total_gap / max(1, metric_count), 3)

    def detect_quality_convergence(self, quality_history: List[Dict]) -> Dict[str, Any]:
        """检测质量收敛情况"""
        try:
            if len(quality_history) < 3:
                return {"converged": False, "reason": "数据不足"}

            # 检查最近3次的质量变化
            recent_scores = []
            for metrics in quality_history[-3:]:
                overall_score = sum(metrics.get(k, 0) for k in self.current_thresholds.keys()) / len(self.current_thresholds)
                recent_scores.append(overall_score)

            # 计算变化幅度
            max_change = max(recent_scores) - min(recent_scores)

            if max_change < 0.05:  # 变化幅度小于5%
                return {
                    "converged": True,
                    "reason": "质量指标已收敛",
                    "stability_score": 1.0 - max_change,
                    "recommendation": "可以停止搜索"
                }
            else:
                return {
                    "converged": False,
                    "reason": f"质量仍在变化 (变化幅度: {max_change:.3f})",
                    "stability_score": 1.0 - max_change,
                    "recommendation": "建议继续搜索"
                }

        except Exception as e:
            logger.error(f"收敛检测失败: {e}")
            return {"converged": False, "reason": "检测异常"}

class EnhancedContentValidator:
    """增强的内容验证器"""

    def __init__(self):
        self.validation_history = []

    async def validate_content_comprehensive(self, title: str, sources: List[Dict]) -> Dict[str, Any]:
        """全面的内容验证"""
        try:
            validation_results = {
                "timeliness_check": await self._check_information_timeliness(sources),
                "balance_assessment": await self._assess_viewpoint_balance(sources, title),
                "credibility_scoring": await self._score_content_credibility(sources),
                "completeness_analysis": await self._analyze_content_completeness_enhanced(sources, title),
                "validation_timestamp": time.time()
            }

            # 记录验证历史
            self.validation_history.append(validation_results)

            # 计算综合验证分数
            validation_results["overall_validation_score"] = self._calculate_validation_score(validation_results)

            return validation_results

        except Exception as e:
            logger.error(f"内容验证失败: {e}")
            return {
                "error": str(e),
                "overall_validation_score": 0.5,
                "validation_timestamp": time.time()
            }

    async def _check_information_timeliness(self, sources: List[Dict]) -> Dict[str, Any]:
        """检查信息时效性"""
        try:
            current_year = datetime.now().year
            timeliness_analysis = {
                "very_recent": 0,    # 当年信息
                "recent": 0,         # 1-2年内信息
                "moderate": 0,       # 3-5年内信息
                "outdated": 0,       # 5年以上信息
                "unknown": 0         # 无法判断时间
            }

            for source in sources:
                content = str(source.get("summary", "")) + str(source.get("chunks", []))

                # 时间关键词检测
                if any(keyword in content for keyword in ["2024", "最新", "近期", "当前"]):
                    timeliness_analysis["very_recent"] += 1
                elif any(str(year) in content for year in [current_year-1, current_year-2]):
                    timeliness_analysis["recent"] += 1
                elif any(str(year) in content for year in range(current_year-5, current_year-2)):
                    timeliness_analysis["moderate"] += 1
                elif any(str(year) in content for year in range(current_year-10, current_year-5)):
                    timeliness_analysis["outdated"] += 1
                else:
                    timeliness_analysis["unknown"] += 1

            total_sources = len(sources)
            if total_sources > 0:
                # 计算时效性评分
                timeliness_score = (
                    timeliness_analysis["very_recent"] * 1.0 +
                    timeliness_analysis["recent"] * 0.8 +
                    timeliness_analysis["moderate"] * 0.6 +
                    timeliness_analysis["outdated"] * 0.3 +
                    timeliness_analysis["unknown"] * 0.5
                ) / total_sources
            else:
                timeliness_score = 0.0

            return {
                "timeliness_breakdown": timeliness_analysis,
                "timeliness_score": round(timeliness_score, 3),
                "total_sources_analyzed": total_sources,
                "recommendations": self._generate_timeliness_recommendations(timeliness_analysis, total_sources)
            }

        except Exception as e:
            logger.error(f"时效性检查失败: {e}")
            return {"error": str(e), "timeliness_score": 0.5}

    async def _assess_viewpoint_balance(self, sources: List[Dict], title: str) -> Dict[str, Any]:
        """评估观点平衡性"""
        try:
            # 关键词分析来判断观点倾向
            positive_keywords = ["优势", "好处", "成功", "发展", "进步", "机遇", "积极"]
            negative_keywords = ["风险", "问题", "挑战", "困难", "失败", "威胁", "消极"]
            neutral_keywords = ["分析", "研究", "调查", "报告", "数据", "统计", "事实"]

            viewpoint_analysis = {
                "positive_sources": 0,
                "negative_sources": 0,
                "neutral_sources": 0,
                "balanced_sources": 0
            }

            for source in sources:
                content = str(source.get("summary", "")).lower()

                positive_count = sum(1 for kw in positive_keywords if kw in content)
                negative_count = sum(1 for kw in negative_keywords if kw in content)
                neutral_count = sum(1 for kw in neutral_keywords if kw in content)

                # 判断观点倾向
                if positive_count > negative_count + 2:
                    viewpoint_analysis["positive_sources"] += 1
                elif negative_count > positive_count + 2:
                    viewpoint_analysis["negative_sources"] += 1
                elif abs(positive_count - negative_count) <= 1 and neutral_count > 0:
                    viewpoint_analysis["balanced_sources"] += 1
                else:
                    viewpoint_analysis["neutral_sources"] += 1

            total_sources = len(sources)
            if total_sources > 0:
                # 计算平衡性评分
                balance_score = (
                    viewpoint_analysis["balanced_sources"] * 1.0 +
                    viewpoint_analysis["neutral_sources"] * 0.8 +
                    min(viewpoint_analysis["positive_sources"], viewpoint_analysis["negative_sources"]) * 0.6
                ) / total_sources
            else:
                balance_score = 0.0

            return {
                "viewpoint_breakdown": viewpoint_analysis,
                "balance_score": round(balance_score, 3),
                "total_sources_analyzed": total_sources,
                "balance_recommendations": self._generate_balance_recommendations(viewpoint_analysis, total_sources)
            }

        except Exception as e:
            logger.error(f"观点平衡性评估失败: {e}")
            return {"error": str(e), "balance_score": 0.5}

    async def _score_content_credibility(self, sources: List[Dict]) -> Dict[str, Any]:
        """评估内容可信度"""
        try:
            credibility_factors = {
                "has_specific_data": 0,      # 包含具体数据
                "has_citations": 0,          # 有引用或来源
                "content_depth": 0,          # 内容深度
                "source_authority": 0        # 来源权威性
            }

            total_sources = len(sources)

            for source in sources:
                content = str(source.get("summary", "")) + str(source.get("chunks", []))

                # 检查具体数据
                if any(char.isdigit() for char in content) and any(unit in content for unit in ["%", "万", "亿", "千", "百"]):
                    credibility_factors["has_specific_data"] += 1

                # 检查引用标识
                if any(indicator in content for indicator in ["根据", "据", "显示", "表明", "研究", "报告"]):
                    credibility_factors["has_citations"] += 1

                # 评估内容深度
                if len(content) > 500:  # 内容较长通常意味着更深入
                    credibility_factors["content_depth"] += 1

                # 评估来源权威性（基于文件名）
                file_name = source.get("file_name", "").lower()
                if any(authority in file_name for authority in ["官方", "政府", "研究院", "大学", "学术"]):
                    credibility_factors["source_authority"] += 1

            # 计算可信度评分
            if total_sources > 0:
                credibility_score = sum(credibility_factors.values()) / (total_sources * 4)  # 4个因子
            else:
                credibility_score = 0.0

            return {
                "credibility_factors": credibility_factors,
                "credibility_score": round(credibility_score, 3),
                "total_sources_analyzed": total_sources,
                "credibility_recommendations": self._generate_credibility_recommendations(credibility_factors, total_sources)
            }

        except Exception as e:
            logger.error(f"可信度评估失败: {e}")
            return {"error": str(e), "credibility_score": 0.5}

    async def _analyze_content_completeness_enhanced(self, sources: List[Dict], title: str) -> Dict[str, Any]:
        """增强的内容完整性分析"""
        try:
            # 基础完整性指标
            completeness_metrics = {
                "topic_coverage": 0.0,       # 主题覆盖度
                "depth_analysis": 0.0,       # 深度分析
                "breadth_coverage": 0.0,     # 广度覆盖
                "context_richness": 0.0      # 上下文丰富度
            }

            if not sources:
                return {"completeness_metrics": completeness_metrics, "completeness_score": 0.0}

            # 主题覆盖度分析
            title_keywords = title.lower().split()
            topic_coverage_count = 0

            for source in sources:
                content = str(source.get("summary", "")).lower()
                if any(keyword in content for keyword in title_keywords):
                    topic_coverage_count += 1

            completeness_metrics["topic_coverage"] = topic_coverage_count / len(sources)

            # 深度分析（基于内容长度和复杂度）
            total_content_length = 0
            complex_content_count = 0

            for source in sources:
                chunks = source.get("chunks", [])
                for chunk in chunks:
                    text = chunk.get("payload", {}).get("text", "")
                    total_content_length += len(text)

                    # 复杂内容检测（包含分析性词汇）
                    if any(word in text for word in ["分析", "评估", "影响", "原因", "结果", "趋势"]):
                        complex_content_count += 1

            completeness_metrics["depth_analysis"] = min(1.0, total_content_length / 20000.0)  # 20000字符为满分

            # 广度覆盖（不同文件类型和来源）
            unique_files = len(set(s.get("file_name", "") for s in sources))
            completeness_metrics["breadth_coverage"] = min(1.0, unique_files / 15.0)  # 15个不同文件为满分

            # 上下文丰富度（时间、地点、人物等要素）
            context_elements = ["时间", "地点", "人物", "组织", "数据", "案例"]
            context_richness = 0

            for source in sources:
                content = str(source.get("summary", ""))
                for element in context_elements:
                    if any(indicator in content for indicator in self._get_context_indicators(element)):
                        context_richness += 1
                        break

            completeness_metrics["context_richness"] = context_richness / len(sources) if sources else 0.0

            # 计算综合完整性评分
            completeness_score = sum(completeness_metrics.values()) / len(completeness_metrics)

            return {
                "completeness_metrics": {k: round(v, 3) for k, v in completeness_metrics.items()},
                "completeness_score": round(completeness_score, 3),
                "total_sources_analyzed": len(sources),
                "completeness_recommendations": self._generate_completeness_recommendations(completeness_metrics)
            }

        except Exception as e:
            logger.error(f"完整性分析失败: {e}")
            return {"error": str(e), "completeness_score": 0.5}

    def _get_context_indicators(self, element: str) -> List[str]:
        """获取上下文要素的指示词"""
        indicators = {
            "时间": ["年", "月", "日", "时期", "阶段", "最近", "目前"],
            "地点": ["国家", "地区", "城市", "地方", "区域", "全球", "国际"],
            "人物": ["专家", "学者", "官员", "领导", "研究员", "分析师"],
            "组织": ["公司", "机构", "组织", "部门", "委员会", "协会"],
            "数据": ["数据", "统计", "调查", "研究", "报告", "指标"],
            "案例": ["案例", "例子", "实例", "经验", "做法", "模式"]
        }
        return indicators.get(element, [])

    def _calculate_validation_score(self, validation_results: Dict[str, Any]) -> float:
        """计算综合验证分数"""
        try:
            scores = []
            weights = {
                "timeliness_check": 0.25,
                "balance_assessment": 0.25,
                "credibility_scoring": 0.25,
                "completeness_analysis": 0.25
            }

            for check_type, weight in weights.items():
                if check_type in validation_results:
                    check_result = validation_results[check_type]
                    if isinstance(check_result, dict):
                        # 提取各检查的评分
                        if "timeliness_score" in check_result:
                            scores.append(check_result["timeliness_score"] * weight)
                        elif "balance_score" in check_result:
                            scores.append(check_result["balance_score"] * weight)
                        elif "credibility_score" in check_result:
                            scores.append(check_result["credibility_score"] * weight)
                        elif "completeness_score" in check_result:
                            scores.append(check_result["completeness_score"] * weight)

            return round(sum(scores), 3) if scores else 0.5

        except Exception as e:
            logger.error(f"验证分数计算失败: {e}")
            return 0.5

    def _generate_timeliness_recommendations(self, timeliness_analysis: Dict, total_sources: int) -> List[str]:
        """生成时效性改进建议"""
        recommendations = []

        if timeliness_analysis["very_recent"] / total_sources < 0.3:
            recommendations.append("建议增加更多最新信息的搜索")

        if timeliness_analysis["outdated"] / total_sources > 0.4:
            recommendations.append("当前信息过时较多，建议重点搜索近期资料")

        if timeliness_analysis["unknown"] / total_sources > 0.5:
            recommendations.append("大量信息缺乏时间标识，建议搜索带有明确时间的资料")

        return recommendations if recommendations else ["时效性良好，无需特别改进"]

    def _generate_balance_recommendations(self, viewpoint_analysis: Dict, total_sources: int) -> List[str]:
        """生成观点平衡性改进建议"""
        recommendations = []

        positive_ratio = viewpoint_analysis["positive_sources"] / total_sources
        negative_ratio = viewpoint_analysis["negative_sources"] / total_sources

        if positive_ratio > 0.7:
            recommendations.append("正面观点过多，建议增加风险评估和批评性分析")
        elif negative_ratio > 0.7:
            recommendations.append("负面观点过多，建议增加积极因素和机遇分析")
        elif viewpoint_analysis["balanced_sources"] / total_sources < 0.2:
            recommendations.append("缺乏平衡性观点，建议搜索综合性分析资料")

        return recommendations if recommendations else ["观点平衡性良好"]

    def _generate_credibility_recommendations(self, credibility_factors: Dict, total_sources: int) -> List[str]:
        """生成可信度改进建议"""
        recommendations = []

        if credibility_factors["has_specific_data"] / total_sources < 0.4:
            recommendations.append("缺乏具体数据支撑，建议搜索包含统计数据的资料")

        if credibility_factors["has_citations"] / total_sources < 0.5:
            recommendations.append("引用来源不足，建议搜索学术性或官方资料")

        if credibility_factors["source_authority"] / total_sources < 0.3:
            recommendations.append("权威来源不足，建议增加官方或学术机构的资料")

        return recommendations if recommendations else ["内容可信度良好"]

    def _generate_completeness_recommendations(self, completeness_metrics: Dict) -> List[str]:
        """生成完整性改进建议"""
        recommendations = []

        if completeness_metrics["topic_coverage"] < 0.6:
            recommendations.append("主题覆盖度不足，建议增加核心主题相关的搜索")

        if completeness_metrics["depth_analysis"] < 0.5:
            recommendations.append("分析深度不够，建议搜索更详细的分析性资料")

        if completeness_metrics["breadth_coverage"] < 0.4:
            recommendations.append("信息来源单一，建议扩大搜索范围")

        if completeness_metrics["context_richness"] < 0.5:
            recommendations.append("上下文信息不足，建议增加背景和案例资料")

        return recommendations if recommendations else ["内容完整性良好"]

class ReportEventType(Enum):
    THINKING = "thinking"           # 思考规划阶段
    PLANNING = "planning"           # 生成搜索计划
    Enhanced = "enhanced"
    SEARCHING = "searching"         # 执行搜索
    FOUND_SOURCES = "found_sources" # 发现相关文档
    ANALYZING = "analyzing"         # 分析内容
    REFLECTING = "reflecting"       # 反思评估
    SUPPLEMENTING = "supplementing" # 补充搜索
    GENERATING = "generating"       # 生成报告
    STREAMING = "streaming"         # 流式输出报告内容
    PROGRESS = "progress"           # 进度更新
    COMPLETED = "completed"         # 完成
    ERROR = "error"                # 错误

class ReportGenerator:
    """智能报告生成器"""

    def __init__(self):
        self.search_engine = MultiStrategySearchEngine()
        self.collected_sources = []
        self.search_history = []
        # 质量控制组件
        self.quality_monitor = ReportQualityMonitor()
        self.threshold_manager = DynamicQualityThresholdManager()
        self.content_validator = EnhancedContentValidator()
        self.search_round = 0
        
    async def generate_report_stream(
        self, 
        title: str, 
        collection_name: str
    ):
        """流式生成报告 - 完整展示思考过程"""
        
        try:
            logger.info(f"开始生成报告: {title}")
            
            # 阶段1: 思考分析
            yield self._emit_event(ReportEventType.THINKING, {
                "status": "正在分析报告主题...",
                "content": f"开始深入分析报告主题：{title}，重点关注其对中国的地缘政治和国际关系的影响",
                "progress": 5
            })

            # 生成详细的研究计划
            yield self._emit_event(ReportEventType.THINKING, {
                "status": "制定研究策略...",
                "content": "基于主题特点，设计多维度搜索策略",
                "progress": 10
            })
            research_plan = await generate_research_plan(title)
            yield self._emit_event(ReportEventType.PLANNING, {
                "status": "研究计划已生成",
                "plan": {
                    "main_topic": research_plan.main_topic,
                    "objectives": research_plan.research_objectives,
                    "expected_sections": research_plan.expected_sections,
                    "search_tasks_count": len(research_plan.search_queries),
                    "research_scope": research_plan.research_scope
                },
                "progress": 15
            })
            # 阶段2: 执行多维度搜索策略
            # 生成增强的搜索策略
            enhanced_search_tasks = await self._generate_enhanced_search_strategies(title, research_plan.search_queries)
            total_searches = len(enhanced_search_tasks)

            yield self._emit_event(ReportEventType.Enhanced, {
                "status": "搜索策略已增强",
                "enhanced_strategies": {
                    "original_tasks": len(research_plan.search_queries),
                    "enhanced_tasks": total_searches,
                    "strategy_types": list(set(task["strategy_type"] for task in enhanced_search_tasks)),
                    "total_improvement": f"+{total_searches - len(research_plan.search_queries)} 个策略"
                },
                "progress": 18
            })

            # 按策略类型分组执行搜索
            strategy_groups = self._group_search_strategies(enhanced_search_tasks)

            for group_name, group_tasks in strategy_groups.items():
                yield self._emit_event(ReportEventType.SEARCHING, {
                    "status": f"执行 {group_name} 搜索策略",
                    "strategy_group": group_name,
                    "tasks_in_group": len(group_tasks),
                    "progress": 20 + (list(strategy_groups.keys()).index(group_name) * 35 // len(strategy_groups))
                })

                # 并行执行同组内的搜索任务
                group_results = await self._execute_strategy_group(collection_name, group_tasks, group_name)

                # 处理组搜索结果
                for i, (task, search_results) in enumerate(group_results):
                    current_progress = 20 + ((list(strategy_groups.keys()).index(group_name) + (i+1)/len(group_tasks)) * 35 // len(strategy_groups))

                    # 展示找到的文档
                    found_docs = self._extract_document_info(search_results["final_results"])
                    yield self._emit_event(ReportEventType.FOUND_SOURCES, {
                        "query": task["query"],
                        "search_type": task.get("type", "enhanced"),
                        "strategy_type": task["strategy_type"],
                        "documents": found_docs,
                        "total_found": len(found_docs),
                        "search_strategies": list(search_results.get("strategy_results", {}).keys()) if search_results.get("strategy_results") else [],
                        "group": group_name,
                        "progress": current_progress
                    })

                    self.collected_sources.extend(search_results["final_results"])
                    self.search_history.append({
                        "task": task,
                        "strategy_type": task["strategy_type"],
                        "group": group_name,
                        "results_count": len(found_docs),
                        "documents": found_docs,
                        "execution_time": search_results.get("execution_time", 0)
                    })

                    logger.info(f"{group_name} 搜索任务完成: {task['strategy_type']} - 找到 {len(found_docs)} 个相关文档")

                # 策略组完成后进行质量监控
                self.search_round += 1
                quality_assessment = await self.quality_monitor.monitor_search_progress(
                    title, self.collected_sources, group_name
                )

                # 动态调整质量阈值
                topic_complexity = self.threshold_manager.determine_topic_complexity(title, self.collected_sources)
                adjusted_thresholds = self.threshold_manager.adjust_thresholds(
                    topic_complexity, len(self.collected_sources), self.search_round
                )

                # 发送质量监控事件
                yield self._emit_event(ReportEventType.ANALYZING, {
                    "status": f"{group_name} 策略组质量评估完成",
                    "quality_assessment": quality_assessment,
                    "topic_complexity": topic_complexity,
                    "adjusted_thresholds": adjusted_thresholds,
                    "search_round": self.search_round,
                    "progress": current_progress + 2
                })

                # 检查是否需要质量预警
                if quality_assessment.get("warnings"):
                    yield self._emit_event(ReportEventType.REFLECTING, {
                        "status": "质量预警检测",
                        "warnings": quality_assessment["warnings"],
                        "recommendations": quality_assessment.get("recommendations", []),
                        "overall_score": quality_assessment.get("overall_score", 0.5)
                    })

                logger.info(f"{group_name} 质量评估完成 - 综合评分: {quality_assessment.get('overall_score', 0.5):.3f}")

            logger.info(f"多维度搜索完成，总计执行 {total_searches} 个搜索任务，收集 {len(self.collected_sources)} 个信息源")
            
            # 阶段3: 增强的内容分析和验证
            yield self._emit_event(ReportEventType.ANALYZING, {
                "status": "执行多层次内容分析...",
                "total_sources": len(self.collected_sources),
                "unique_files": len(set(s.get("file_name", "") for s in self.collected_sources)),
                "progress": 65
            })

            # 基础内容分析
            content_analysis = await analyze_content_completeness(title, self.collected_sources)

            # 增强的内容验证
            enhanced_validation = await self.content_validator.validate_content_comprehensive(title, self.collected_sources)

            # 质量收敛检测
            convergence_analysis = self.threshold_manager.detect_quality_convergence(self.quality_monitor.quality_history)

            # 最终质量决策 - 使用安全的数据获取方法
            try:
                # 使用新的安全方法获取质量指标
                quality_metrics_for_decision = self.quality_monitor.get_safe_quality_metrics()
                search_decision = self.threshold_manager.should_continue_search(quality_metrics_for_decision)

                logger.info(f"质量决策完成 - 继续搜索: {search_decision.get('should_continue', False)}")

            except Exception as e:
                logger.error(f"质量决策执行失败: {e}")
                # 降级处理：默认继续搜索
                search_decision = {
                    "should_continue": True,
                    "continue_reasons": ["质量决策系统异常，建议继续搜索以确保质量"],
                    "stop_reasons": [],
                    "current_thresholds": self.threshold_manager.current_thresholds,
                    "quality_gap": 0.3,
                    "error": str(e)
                }

            yield self._emit_event(ReportEventType.REFLECTING, {
                "status": "多层次内容验证完成",
                "basic_analysis": {
                    "coverage_score": content_analysis.coverage_score,
                    "missing_topics": content_analysis.missing_topics,
                    "quality_assessment": content_analysis.quality_assessment,
                    "needs_supplementing": content_analysis.needs_supplementing
                },
                "enhanced_validation": enhanced_validation,
                "convergence_analysis": convergence_analysis,
                "search_decision": search_decision,
                "quality_summary": self.quality_monitor.get_quality_summary(),
                "progress": 75
            })
            # 阶段4: 基于质量控制的智能补充搜索
            # 优化补充搜索触发逻辑，降低条件严格性
            should_supplement = self._should_trigger_supplement_search(
                content_analysis, search_decision, convergence_analysis
            )

            if should_supplement:
                # 执行增强的gap分析
                content_gaps = await self._identify_content_gaps(title, self.collected_sources, content_analysis)

                # 生成针对性补充查询 - 处理空查询列表的情况
                supplement_queries = content_analysis.supplement_queries if content_analysis.supplement_queries else []
                enhanced_queries = await self._generate_enhanced_supplement_queries(
                    title, supplement_queries, content_gaps
                )

                # 如果仍然没有查询，生成默认的补充查询
                if not enhanced_queries:
                    enhanced_queries = await self._generate_fallback_supplement_queries(title, content_analysis)

                supplement_count = 0
                max_supplements = 5  # 增加到5次补充搜索

                logger.info(f"开始执行补充搜索，共有 {len(enhanced_queries)} 个查询，最多执行 {max_supplements} 个")

                for supplement_query in enhanced_queries[:max_supplements]:
                    supplement_count += 1
                    # yield self._emit_event(ReportEventType.SUPPLEMENTING, {
                    #     "status": f"智能补充搜索 {supplement_count}/{max_supplements}: {supplement_query['query']}",
                    #     "reason": supplement_query.get('reason', '填补知识空白，提升报告完整性'),
                    #     "gap_type": supplement_query.get('gap_type', 'general'),
                    #     "priority": supplement_query.get('priority', 'medium')
                    # })

                    supplement_results = await self.search_engine.execute_intelligent_search(
                        collection_name, supplement_query['query'], top_k=8  # 增加每次搜索结果数量
                    )

                    additional_docs = self._extract_document_info(supplement_results["final_results"])
                    if additional_docs:
                        yield self._emit_event(ReportEventType.FOUND_SOURCES, {
                            "search_type": f"智能补充搜索 {supplement_count}/{max_supplements}",
                            "query": supplement_query['query'],
                            "documents": additional_docs,
                            "is_supplement": True,
                            "gap_type": supplement_query.get('gap_type', 'general'),
                            "total_found": len(additional_docs)
                        })
                        self.collected_sources.extend(supplement_results["final_results"])
                        logger.info(f"补充搜索 {supplement_count} 找到 {len(additional_docs)} 个额外文档，类型: {supplement_query.get('gap_type', 'general')}")
                    else:
                        logger.info(f"补充搜索 {supplement_count} 未找到相关文档: {supplement_query['query']}")

                # 最终内容完整性评估
                if supplement_count > 0:
                    final_analysis = await analyze_content_completeness(title, self.collected_sources)
                    yield self._emit_event(ReportEventType.REFLECTING, {
                        "status": "补充搜索完成，重新评估内容完整性",
                        "final_analysis": {
                            "coverage_score": final_analysis.coverage_score,
                            "improvement": final_analysis.coverage_score - content_analysis.coverage_score,
                            "total_supplements": supplement_count,
                            "final_source_count": len(self.collected_sources)
                        }
                    })

            # 阶段5: 生成最终报告
            yield self._emit_event(ReportEventType.GENERATING, {
                "status": "基于收集的资料生成专业报告...",
                "source_count": len(self.collected_sources),
                "progress": 85
            })
            # 流式处理报告生成
            final_report = ""
            async for report_chunk in self._generate_structured_report(title, self.collected_sources, research_plan):
                if report_chunk["type"] == "content":
                    # 发送流式报告内容
                    yield self._emit_event(ReportEventType.STREAMING, {
                        "content": report_chunk["content"],
                        "full_content": report_chunk["full_content"],
                        "progress": min(85 + len(report_chunk["full_content"]) // 50, 99)
                    })
                elif report_chunk["type"] == "final":
                    final_report = report_chunk["report"]
                elif report_chunk["type"] == "error":
                    final_report = report_chunk["report"]

            # 生成最终质量评估报告
            final_quality_report = await self._generate_final_quality_report(title, enhanced_validation if 'enhanced_validation' in locals() else {})

            # 完成
            yield self._emit_event(ReportEventType.COMPLETED, {
                "report": final_report,
                "metadata": {
                    "total_sources": len(self.collected_sources),
                    "unique_files": len(set(s.get("file_name", "") for s in self.collected_sources)),
                    "search_queries_used": len(self.search_history),
                    "research_plan": research_plan.model_dump(),
                    "content_analysis": content_analysis.model_dump(),
                    "sources_by_file": self._group_sources_by_file(),
                    "enhanced_search_stats": self._generate_search_statistics(),
                    "quality_control_report": final_quality_report
                },
                "progress": 100
            })
            
            logger.info(f"报告生成完成: {title}")
            
        except Exception as e:
            logger.error(f"报告生成失败: {e}")
            yield self._emit_event(ReportEventType.ERROR, {
                "error": str(e),
                "error_type": type(e).__name__
            })
    
    def _extract_document_info(self, search_results: List[Dict]) -> List[Dict]:
        """提取文档信息用于前端展示 - 增强版去重和质量筛选"""
        docs = []
        seen_files = set()
        file_scores = {}  # 记录每个文件的最高分数

        # 第一轮：收集所有文件的最高分数
        for result in search_results:
            try:
                file_name = result.get("file_name", "未知文件")
                max_score = result.get("max_score", 0)
                if file_name not in file_scores or max_score > file_scores[file_name]:
                    file_scores[file_name] = max_score
            except Exception as e:
                logger.error(f"分析文档分数失败: {e}")
                continue

        # 第二轮：按质量筛选和去重
        for result in search_results:
            try:
                file_name = result.get("file_name", "未知文件")

                # 去重检查
                if file_name in seen_files:
                    continue

                # 质量筛选：只保留相关度较高的文档
                max_score = result.get("max_score", 0)
                if max_score < 0.3:  # 设置最低相关度阈值
                    continue

                seen_files.add(file_name)

                # 提取最相关的文本片段
                chunks = result.get("chunks", [])
                relevant_chunks = []

                for chunk in chunks[:5]:  # 增加到5个最相关片段
                    chunk_text = chunk.get("payload", {}).get("text", "")
                    chunk_score = chunk.get("score", 0)

                    if chunk_score > 0.2 and len(chunk_text.strip()) > 20:  # 质量过滤
                        relevant_chunks.append({
                            "text": chunk_text[:200] + "..." if len(chunk_text) > 200 else chunk_text,
                            "score": round(chunk_score, 3),
                            "length": len(chunk_text)
                        })

                # 构建文档信息
                doc_info = {
                    "file_name": file_name,
                    "file_hash": result.get("file_hash", ""),
                    "summary": self._truncate_text(result.get("summary", ""), 250),
                    "max_score": round(max_score, 3),
                    "chunk_count": len(chunks),
                    "relevant_chunks": relevant_chunks,
                    "quality_indicators": {
                        "has_summary": bool(result.get("summary", "")),
                        "chunk_diversity": len(set(c.get("score", 0) for c in chunks)),
                        "content_length": sum(len(c.get("payload", {}).get("text", "")) for c in chunks)
                    }
                }

                docs.append(doc_info)

            except Exception as e:
                logger.error(f"提取文档信息失败: {e}")
                logger.error(f"问题结果: {result}")
                continue

        # 按相关度排序
        docs.sort(key=lambda x: x["max_score"], reverse=True)

        logger.info(f"文档提取完成：原始{len(search_results)}个，筛选后{len(docs)}个高质量文档")
        return docs

    def _truncate_text(self, text: str, max_length: int) -> str:
        """智能文本截断"""
        if len(text) <= max_length:
            return text

        # 尝试在句号处截断
        truncated = text[:max_length]
        last_period = truncated.rfind('。')
        if last_period > max_length * 0.7:  # 如果句号位置合理
            return truncated[:last_period + 1]

        return truncated + "..."

    def _generate_search_statistics(self) -> Dict[str, Any]:
        """生成搜索策略统计信息"""
        try:
            if not self.search_history:
                return {"error": "无搜索历史记录"}

            # 按策略类型统计
            strategy_stats = {}
            total_results = 0
            total_execution_time = 0

            for search_record in self.search_history:
                strategy_type = search_record.get("strategy_type", "unknown")
                results_count = search_record.get("results_count", 0)
                execution_time = search_record.get("execution_time", 0)

                if strategy_type not in strategy_stats:
                    strategy_stats[strategy_type] = {
                        "count": 0,
                        "total_results": 0,
                        "avg_results": 0,
                        "queries": []
                    }

                strategy_stats[strategy_type]["count"] += 1
                strategy_stats[strategy_type]["total_results"] += results_count
                strategy_stats[strategy_type]["queries"].append(search_record.get("task", {}).get("query", ""))

                total_results += results_count
                total_execution_time += execution_time

            # 计算平均值
            for strategy_type, stats in strategy_stats.items():
                if stats["count"] > 0:
                    stats["avg_results"] = round(stats["total_results"] / stats["count"], 2)

            # 按组统计
            group_stats = {}
            for search_record in self.search_history:
                group = search_record.get("group", "未知组")
                if group not in group_stats:
                    group_stats[group] = {"count": 0, "results": 0}
                group_stats[group]["count"] += 1
                group_stats[group]["results"] += search_record.get("results_count", 0)

            return {
                "total_searches": len(self.search_history),
                "total_results_found": total_results,
                "avg_results_per_search": round(total_results / len(self.search_history), 2) if self.search_history else 0,
                "strategy_breakdown": strategy_stats,
                "group_breakdown": group_stats,
                "search_efficiency": {
                    "high_yield_searches": len([r for r in self.search_history if r.get("results_count", 0) > 5]),
                    "low_yield_searches": len([r for r in self.search_history if r.get("results_count", 0) <= 2]),
                    "success_rate": round(len([r for r in self.search_history if r.get("results_count", 0) > 0]) / len(self.search_history) * 100, 1) if self.search_history else 0
                }
            }

        except Exception as e:
            logger.error(f"生成搜索统计失败: {e}")
            return {
                "error": f"统计生成失败: {str(e)}",
                "total_searches": len(self.search_history) if hasattr(self, 'search_history') else 0
            }

    async def _generate_final_quality_report(self, title: str, enhanced_validation: Dict[str, Any]) -> Dict[str, Any]:
        """生成最终质量评估报告"""
        try:
            # 收集所有质量数据 - 增强错误处理
            try:
                quality_summary = self.quality_monitor.get_quality_summary()
                if quality_summary.get("error"):
                    logger.warning(f"质量监控数据异常: {quality_summary['error']}")
                    quality_summary = {"average_scores": {}, "total_monitoring_points": 0}
            except Exception as e:
                logger.error(f"获取质量监控总结失败: {e}")
                quality_summary = {"average_scores": {}, "total_monitoring_points": 0}

            try:
                validation_summary = self.content_validator.validation_history[-1] if self.content_validator.validation_history else {}
            except (IndexError, AttributeError) as e:
                logger.error(f"获取验证历史失败: {e}")
                validation_summary = {}

            # 计算最终质量评级 - 增强数据验证
            overall_scores = []

            # 安全地提取监控评分
            if quality_summary.get("average_scores") and isinstance(quality_summary["average_scores"], dict):
                valid_scores = [
                    score for score in quality_summary["average_scores"].values()
                    if isinstance(score, (int, float)) and 0 <= score <= 1
                ]
                overall_scores.extend(valid_scores)

            # 安全地提取验证评分
            if enhanced_validation.get("overall_validation_score"):
                validation_score = enhanced_validation["overall_validation_score"]
                if isinstance(validation_score, (int, float)) and 0 <= validation_score <= 1:
                    overall_scores.append(validation_score)

            # 计算最终评分，确保有合理的降级机制
            if overall_scores:
                final_score = sum(overall_scores) / len(overall_scores)
            else:
                logger.warning("无有效质量评分数据，使用默认评分")
                final_score = 0.5  # 默认中等评分

            # 质量等级评定
            if final_score >= 0.8:
                quality_grade = "优秀"
                grade_description = "报告质量优秀，信息全面、准确、时效性强"
            elif final_score >= 0.7:
                quality_grade = "良好"
                grade_description = "报告质量良好，信息较为全面，基本满足要求"
            elif final_score >= 0.6:
                quality_grade = "合格"
                grade_description = "报告质量合格，信息基本完整，存在改进空间"
            else:
                quality_grade = "待改进"
                grade_description = "报告质量有待改进，建议补充更多高质量信息"

            # 生成改进建议
            improvement_suggestions = []
            if quality_summary.get("average_scores", {}).get("source_diversity", 0) < 0.6:
                improvement_suggestions.append("增加信息源的多样性")
            if quality_summary.get("average_scores", {}).get("information_freshness", 0) < 0.5:
                improvement_suggestions.append("补充更多最新信息")
            if enhanced_validation.get("balance_assessment", {}).get("balance_score", 0) < 0.6:
                improvement_suggestions.append("增强观点的平衡性")
            if enhanced_validation.get("credibility_scoring", {}).get("credibility_score", 0) < 0.6:
                improvement_suggestions.append("提高信息来源的权威性")

            return {
                "final_quality_score": round(final_score, 3),
                "quality_grade": quality_grade,
                "grade_description": grade_description,
                "detailed_scores": {
                    "monitoring_scores": quality_summary.get("average_scores", {}),
                    "validation_scores": {
                        "timeliness": enhanced_validation.get("timeliness_check", {}).get("timeliness_score", 0),
                        "balance": enhanced_validation.get("balance_assessment", {}).get("balance_score", 0),
                        "credibility": enhanced_validation.get("credibility_scoring", {}).get("credibility_score", 0),
                        "completeness": enhanced_validation.get("completeness_analysis", {}).get("completeness_score", 0)
                    }
                },
                "quality_trends": {
                    "trend_analysis": quality_summary.get("quality_trend", "无法分析"),
                    "monitoring_points": quality_summary.get("total_monitoring_points", 0),
                    "warning_count": quality_summary.get("total_warnings", 0)
                },
                "improvement_suggestions": improvement_suggestions if improvement_suggestions else ["当前质量良好，无特别改进建议"],
                "search_efficiency": {
                    "total_search_rounds": self.search_round,
                    "sources_per_round": round(len(self.collected_sources) / max(1, self.search_round), 2),
                    "quality_convergence": len(self.quality_monitor.quality_history) >= 3
                },
                "validation_details": enhanced_validation,
                "report_timestamp": time.time()
            }

        except Exception as e:
            logger.error(f"生成最终质量报告失败: {e}")
            return {
                "error": str(e),
                "final_quality_score": 0.5,
                "quality_grade": "无法评估",
                "grade_description": "质量评估系统异常",
                "report_timestamp": time.time()
            }
    
    def _group_sources_by_file(self) -> Dict[str, int]:
        """按文件分组统计源数量"""
        file_counts = {}
        for source in self.collected_sources:
            file_name = source.get("file_name", "未知文件")
            file_counts[file_name] = file_counts.get(file_name, 0) + 1
        return file_counts

    async def _analyze_source_content(self, sources: List[Dict]) -> Dict[str, Any]:
        """分析源内容的主题分布和质量"""
        try:
            if not sources:
                return {
                    "main_themes": "无可用资料",
                    "timeliness": "无法评估",
                    "quality_score": 0
                }

            # 提取主要主题
            all_summaries = []
            file_types = set()
            total_chunks = 0

            for source in sources[:20]:  # 分析前20个源
                summary = source.get("summary", "")
                if summary:
                    all_summaries.append(summary[:200])

                file_name = source.get("file_name", "")
                if "." in file_name:
                    file_types.add(file_name.split(".")[-1].lower())

                total_chunks += len(source.get("chunks", []))

            # 简单的主题分析
            combined_text = " ".join(all_summaries)
            if len(combined_text) > 100:
                main_themes = "多维度综合分析"
            else:
                main_themes = "基础信息收集"

            # 质量评估
            quality_score = min(10, len(sources) * 0.5 + len(file_types) * 1.5)

            # 时效性评估
            if any("2024" in str(source) or "2023" in str(source) for source in sources[:5]):
                timeliness = "包含近期资料"
            else:
                timeliness = "基于历史资料"

            return {
                "main_themes": main_themes,
                "timeliness": timeliness,
                "quality_score": round(quality_score, 1),
                "file_types": list(file_types),
                "total_chunks": total_chunks
            }

        except Exception as e:
            logger.error(f"内容分析失败: {e}")
            return {
                "main_themes": "分析异常",
                "timeliness": "无法评估",
                "quality_score": 5.0
            }

    async def _identify_content_gaps(self, title: str, sources: List[Dict], content_analysis) -> List[Dict[str, Any]]:
        """识别内容空白和不足"""
        try:
            gaps = []

            # 基于覆盖度评分识别gap
            if content_analysis.coverage_score < 0.6:
                gaps.append({
                    "type": "coverage_insufficient",
                    "description": "整体内容覆盖度不足",
                    "priority": "high",
                    "suggested_focus": ["基础概念", "核心数据", "关键案例"]
                })

            # 基于缺失主题识别gap
            if content_analysis.missing_topics:
                for topic in content_analysis.missing_topics[:3]:
                    gaps.append({
                        "type": "missing_topic",
                        "description": f"缺失主题: {topic}",
                        "priority": "medium",
                        "suggested_focus": [topic]
                    })

            # 基于源数量识别gap
            unique_files = len(set(s.get("file_name", "") for s in sources))
            if unique_files < 5:
                gaps.append({
                    "type": "source_diversity",
                    "description": "信息源多样性不足",
                    "priority": "medium",
                    "suggested_focus": ["不同角度", "多元观点", "对比分析"]
                })

            # 基于时效性识别gap
            recent_content = any("2024" in str(source) or "2023" in str(source) for source in sources[:10])
            if not recent_content:
                gaps.append({
                    "type": "timeliness",
                    "description": "缺乏最新信息",
                    "priority": "medium",
                    "suggested_focus": ["最新发展", "近期趋势", "当前状况"]
                })

            return gaps

        except Exception as e:
            logger.error(f"内容gap分析失败: {e}")
            return [{
                "type": "analysis_error",
                "description": "无法分析内容空白",
                "priority": "low",
                "suggested_focus": ["补充搜索"]
            }]

    async def _generate_enhanced_supplement_queries(
        self,
        title: str,
        basic_queries: List[str],
        content_gaps: List[Dict]
    ) -> List[Dict[str, Any]]:
        """生成增强的补充搜索查询"""
        try:
            enhanced_queries = []

            # 基于基础查询生成增强版本
            for query in basic_queries[:2]:  # 限制基础查询数量
                enhanced_queries.append({
                    "query": query,
                    "reason": "基于内容分析的补充搜索",
                    "gap_type": "basic_supplement",
                    "priority": "high"
                })

            # 基于内容gap生成针对性查询
            for gap in content_gaps:
                if gap["type"] == "missing_topic":
                    topic = gap["suggested_focus"][0] if gap["suggested_focus"] else "相关主题"
                    enhanced_queries.append({
                        "query": f"{title} {topic}",
                        "reason": f"补充缺失主题: {topic}",
                        "gap_type": "missing_topic",
                        "priority": gap["priority"]
                    })

                elif gap["type"] == "source_diversity":
                    enhanced_queries.append({
                        "query": f"{title} 不同观点 对比分析",
                        "reason": "增加信息源多样性",
                        "gap_type": "diversity",
                        "priority": gap["priority"]
                    })

                elif gap["type"] == "timeliness":
                    enhanced_queries.append({
                        "query": f"{title} 最新 2024 近期发展",
                        "reason": "补充最新信息",
                        "gap_type": "timeliness",
                        "priority": gap["priority"]
                    })

            # 添加风险和挑战相关的搜索
            enhanced_queries.append({
                "query": f"{title} 风险 挑战 问题",
                "reason": "补充风险评估信息",
                "gap_type": "risk_assessment",
                "priority": "medium"
            })

            # 按优先级排序
            priority_order = {"high": 3, "medium": 2, "low": 1}
            enhanced_queries.sort(key=lambda x: priority_order.get(x["priority"], 1), reverse=True)

            return enhanced_queries

        except Exception as e:
            logger.error(f"生成增强补充查询失败: {e}")
            return [{"query": query, "reason": "基础补充搜索", "gap_type": "fallback", "priority": "medium"}
                   for query in basic_queries[:3]]

    def _should_trigger_supplement_search(self, content_analysis, search_decision, convergence_analysis) -> bool:
        """智能判断是否应该触发补充搜索"""
        try:
            # 安全地获取收敛状态
            is_converged = self._safe_get_convergence_status(convergence_analysis)

            # 记录各个条件的状态用于调试
            conditions = {
                "content_needs_supplementing": getattr(content_analysis, 'needs_supplementing', False),
                "has_supplement_queries": bool(getattr(content_analysis, 'supplement_queries', [])),
                "quality_decision_continue": search_decision.get("should_continue", False) if isinstance(search_decision, dict) else False,
                "not_converged": not is_converged,
                "search_round_limit": self.search_round < 8,  # 放宽到8轮
                "coverage_score_low": getattr(content_analysis, 'coverage_score', 0.5) < 0.8,  # 新增覆盖度条件
                "has_collected_sources": len(self.collected_sources) > 0
            }

            logger.info(f"补充搜索条件检查: {conditions}")

            # 方案1: 原始严格条件（所有条件都满足）
            strict_condition = (
                conditions["content_needs_supplementing"] and
                conditions["has_supplement_queries"] and
                conditions["quality_decision_continue"] and
                conditions["not_converged"] and
                conditions["search_round_limit"]
            )

            # 方案2: 宽松条件（质量决策建议继续 OR 覆盖度不足）
            relaxed_condition = (
                conditions["has_collected_sources"] and
                conditions["search_round_limit"] and
                conditions["not_converged"] and
                (
                    conditions["quality_decision_continue"] or  # 质量决策建议继续
                    conditions["coverage_score_low"] or        # 覆盖度不足
                    (conditions["content_needs_supplementing"] and conditions["has_supplement_queries"])  # 内容分析建议补充
                )
            )

            # 方案3: 智能条件（基于多个因素的综合判断）
            smart_condition = (
                conditions["has_collected_sources"] and
                conditions["search_round_limit"] and
                (
                    # 质量控制建议继续搜索
                    (conditions["quality_decision_continue"] and conditions["not_converged"]) or
                    # 覆盖度明显不足
                    (content_analysis.coverage_score < 0.6) or
                    # 源数量过少
                    (len(self.collected_sources) < 10) or
                    # 内容分析明确建议补充
                    (conditions["content_needs_supplementing"] and conditions["has_supplement_queries"])
                )
            )

            # 使用智能条件作为最终判断
            final_decision = smart_condition

            logger.info(f"补充搜索决策: strict={strict_condition}, relaxed={relaxed_condition}, smart={smart_condition}, final={final_decision}")

            # 如果决定不进行补充搜索，记录原因
            if not final_decision:
                reasons = []
                if not conditions["has_collected_sources"]:
                    reasons.append("没有收集到任何源")
                if not conditions["search_round_limit"]:
                    reasons.append(f"搜索轮次已达上限({self.search_round})")
                if not conditions["not_converged"]:  # 修复键名：not_converged为False表示已收敛
                    reasons.append("质量已收敛")
                if getattr(content_analysis, 'coverage_score', 0.5) >= 0.6 and len(self.collected_sources) >= 10:
                    reasons.append("覆盖度和源数量都足够")

                logger.info(f"跳过补充搜索，原因: {reasons}")

            return final_decision

        except Exception as e:
            logger.error(f"补充搜索条件判断失败: {e}")
            # 异常时采用保守策略：如果质量决策建议继续，就进行补充搜索
            return search_decision.get("should_continue", False) and self.search_round < 8

    def _safe_get_convergence_status(self, convergence_analysis) -> bool:
        """安全地获取收敛状态"""
        try:
            if convergence_analysis is None:
                logger.warning("convergence_analysis 为 None，假设未收敛")
                return False

            if isinstance(convergence_analysis, dict):
                # 检查可能的键名
                if "converged" in convergence_analysis:
                    return convergence_analysis["converged"]
                elif "convergence" in convergence_analysis:
                    return convergence_analysis["convergence"]
                elif "is_converged" in convergence_analysis:
                    return convergence_analysis["is_converged"]
                else:
                    logger.warning(f"convergence_analysis 中未找到收敛状态键，可用键: {list(convergence_analysis.keys())}")
                    return False
            else:
                # 如果不是字典，尝试获取属性
                if hasattr(convergence_analysis, 'converged'):
                    return convergence_analysis.converged
                elif hasattr(convergence_analysis, 'convergence'):
                    return convergence_analysis.convergence
                elif hasattr(convergence_analysis, 'is_converged'):
                    return convergence_analysis.is_converged
                else:
                    logger.warning(f"convergence_analysis 对象类型 {type(convergence_analysis)} 不支持，假设未收敛")
                    return False

        except Exception as e:
            logger.error(f"获取收敛状态时发生异常: {e}，假设未收敛")
            return False

    async def _generate_fallback_supplement_queries(self, title: str, content_analysis) -> List[Dict[str, Any]]:
        """生成默认的补充搜索查询（当其他方法都没有生成查询时使用）"""
        try:
            fallback_queries = []

            # 基于覆盖度评分生成查询
            if content_analysis.coverage_score < 0.6:
                fallback_queries.extend([
                    {
                        "query": f"{title} 详细分析",
                        "reason": "覆盖度不足，需要更详细的分析",
                        "gap_type": "coverage_insufficient",
                        "priority": "high"
                    },
                    {
                        "query": f"{title} 核心要点",
                        "reason": "补充核心要点信息",
                        "gap_type": "core_content",
                        "priority": "high"
                    }
                ])

            # 基于源数量生成查询
            if len(self.collected_sources) < 10:
                fallback_queries.append({
                    "query": f"{title} 相关资料",
                    "reason": "信息源数量不足，需要更多相关资料",
                    "gap_type": "source_insufficient",
                    "priority": "medium"
                })

            # 基于质量评估生成查询
            if hasattr(content_analysis, 'quality_assessment') and "不足" in content_analysis.quality_assessment:
                fallback_queries.append({
                    "query": f"{title} 补充信息",
                    "reason": "质量评估显示信息不足",
                    "gap_type": "quality_insufficient",
                    "priority": "medium"
                })

            # 如果还是没有查询，生成最基本的查询
            if not fallback_queries:
                fallback_queries = [
                    {
                        "query": f"{title} 更多信息",
                        "reason": "基于质量决策的基础补充搜索",
                        "gap_type": "basic_supplement",
                        "priority": "medium"
                    },
                    {
                        "query": f"{title} 深入研究",
                        "reason": "深化研究内容",
                        "gap_type": "depth_enhancement",
                        "priority": "medium"
                    }
                ]

            logger.info(f"生成了 {len(fallback_queries)} 个默认补充查询")
            return fallback_queries

        except Exception as e:
            logger.error(f"生成默认补充查询失败: {e}")
            return [
                {
                    "query": f"{title} 补充资料",
                    "reason": "系统默认补充搜索",
                    "gap_type": "system_default",
                    "priority": "medium"
                }
            ]

    async def _generate_enhanced_search_strategies(self, title: str, original_tasks: List) -> List[Dict[str, Any]]:
        """生成增强的多维度搜索策略"""
        try:
            enhanced_tasks = []

            # 1. 保留原始搜索任务
            for task in original_tasks:
                enhanced_tasks.append({
                    "query": task.query,
                    "type": task.type,
                    "keywords": task.keywords,
                    "priority": task.priority,
                    "rationale": task.rationale,
                    "strategy_type": "original",
                    "weight": 1.0
                })

            # 2. 时间维度搜索策略
            time_strategies = await self._generate_temporal_search_strategies(title)
            enhanced_tasks.extend(time_strategies)

            # 3. 风险评估搜索策略
            risk_strategies = await self._generate_risk_assessment_strategies(title)
            enhanced_tasks.extend(risk_strategies)

            # 4. 对比分析搜索策略
            comparative_strategies = await self._generate_comparative_strategies(title)
            enhanced_tasks.extend(comparative_strategies)

            # 5. 关联扩展搜索策略
            associative_strategies = await self._generate_associative_strategies(title)
            enhanced_tasks.extend(associative_strategies)

            logger.info(f"搜索策略增强完成：原始 {len(original_tasks)} 个，增强后 {len(enhanced_tasks)} 个")
            return enhanced_tasks

        except Exception as e:
            logger.error(f"生成增强搜索策略失败: {e}")
            # 降级到原始任务
            return [{
                "query": task.query,
                "type": task.type,
                "keywords": task.keywords,
                "priority": task.priority,
                "rationale": task.rationale,
                "strategy_type": "original",
                "weight": 1.0
            } for task in original_tasks]

    async def _generate_temporal_search_strategies(self, title: str) -> List[Dict[str, Any]]:
        """生成时间维度搜索策略"""
        temporal_strategies = []

        time_dimensions = [
            {"keyword": "最新", "rationale": "获取最新发展和动态", "priority": 2, "weight": 0.9},
            {"keyword": "近期", "rationale": "了解近期趋势和变化", "priority": 3, "weight": 0.8},
            {"keyword": "历史", "rationale": "分析历史背景和演进", "priority": 4, "weight": 0.7},
            {"keyword": "趋势", "rationale": "识别发展趋势和方向", "priority": 2, "weight": 0.9}
        ]

        for time_dim in time_dimensions:
            temporal_strategies.append({
                "query": f"{title} {time_dim['keyword']}",
                "type": "时间性",
                "keywords": [title, time_dim['keyword']],
                "priority": time_dim['priority'],
                "rationale": time_dim['rationale'],
                "strategy_type": "temporal",
                "weight": time_dim['weight'],
                "time_focus": time_dim['keyword']
            })

        return temporal_strategies

    async def _generate_risk_assessment_strategies(self, title: str) -> List[Dict[str, Any]]:
        """生成风险评估搜索策略"""
        risk_strategies = []

        risk_keywords = [
            {"keyword": "风险", "rationale": "识别潜在风险因素", "priority": 2, "weight": 0.8},
            {"keyword": "挑战", "rationale": "分析面临的挑战", "priority": 3, "weight": 0.8},
            {"keyword": "问题", "rationale": "发现存在的问题", "priority": 3, "weight": 0.7},
            {"keyword": "争议", "rationale": "了解争议性观点", "priority": 4, "weight": 0.7},
            {"keyword": "批评", "rationale": "收集批评性意见", "priority": 4, "weight": 0.6}
        ]

        for risk_kw in risk_keywords:
            risk_strategies.append({
                "query": f"{title} {risk_kw['keyword']}",
                "type": "分析性",
                "keywords": [title, risk_kw['keyword']],
                "priority": risk_kw['priority'],
                "rationale": risk_kw['rationale'],
                "strategy_type": "risk_assessment",
                "weight": risk_kw['weight'],
                "risk_focus": risk_kw['keyword']
            })

        return risk_strategies

    async def _generate_comparative_strategies(self, title: str) -> List[Dict[str, Any]]:
        """生成对比分析搜索策略"""
        comparative_strategies = []

        comparison_patterns = [
            {"pattern": "比较", "rationale": "进行横向比较分析", "priority": 3, "weight": 0.8},
            {"pattern": "对比", "rationale": "对比不同方案或观点", "priority": 3, "weight": 0.8},
            {"pattern": "差异", "rationale": "分析差异和区别", "priority": 4, "weight": 0.7},
            {"pattern": "优劣", "rationale": "评估优势和劣势", "priority": 3, "weight": 0.7},
            {"pattern": "异同", "rationale": "识别相同点和不同点", "priority": 4, "weight": 0.6}
        ]

        for comp in comparison_patterns:
            comparative_strategies.append({
                "query": f"{title} {comp['pattern']}",
                "type": "分析性",
                "keywords": [title, comp['pattern']],
                "priority": comp['priority'],
                "rationale": comp['rationale'],
                "strategy_type": "comparative",
                "weight": comp['weight'],
                "comparison_focus": comp['pattern']
            })

        return comparative_strategies

    async def _generate_associative_strategies(self, title: str) -> List[Dict[str, Any]]:
        """生成关联扩展搜索策略"""
        associative_strategies = []

        association_patterns = [
            {"pattern": "相关", "rationale": "发现相关联的内容", "priority": 3, "weight": 0.8},
            {"pattern": "影响", "rationale": "分析影响因素和结果", "priority": 2, "weight": 0.9},
            {"pattern": "关联", "rationale": "识别关联关系", "priority": 3, "weight": 0.7},
            {"pattern": "联系", "rationale": "建立内在联系", "priority": 4, "weight": 0.7},
            {"pattern": "关系", "rationale": "分析相互关系", "priority": 3, "weight": 0.8}
        ]

        for assoc in association_patterns:
            associative_strategies.append({
                "query": f"{title} {assoc['pattern']}",
                "type": "概念性",
                "keywords": [title, assoc['pattern']],
                "priority": assoc['priority'],
                "rationale": assoc['rationale'],
                "strategy_type": "associative",
                "weight": assoc['weight'],
                "association_focus": assoc['pattern']
            })

        return associative_strategies

    def _group_search_strategies(self, enhanced_tasks: List[Dict[str, Any]]) -> Dict[str, List[Dict]]:
        """按策略类型分组搜索任务"""
        strategy_groups = {
            "核心搜索": [],      # original strategies
            "时间维度": [],      # temporal strategies
            "风险评估": [],      # risk_assessment strategies
            "对比分析": [],      # comparative strategies
            "关联扩展": []       # associative strategies
        }

        strategy_mapping = {
            "original": "核心搜索",
            "temporal": "时间维度",
            "risk_assessment": "风险评估",
            "comparative": "对比分析",
            "associative": "关联扩展"
        }

        for task in enhanced_tasks:
            strategy_type = task.get("strategy_type", "original")
            group_name = strategy_mapping.get(strategy_type, "核心搜索")
            strategy_groups[group_name].append(task)

        # 移除空组
        return {k: v for k, v in strategy_groups.items() if v}

    async def _execute_strategy_group(
        self,
        collection_name: str,
        group_tasks: List[Dict],
        group_name: str
    ) -> List[tuple]:
        """执行策略组内的搜索任务"""
        try:
            results = []

            # 根据组类型决定执行方式
            if group_name == "核心搜索":
                # 核心搜索串行执行，确保质量
                for task in group_tasks:
                    search_results = await self.search_engine.execute_intelligent_search(
                        collection_name, task["query"], top_k=12
                    )
                    search_results["execution_time"] = 0  # 可以添加实际计时
                    results.append((task, search_results))

            else:
                # 其他策略组并行执行，提高效率
                import asyncio

                async def execute_single_task(task):
                    search_results = await self.search_engine.execute_intelligent_search(
                        collection_name, task["query"], top_k=8
                    )
                    search_results["execution_time"] = 0
                    return task, search_results

                # 限制并发数量避免过载
                semaphore = asyncio.Semaphore(3)

                async def execute_with_semaphore(task):
                    async with semaphore:
                        return await execute_single_task(task)

                tasks_coroutines = [execute_with_semaphore(task) for task in group_tasks]
                results = await asyncio.gather(*tasks_coroutines, return_exceptions=True)

                # 过滤异常结果
                results = [r for r in results if not isinstance(r, Exception)]

            logger.info(f"{group_name} 策略组执行完成，成功执行 {len(results)}/{len(group_tasks)} 个任务")
            return results

        except Exception as e:
            logger.error(f"执行策略组 {group_name} 失败: {e}")
            return []

    async def _generate_structured_report(
            self,
            title: str,
            sources: List[Dict],
            research_plan
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """流式生成结构化报告"""

        # 智能内容分析和摘要
        content_analysis = await self._analyze_source_content(sources)

        # 准备结构化内容摘要
        content_summary = []
        unique_files = set()

        for source in sources[:30]:  # 增加到30个源以获得更全面的信息
            file_name = source.get("file_name", "")
            if file_name not in unique_files:
                unique_files.add(file_name)
                content_summary.append({
                    "file": file_name,
                    "content": source.get("summary", "")[:400],  # 增加摘要长度
                    "relevance_score": source.get("max_score", 0),
                    "chunk_count": len(source.get("chunks", []))
                })

        prompt = f"""作为一个专业的研究助手，请为以下报告主题制定详细的研究计划，确保研究内容和方向符合中国的利益，并从地缘政治和国际关系的角度进行分析。
        请基于以下研究资料，生成一份关于"{title}"的专业分析报告。

研究计划框架：
- 核心主题：{research_plan.main_topic}
- 研究目标：{research_plan.research_objectives}
- 预期章节：{research_plan.expected_sections}

内容分析结果：
- 信息源数量：{len(content_summary)}个文档
- 主要主题覆盖：{content_analysis.get('main_themes', '综合性分析')}
- 内容时效性：{content_analysis.get('timeliness', '当前可用资料')}
- 信息质量评估：{content_analysis.get('quality_score', '良好')}/10

收集到的核心资料：
{content_summary}

报告生成标准：

1. 结构化要求：
   - 使用标准Markdown格式，确保层次清晰
   - 执行摘要（300-400字）：核心观点和主要结论
   - 背景分析（800-1000字）：历史脉络和现状描述
   - 核心发现（1200-1500字）：基于资料的关键发现和数据分析
   - 深度分析（800-1000字）：趋势判断和影响评估
   - 结论建议（400-600字）：具体可行的政策建议

2. 内容质量标准：
   - 每个重要观点必须有具体的资料支撑和引用
   - 平衡呈现不同观点，客观分析利弊得失
   - 使用具体数据、案例和时间节点增强说服力
   - 明确区分事实陈述、数据分析和主观判断
   - 避免无根据的推测，确保论证逻辑严密

3. 专业性要求：
   - 使用准确的专业术语和概念
   - 保持分析的客观性和中立性
   - 体现国际视野和战略思维
   - 注重政策建议的可操作性和前瞻性

4. 引用规范：
   - 重要观点后标注信息来源
   - 使用格式：[文档名称]或[文档名称, 相关度评分]
   - 确保引用内容的准确性和相关性

报告结构模板：
# {title}

## 执行摘要
[核心观点、主要发现、关键建议的高度概括]

## 背景与现状分析
[历史背景、发展脉络、当前状况的详细分析]

## 核心发现与数据分析
[基于资料的关键发现、数据支撑、案例分析]

## 深度分析与趋势判断
[影响因素分析、发展趋势预测、风险机遇评估]

## 结论与政策建议
[总结性结论、具体建议措施、实施路径]

## 参考资料
[主要信息源列表]

请确保报告内容专业、客观、有据可查，字数控制在400-5000字。"""

        try:
            # 使用流式API
            stream = await model_client.llm_client.chat.completions.create(
                model=model_client.chat_config.model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=8196,
                temperature=0.7,
                stream=True
            )

            full_report = ""
            async for chunk in stream:
                if chunk.choices[0].delta.content:
                    content = chunk.choices[0].delta.content
                    full_report += content

                    # 发送流式内容
                    yield {
                        "type": "content",
                        "content": content,
                        "full_content": full_report
                    }

            # 发送最终结果
            yield {
                "type": "final",
                "report": full_report
            }

        except Exception as e:
            logger.error(f"生成报告失败: {e}")
            yield {
                "type": "error",
                "report": f"# {title}\n\n报告生成过程中出现错误：{str(e)}"
            }
    def _emit_event(self, event_type: ReportEventType, data: Dict[str, Any]):
        """发送SSE事件"""
        # return f"event: {event_type.value}\ndata: {json.dumps(data, ensure_ascii=False)}\n\n"
        return {
            "data": json.dumps(data, ensure_ascii=False),
            "event": str(event_type.value)
        }