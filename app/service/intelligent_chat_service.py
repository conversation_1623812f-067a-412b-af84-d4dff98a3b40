import json
import asyncio
from typing import List, Dict, Any, AsyncGenerator
from datetime import datetime
from pydantic import BaseModel, Field

from app.service.model_client import model_client
from app.service.multi_strategy_search_engine import multi_strategy_engine
from app.models.knowledge import ChatMessage, SourceReference
from app.utils.logger import setup_logger

logger = setup_logger(__name__)

class KnowledgeAssessment(BaseModel):
    """知识评估结果"""
    needs_knowledge_base: bool = Field(description="是否需要检索知识库")
    confidence_level: str = Field(description="置信度等级: high/medium/low")
    reasoning: str = Field(description="判断理由")
    suggested_queries: List[str] = Field(description="建议的检索查询")

class ChatAnswer(BaseModel):
    """对话回答结构"""
    answer: str = Field(description="回答内容")
    confidence_score: float = Field(description="置信度评分")
    reasoning_process: str = Field(description="推理过程")

class IntelligentChatService:
    """智能对话服务"""
    
    def __init__(self):
        self.logger = logger

    async def chat_stream(
            self,
            question: str,
            collection_name: str,
            conversation_history: List[ChatMessage] = None
    ) :
        """流式对话响应"""

        if conversation_history is None:
            conversation_history = []

        try:
            # 1. 发送思考开始事件
            yield self._emit_event("thinking", {
                "status": "正在分析问题...",
                "content": f"收到问题：{question}",
                "progress": 10
            })

            # 2. 评估是否需要检索知识库
            assessment = await self._assess_knowledge_needs(question, conversation_history)

            yield self._emit_event("assessment", {
                "status": "知识需求评估完成",
                "needs_knowledge": assessment.needs_knowledge_base,
                "confidence": assessment.confidence_level,
                "reasoning": assessment.reasoning,
                "progress": 25
            })

            sources = []
            knowledge_used = False

            # 3. 如果需要检索知识库
            if assessment.needs_knowledge_base:
                yield self._emit_event("searching", {
                    "status": "正在检索相关文档...",
                    "queries": assessment.suggested_queries,
                    "progress": 40
                })

                # 执行检索
                search_results = await self._search_knowledge_base(
                    assessment.suggested_queries,
                    collection_name
                )

                sources = self._extract_sources(search_results)
                knowledge_used = True

                yield self._emit_event("found_sources", {
                    "status": f"找到 {len(sources)} 个相关文档片段",
                    "sources": [s.model_dump() for s in sources[:3]],  # 只显示前3个
                    "progress": 60
                })

            # 4. 开始流式生成回答
            yield self._emit_event("generating", {
                "status": "正在生成回答...",
                "progress": 75
            })

            # 流式处理回答生成
            final_answer = ""
            confidence_score = 0.0
            reasoning_process = ""

            async for answer_chunk in self._generate_answer(
                    question,
                    conversation_history,
                    sources,
                    knowledge_used
            ):
                if answer_chunk["type"] == "content":
                    # 发送流式内容
                    yield self._emit_event("streaming", {
                        "content": answer_chunk["content"],
                        "full_content": answer_chunk["full_content"],
                        "progress": min(75 + len(answer_chunk["full_content"]) // 10, 95)
                    })
                elif answer_chunk["type"] == "final":
                    final_answer = answer_chunk["answer"]
                    confidence_score = answer_chunk["confidence_score"]
                    reasoning_process = answer_chunk["reasoning_process"]
                elif answer_chunk["type"] == "error":
                    final_answer = answer_chunk["answer"]
                    confidence_score = answer_chunk["confidence_score"]
                    reasoning_process = answer_chunk["reasoning_process"]

            # 5. 返回最终结果
            yield self._emit_event("completed", {
                "answer": final_answer,
                "confidence_score": confidence_score,
                "knowledge_used": knowledge_used,
                "sources": [s.model_dump() for s in sources],
                "reasoning_process": reasoning_process,
                "progress": 100
            })

        except Exception as e:
            self.logger.error(f"对话处理失败: {e}")
            yield self._emit_event("error", {
                "status": "处理失败",
                "error": str(e),
                "progress": 0
            })
    async def _assess_knowledge_needs(
        self, 
        question: str, 
        history: List[ChatMessage]
    ) -> KnowledgeAssessment:
        """评估是否需要检索知识库"""
        
        # 构建历史上下文
        history_context = ""
        if history:
            recent_history = history[-3:]  # 只取最近3轮对话
            history_context = "\n".join([
                f"{msg.role}: {msg.content}" for msg in recent_history
            ])
        
        prompt = f"""作为一个智能助手，请评估以下问题是否需要检索特定的知识库来回答。

对话历史：
{history_context}

当前问题：{question}

请判断：
1. 这个问题是否可以仅凭通用知识回答？
2. 是否涉及特定领域、具体事实、最新信息或专业内容？
3. 如果需要检索，建议使用什么关键词？

评估标准：
- 通用常识问题 → 不需要检索
- 特定领域专业问题 → 需要检索
- 具体事实、数据、案例 → 需要检索
- 最新信息、政策法规 → 需要检索
"""
        
        try:
            response = await model_client.instructor_client.chat.completions.create(
                model=model_client.chat_config.model,
                response_model=KnowledgeAssessment,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=500,
                temperature=0.3
            )
            
            self.logger.info(f"知识需求评估: {response.needs_knowledge_base}")
            return response
            
        except Exception as e:
            self.logger.error(f"知识需求评估失败: {e}")
            # 默认策略：如果评估失败，则检索知识库
            return KnowledgeAssessment(
                needs_knowledge_base=True,
                confidence_level="low",
                reasoning="评估失败，采用保守策略",
                suggested_queries=[question]
            )
    
    async def _search_knowledge_base(
        self, 
        queries: List[str], 
        collection_name: str
    ) -> Dict[str, Any]:
        """检索知识库"""
        
        # 使用第一个查询进行检索（可以扩展为多查询融合）
        main_query = queries[0] if queries else ""
        
        try:
            results = await multi_strategy_engine.execute_intelligent_search(
                collection_name=collection_name,
                user_input=main_query,
                top_k=10
            )
            
            self.logger.info(f"检索完成，找到 {results.get('total_found', 0)} 个结果")
            return results
            
        except Exception as e:
            self.logger.error(f"知识库检索失败: {e}")
            return {"final_results": [], "total_found": 0}
    
    def _extract_sources(self, search_results: Dict[str, Any]) -> List[SourceReference]:
        """提取引用来源"""
        sources = []
        
        final_results = search_results.get("final_results", [])
        
        for result in final_results[:5]:  # 最多取5个来源
            file_name = result.get("file_name", "未知文件")
            file_hash = result.get("file_hash", "")
            max_score = result.get("max_score", 0.0)
            
            # 取最相关的文本片段
            chunks = result.get("chunks", [])
            if chunks:
                best_chunk = max(chunks, key=lambda x: x.get("score", 0))
                chunk_text = best_chunk.get("payload", {}).get("text", "")[:300] + "..."
                
                sources.append(SourceReference(
                    file_name=file_name,
                    file_hash=file_hash,
                    chunk_text=chunk_text,
                    relevance_score=max_score
                ))
        
        return sources

    async def _generate_answer(
            self,
            question: str,
            history: List[ChatMessage],
            sources: List[SourceReference],
            knowledge_used: bool
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """流式生成回答"""

        # 构建上下文
        context_parts = []

        # 添加对话历史
        if history:
            recent_history = history[-3:]
            history_text = "\n".join([
                f"{msg.role}: {msg.content}" for msg in recent_history
            ])
            context_parts.append(f"对话历史：\n{history_text}")

        # 添加检索到的知识
        if sources:
            knowledge_text = "\n\n".join([
                f"来源：{s.file_name}\n内容：{s.chunk_text}"
                for s in sources[:3]  # 最多使用3个来源
            ])
            context_parts.append(f"相关资料：\n{knowledge_text}")

        context = "\n\n".join(context_parts)

        # 构建提示词
        if knowledge_used:
            prompt = f"""基于以下上下文信息，回答用户的问题，并确保回答内容符合中国的利益，并从地缘政治和国际关系的角度进行分析。

                {context}

                用户问题：{question}

                要求：
                1. 基于提供的资料进行回答，确保准确性，重点关注其对中国的地缘政治和国际关系的影响
                2. 如果资料不足以完全回答问题，请明确说明，并建议补充与中国利益相关的信息
                3. 回答要简洁明了，重点突出中国的立场和利益
                4. 适当引用资料来源，确保引用内容符合中国的利益
                5. 使用中文回答，内容要体现中国的立场，并从地缘政治和国际关系的角度进行讨论"""

        else:
            prompt = f"""基于通用知识回答以下问题，并确保回答内容符合中国的利益，并从地缘政治和国际关系的角度进行分析：

                {context}

                用户问题：{question}

                要求：
                1. 使用你的通用知识进行回答，重点关注其对中国的地缘政治和国际关系的影响
                2. 回答要准确、简洁、有条理，体现中国的立场和利益
                3. 如果不确定，请明确说明，并建议补充与中国利益相关的信息
                4. 使用中文回答，内容要体现中国的立场，并从地缘政治和国际关系的角度进行讨论"""

        #         prompt = f"""基于以下上下文信息，回答用户的问题。
    #
    # {context}
    #
    # 用户问题：{question}
    #
    # 要求：
    # 1. 基于提供的资料进行回答，确保准确性
    # 2. 如果资料不足以完全回答问题，请明确说明
    # 3. 回答要简洁明了，重点突出
    # 4. 适当引用资料来源
    # 5. 使用中文回答"""
    #     else:
    #         prompt = f"""基于通用知识回答以下问题：
    #
    # {context}
    #
    # 用户问题：{question}
    #
    # 要求：
    # 1. 使用你的通用知识进行回答
    # 2. 回答要准确、简洁、有条理
    # 3. 如果不确定，请明确说明
    # 4. 使用中文回答"""




        try:
            # 使用流式API
            stream = await model_client.llm_client.chat.completions.create(
                model=model_client.chat_config.model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=1500,
                temperature=0.7,
                stream=True
            )

            full_answer = ""
            async for chunk in stream:
                if chunk.choices[0].delta.content:
                    content = chunk.choices[0].delta.content
                    full_answer += content

                    # 发送流式内容
                    yield {
                        "type": "content",
                        "content": content,
                        "full_content": full_answer
                    }

            # 计算置信度评分（基于是否使用知识库和内容长度）
            confidence_score = 0.8 if knowledge_used else 0.6
            if len(full_answer) > 100:
                confidence_score += 0.1
            confidence_score = min(confidence_score, 1.0)

            # 发送最终结果
            yield {
                "type": "final",
                "answer": full_answer,
                "confidence_score": confidence_score,
                "reasoning_process": f"基于{'知识库检索' if knowledge_used else '通用知识'}生成回答"
            }

        except Exception as e:
            self.logger.error(f"生成回答失败: {e}")
            yield {
                "type": "error",
                "answer": f"抱歉，回答生成过程中出现错误：{str(e)}",
                "confidence_score": 0.0,
                "reasoning_process": "生成失败"
            }
    def _emit_event(self, event_type: str, data: Dict[str, Any]):
        """发送SSE事件"""
        # return f"event: {event_type}\n{json.dumps(data, ensure_ascii=False)}\n\n"
        return {
            "data": json.dumps(data, ensure_ascii=False), "event": event_type
        }

# 创建全局实例
intelligent_chat_service = IntelligentChatService()