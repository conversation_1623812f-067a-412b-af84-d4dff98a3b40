import asyncio
from typing import List, Dict, Any, Optional
from collections import defaultdict

from app.database.qdrant_manager import qdrant_manager
from app.models.database import FileInfo
from app.service.search.intelligent_search_planner import intelligent_planner, SearchIntent
from app.service.model_client import model_client
from app.utils.logger import setup_logger
logger = setup_logger(__name__)

class MultiStrategySearchEngine:
    """多策略搜索引擎"""
    collection_name: str = "documents"
    knowledge_name: str = None

    async def execute_intelligent_search(
        self,
        collection_name: str,
        user_input: str,
        top_k: int = 20
    ) -> Dict[str, Any]:
        """执行智能搜索"""
        self.collection_name = collection_name
        self.knowledge_name = collection_name  # collection_name 实际上就是 knowledge_name
        # 生成搜索计划
        search_plan = await intelligent_planner.generate_search_plan(user_input)
        
        # 执行多策略搜索
        search_results = await self._execute_search_strategies(
            search_plan["search_strategies"],
            search_plan["execution_order"],
            top_k
        )
        
        # 结果融合和重排序
        final_results = await self._merge_and_rerank_results(
            search_results,
            search_plan["intent_analysis"]
        )

        # 规整final_results
        return {
            "search_plan": search_plan,
            "strategy_results": search_results,
            "final_results": final_results[:top_k],
            "total_found": len(final_results)
        }
    
    async def _execute_search_strategies(
        self, 
        strategies: List[Dict[str, Any]], 
        execution_order: List[str],
        top_k: int
    ) -> Dict[str, List]:
        """执行搜索策略"""
        
        results = {}
        
        # 按优先级分组执行
        high_priority = [s for s in strategies if s["type"] in execution_order[:2]]
        medium_priority = [s for s in strategies if s["type"] in execution_order[2:4]]
        low_priority = [s for s in strategies if s["type"] in execution_order[4:]]
        
        # 高优先级串行执行
        for strategy in high_priority:
            result = await self._single_strategy_search(strategy, top_k//2)
            results[f"{strategy['type']}_{strategy['query'][:20]}"] = result
        
        # 中优先级并行执行
        if medium_priority:
            medium_tasks = [
                self._single_strategy_search(strategy, top_k//3) 
                for strategy in medium_priority
            ]
            medium_results = await asyncio.gather(*medium_tasks)
            for strategy, result in zip(medium_priority, medium_results):
                results[f"{strategy['type']}_{strategy['query'][:20]}"] = result
        
        # 低优先级并行执行
        if low_priority:
            low_tasks = [
                self._single_strategy_search(strategy, top_k//4) 
                for strategy in low_priority
            ]
            low_results = await asyncio.gather(*low_tasks)
            for strategy, result in zip(low_priority, low_results):
                results[f"{strategy['type']}_{strategy['query'][:20]}"] = result
        
        return results
    
    async def _single_strategy_search(
        self, 
        strategy: Dict[str, Any], 
        limit: int
    ) -> List[Dict[str, Any]]:
        """执行单个搜索策略"""
        
        try:
            if strategy["type"] == "entity_search":
                # 实体搜索可能需要特殊处理
                return await self._entity_based_search(strategy["query"], limit)
            else:
                # 标准向量搜索
                return await self._vector_search(strategy["query"], limit)
                
        except Exception as e:
            logger.error(f"搜索策略执行失败: {strategy['type']} - {e}")
            return []
    
    async def _vector_search(self, query: str, limit: int) -> List[Dict[str, Any]]:
        """向量搜索"""
        # 翻译查询（如果需要）
        # if detect(query) in ['zh-cn', 'zh']:
        #     russian_query = await cross_lingual_search.translate_to_russian(query)
        # else:
        #     russian_query = query
            
        # 生成嵌入向量
        query_embedding = await model_client.get_embeddings(query)
        
        # 搜索
        results = await qdrant_manager.search_vectors(
            collection_name=self.collection_name,
            query_vector=query_embedding[0],
            limit=limit
        )

        return [{
            "doc_id": r.id,
            "score": r.score,
            "payload": {
                "id": r.payload.get("id"),
                "file_hash": r.payload.get("file_hash"),
                "text": r.payload.get("text")
            }
        } for r in results]
    
    async def _entity_based_search(self, entity: str, limit: int) -> List[Dict[str, Any]]:
        """基于实体的搜索"""
        # 可以结合图数据库或特殊的实体索引
        return await self._vector_search(entity, limit)

    async def _merge_and_rerank_results(
            self,
            strategy_results: Dict[str, List],
            intent: SearchIntent
    ) -> List[Dict[str, Any]]:
        """合并和重排序结果"""

        # 收集所有结果并计算加权分数
        doc_scores = defaultdict(float)
        doc_info = {}

        for strategy_name, results in strategy_results.items():
            strategy_type = strategy_name.split('_')[0]
            weight = self._get_strategy_weight(strategy_type, intent)

            for result in results:
                doc_id = result["doc_id"]
                doc_scores[doc_id] += result["score"] * weight
                doc_info[doc_id] = result

        # 按file_hash分组
        file_groups = defaultdict(list)
        for doc_id, final_score in doc_scores.items():
            result = doc_info[doc_id].copy()
            result["final_score"] = final_score
            file_hash = result["payload"]["file_hash"]
            file_groups[file_hash].append(result)

        # 对每个文档内的结果按id排序，然后按文档的最高分数排序
        final_results = []
        for file_hash, results in file_groups.items():
            try:
                # 文档内按id排序
                results.sort(key=lambda x: x["payload"]["id"])
                # 计算文档总分（取最高分或平均分）
                doc_max_score = max(r["final_score"] for r in results)

                # 安全地获取文档信息 - 使用复合查询条件
                file_info = await self._get_file_info_safe(file_hash, self.knowledge_name)

                if file_info:
                    final_results.append({
                        "file_hash": file_hash,
                        "file_name": file_info.original_filename,
                        "summary": file_info.summary,
                        "metadata": file_info.metadata,
                        "max_score": doc_max_score,
                        "chunks": results
                    })
                else:
                    # 如果无法获取文件信息，使用默认值
                    logger.warning(f"无法获取文件信息，file_hash: {file_hash}，使用默认值")
                    final_results.append({
                        "file_hash": file_hash,
                        "file_name": f"未知文件_{file_hash[:8]}",
                        "summary": "文件信息获取失败",
                        "metadata": {},
                        "max_score": doc_max_score,
                        "chunks": results
                    })

            except Exception as e:
                logger.error(f"处理文档 {file_hash} 时出错: {e}")
                # 继续处理其他文档，不让单个文档错误影响整体结果
                continue

        # 按文档最高分数排序
        final_results.sort(key=lambda x: x["max_score"], reverse=True)

        return final_results

    # async def _merge_and_rerank_results(
    #         self,
    #         strategy_results: Dict[str, List],
    #         intent: SearchIntent
    # ) -> List[Dict[str, Any]]:
    #     """合并和重排序结果"""
    #
    #     # 收集所有结果并计算加权分数
    #     doc_scores = defaultdict(float)
    #     doc_info = {}
    #
    #     for strategy_name, results in strategy_results.items():
    #         strategy_type = strategy_name.split('_')[0]
    #         weight = self._get_strategy_weight(strategy_type, intent)
    #
    #         for result in results:
    #             doc_id = result["doc_id"]
    #             doc_scores[doc_id] += result["score"] * weight
    #             doc_info[doc_id] = result
    #
    #     # 按分数排序
    #     sorted_docs = sorted(
    #         doc_scores.items(),
    #         key=lambda x: x[1],
    #         reverse=True
    #     )
    #
    #     # 构建最终结果并规整格式
    #     final_results = []
    #     for doc_id, final_score in sorted_docs:
    #         result = doc_info[doc_id].copy()
    #
    #         # 规整final_results格式
    #         formatted_result = {
    #             "doc_id": result["doc_id"],
    #             "score": result["score"],
    #             "final_score": final_score,
    #             "payload": {
    #                 "id": result["payload"].get("id"),
    #                 "file_hash": result["payload"].get("file_hash"),
    #                 "text": result["payload"].get("text", "")[:500] + "..." if len(
    #                     result["payload"].get("text", "")) > 500 else result["payload"].get("text", "")  # 限制文本长度
    #             }
    #         }
    #         final_results.append(formatted_result)
    #
    #     return final_results
    def _get_strategy_weight(self, strategy_type: str, intent: SearchIntent) -> float:
        """获取策略权重"""
        weights = {
            "exact_topic": 1.0,
            "generated_query": 0.9,
            "entity_search": 0.8,
            "semantic_search": 0.7,
            "concept_search": 0.6
        }
        
        # 根据搜索范围调整权重
        if intent.search_scope == "specific":
            weights["exact_topic"] *= 1.2
            weights["entity_search"] *= 1.1
        elif intent.search_scope == "broad":
            weights["concept_search"] *= 1.2
            weights["semantic_search"] *= 1.1
        
        return weights.get(strategy_type, 0.5)

    async def _get_file_info_safe(self, file_hash: str, knowledge_name: str = None) -> Optional[FileInfo]:
        """安全地获取文件信息，处理重复记录问题"""
        try:
            # 构建查询条件
            query_conditions = {"file_hash": file_hash}
            if knowledge_name:
                query_conditions["knowledge_name"] = knowledge_name

            # 首先尝试使用 get() 方法
            try:
                file_info = await FileInfo.get(**query_conditions)
                return file_info
            except Exception as get_error:
                # 如果 get() 失败（可能是多个对象），使用 filter().first()
                logger.warning(f"FileInfo.get() 失败，尝试使用 filter().first(): {get_error}")

                file_info = await FileInfo.filter(**query_conditions).first()
                if file_info:
                    # 检查是否存在重复记录
                    duplicate_count = await FileInfo.filter(**query_conditions).count()
                    if duplicate_count > 1:
                        logger.warning(f"发现重复的文件记录: file_hash={file_hash}, knowledge_name={knowledge_name}，数量: {duplicate_count}")
                        # 清理重复记录
                        await self._cleanup_duplicate_file_records(file_hash, knowledge_name)

                    return file_info
                else:
                    logger.error(f"未找到对应的文件记录: file_hash={file_hash}, knowledge_name={knowledge_name}")
                    return None

        except Exception as e:
            logger.error(f"获取文件信息时发生异常，file_hash: {file_hash}, knowledge_name: {knowledge_name}, 错误: {e}")
            return None

    async def _cleanup_duplicate_file_records(self, file_hash: str, knowledge_name: str = None):
        """清理重复的文件记录，保留最新的一条"""
        try:
            # 构建查询条件
            query_conditions = {"file_hash": file_hash}
            if knowledge_name:
                query_conditions["knowledge_name"] = knowledge_name

            # 获取所有重复记录，按创建时间排序
            duplicate_records = await FileInfo.filter(**query_conditions).order_by('-created_at').all()

            if len(duplicate_records) <= 1:
                return  # 没有重复记录

            # 保留最新的记录，删除其他的
            records_to_keep = duplicate_records[0]
            records_to_delete = duplicate_records[1:]

            logger.info(f"清理重复记录: file_hash={file_hash}, knowledge_name={knowledge_name}, 保留记录 ID {records_to_keep.id}，删除 {len(records_to_delete)} 条重复记录")

            # 删除重复记录
            for record in records_to_delete:
                await record.delete()
                logger.debug(f"删除重复记录: ID {record.id}, file_hash: {file_hash}, knowledge_name: {knowledge_name}")

        except Exception as e:
            logger.error(f"清理重复文件记录失败，file_hash: {file_hash}, knowledge_name: {knowledge_name}, 错误: {e}")

multi_strategy_engine = MultiStrategySearchEngine()