import hashlib
import io
import os
import tempfile
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional
import mimetypes

from fastapi import UploadFile, File

from app.database.minio_manager import minio_manager
from app.database.postgres_manager import postgres_manager
from app.models.database import FileInfo, KnowledgeBase
from app.models.database.document import AnalysisStatus
from app.models.document_upload import DocumentUploadResponse
from app.service.document.async_parse_queue import document_queue

try:
    from docx import Document as DocxDocument
    from docx.opc.exceptions import PackageNotFoundError

    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False

try:
    import openpyxl
    from openpyxl.utils.exceptions import InvalidFileException

    EXCEL_AVAILABLE = True
except ImportError:
    EXCEL_AVAILABLE = False

try:
    from pptx import Presentation
    from pptx.exc import PackageNotFoundError as PptxPackageNotFoundError

    PPTX_AVAILABLE = True
except ImportError:
    PPTX_AVAILABLE = False

from app.utils.logger import setup_logger
from app.utils.exceptions import DocumentProcessingError

logger = setup_logger(__name__)


def extract_document_metadata(file_path: str) -> Dict[str, Any]:
    """
    提取文档元数据
    Office 文档元数据：作者，最后保存者，创建、修改时间，公司，管理者
    :param file_path: 文档文件路径
    :return: 元数据字典
    """
    logger.info(f"从文档中提取元数据: {file_path}")

    if not os.path.exists(file_path):
        raise DocumentProcessingError(f"文件不存在: {file_path}")

    file_path_obj = Path(file_path)
    file_stats = file_path_obj.stat()

    # 基础文件信息
    metadata = {
        "author": None,
        "last_modified_by": None,
        "created": None,
        "modified": None,
        "company": None,
        "title": None,
    }

    try:
        # 根据文件类型提取特定元数据
        if file_path_obj.suffix.lower() in ['.docx', '.doc']:
            metadata.update(_extract_word_metadata(file_path))
        elif file_path_obj.suffix.lower() in ['.xlsx', '.xls']:
            metadata.update(_extract_excel_metadata(file_path))
        elif file_path_obj.suffix.lower() in ['.pptx', '.ppt']:
            metadata.update(_extract_powerpoint_metadata(file_path))
        else:
            logger.warning(f"不支持的文件类型: {file_path_obj.suffix}")

    except Exception as e:
        logger.error(f"提取元数据失败: {str(e)}")
        metadata["extraction_error"] = str(e)

    logger.info(f"Successfully extracted metadata for: {file_path}")
    return metadata


def _extract_word_metadata(file_path: str) -> Dict[str, Any]:
    """提取Word文档元数据"""
    if not DOCX_AVAILABLE:
        logger.warning("python-docx未安装，无法提取Word元数据")
        return {}

    try:
        doc = DocxDocument(file_path)
        core_props = doc.core_properties
        return {
            "author": core_props.author,
            "last_modified_by": core_props.last_modified_by,
            "created": core_props.created.isoformat() if core_props.created else None,
            "modified": core_props.modified.isoformat() if core_props.modified else None,
            "title": core_props.title,
            # "company": core_props.company,
        }
    except (PackageNotFoundError, Exception) as e:
        logger.error(f"Word文档元数据提取失败: {str(e)}")
        return {"extraction_error": str(e)}


def _extract_excel_metadata(file_path: str) -> Dict[str, Any]:
    """提取Excel文档元数据"""
    if not EXCEL_AVAILABLE:
        logger.warning("openpyxl未安装，无法提取Excel元数据")
        return {}

    try:
        workbook = openpyxl.load_workbook(file_path, read_only=True)
        props = workbook.properties

        return {
            "author": props.creator,
            "last_modified_by": props.lastModifiedBy,
            "created": props.created.isoformat() if props.created else None,
            "modified": props.modified.isoformat() if props.modified else None,
            "title": props.title,
            # "company": props.company,
        }
    except (InvalidFileException, Exception) as e:
        logger.error(f"Excel文档元数据提取失败: {str(e)}")
        return {"extraction_error": str(e)}


def _extract_powerpoint_metadata(file_path: str) -> Dict[str, Any]:
    """提取PowerPoint文档元数据"""
    if not PPTX_AVAILABLE:
        logger.warning("python-pptx未安装，无法提取PowerPoint元数据")
        return {}

    try:
        prs = Presentation(file_path)
        core_props = prs.core_properties

        return {
            "author": core_props.author,
            "last_modified_by": core_props.last_modified_by,
            "created": core_props.created.isoformat() if core_props.created else None,
            "modified": core_props.modified.isoformat() if core_props.modified else None,
            "title": core_props.title,
            # "company": core_props.company,
        }
    except (PptxPackageNotFoundError, Exception) as e:
        logger.error(f"PowerPoint文档元数据提取失败: {str(e)}")
        return {"extraction_error": str(e)}

async def upload_document_service(knowledge_hash: str, file: UploadFile = File(...)) -> DocumentUploadResponse:
    # 验证知识库hash是否存在
    knowledge = await KnowledgeBase.get(name_md5=knowledge_hash)
    if not knowledge:
        return DocumentUploadResponse(code=-1, message="知识库不存在", file_info={})
    # 读取文件内容
    file_content = await file.read()
    # 获取文档hash
    file_hash = await postgres_manager.calculate_file_hash(file_content)
    # 检查数据库中是否存在该文件
    existing_file = await postgres_manager.check_file_exists(file_hash, knowledge_hash)
    if existing_file:
        return DocumentUploadResponse(message="文件已存在", file_info=existing_file)

    # 创建临时文件
    with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1]) as temp_file:
        temp_file.write(file_content)
        temp_file_path = temp_file.name

    try:
        # 提取文档元数据
        document_meta = extract_document_metadata(temp_file_path)
        logger.info(f"成功提取元数据: {file.filename}")

        # 重置文件指针用于后续上传到minio
        await file.seek(0)

        # md5_hash  = hashlib.md5()
        # md5_hash.update(knowledge_name.encode("utf-8"))
        # bucket_name = md5_hash.hexdigest()
        minio_name = f"{file_hash}{Path(file.filename).suffix}"
        etag = await minio_manager.upload_file(knowledge_hash, minio_name, temp_file_path)
        # TODO: 保存元信息和minio信息到数据库
        await FileInfo.create(
            file_hash=file_hash,
            original_filename=file.filename,
            bucket_name=knowledge_hash,
            knowledge_name=knowledge_hash,
            minio_name=minio_name,
            file_size=len(file_content),
            file_type=Path(file.filename).suffix,
            metadata=document_meta,
            etag=etag
        )
        task_id = await document_queue.add_task(file_hash, knowledge_hash)
        logger.info(f"成功提交任务: {task_id}")
        #
        # # 更新状态为排队中
        await FileInfo.filter(file_hash=file_hash, knowledge_name=knowledge_hash).update(
            analysis_status=AnalysisStatus.ANALYZING.value,
            processed_status="任务已加入处理队列，等待执行...\n"
        )

        # 返回成功响应
        return DocumentUploadResponse(
            message="文件上传成功",
            file_info={
                "original_name": file.filename,
                "upload_time": datetime.now().isoformat(),
                "file_size": len(file_content),
                "file_hash": file_hash,
                "minio_name": minio_name
            }
        )

    finally:
        # 清理临时文件
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)