import asyncio
import os.path
import uuid
from io import By<PERSON><PERSON>
from typing import List, Dict, Any, Tu<PERSON>

from docling.datamodel.accelerator_options import AcceleratorOptions, AcceleratorDevice
from docling.datamodel.base_models import InputFormat
from docling.datamodel.document import ConversionR<PERSON>ult
from docling.datamodel.pipeline_options import PdfPipelineOptions, TableStructureOptions, TesseractCliOcrOptions, \
    RapidOcrOptions
from docling.document_converter import DocumentConverter, PdfFormatOption, WordFormatOption
from docling.pipeline.simple_pipeline import SimplePipeline
from docling.pipeline.standard_pdf_pipeline import StandardPdfPipeline
from docling_core.transforms.chunker.tokenizer.huggingface import HuggingFaceTokenizer
from docling.chunking import HybridChunker
from docling_core.types.io import DocumentStream
from qdrant_client.http.models import PointStruct
from transformers import AutoTokenizer

from app.config.settings import settings
from app.database.minio_manager import minio_manager
from app.database.qdrant_manager import qdrant_manager
from app.models.database import FileInfo
from app.models.database.document import AnalysisStatus
from app.models.database.qdrant import DocumentEmbedding
from app.service.document.docling_parse import CombinedSerializerProvider
from app.service.model_client import model_client
from app.utils.logger import setup_logger

logger = setup_logger(__name__)
accelerator_options = AcceleratorOptions(
    num_threads=8,
    device=AcceleratorDevice.AUTO,  # 可换 AUTO / CUDA / MPS
    # cuda_use_flash_attention2=True
)
# ✅ 2. 配置 PDF 解析管道
pipeline_options = PdfPipelineOptions(
    accelerator_options=accelerator_options,
    do_ocr=True,
    do_table_structure=True,
    table_structure_options=TableStructureOptions(
        do_cell_matching=True,
    ),
    ocr_options=RapidOcrOptions(
        det_model_path=os.path.join("model",  "PP-OCRv4", "en_PP-OCRv3_det_infer.onnx"),
        rec_model_path=os.path.join("model", "PP-OCRv4", "ch_PP-OCRv4_rec_server_infer.onnx"),
        cls_model_path=os.path.join(
        "model", "PP-OCRv3", "ch_ppocr_mobile_v2.0_cls_train.onnx"
    ),
    )
    # ocr_options=TesseractCliOcrOptions(force_full_page_ocr=True, lang=["auto"]),
    # images_scale=IMAGE_SCN_SCALE_INDEX,
    # generate_page_images=True,
    # generate_picture_images=True,
)
# vlm_pipeline_options = VlmPipelineOptions(
#     accelerator_options=accelerator_options,
# )
converter = DocumentConverter(
    allowed_formats=[
        InputFormat.PDF,
        InputFormat.IMAGE,
        InputFormat.DOCX,
        InputFormat.HTML,
        InputFormat.PPTX,
        InputFormat.XML_USPTO,
        InputFormat.CSV,
        InputFormat.MD,
        InputFormat.XLSX,

    ],
    format_options={
        InputFormat.PDF: PdfFormatOption(
            pipeline_cls=StandardPdfPipeline,
            pipeline_options=pipeline_options,
        ),
        InputFormat.DOCX: WordFormatOption(
            pipeline_cls=SimplePipeline,
        ),
    },
)

class DocumentProcessor:
    def __init__(self):
        self.tokenizer = None
        self.chunker = None
        self._initialized = False
        
    async def initialize(self):
        """初始化重量级对象"""
        if self._initialized:
            return
            
        self.tokenizer = HuggingFaceTokenizer(
            tokenizer=AutoTokenizer.from_pretrained(settings.docling_config.embedding_model),
            max_tokens=2056,
        )
        self.chunker = HybridChunker(
            tokenizer=self.tokenizer, 
            serializer_provider=CombinedSerializerProvider(),
            merge_peers=True
        )
        self._initialized = True
        logger.info("文档处理器初始化完成")
        
    async def process_document(self, file_hash: str, knowledge_name: str) -> bool:
        """处理单个文档"""
        try:

            # 获取文件信息
            file_info = await FileInfo.get(file_hash=file_hash, knowledge_name=knowledge_name)
            if not file_info:
                logger.warning(f"文件 {file_hash} 不存在")
                return False
                
            if file_info.analysis_status == AnalysisStatus.COMPLETED.value:
                logger.warning(f"文件 {file_hash} 已经解析完成")
                return True
                
            # 更新状态为处理中
            await FileInfo.filter(file_hash=file_hash, knowledge_name=knowledge_name).update(
                analysis_status=AnalysisStatus.ANALYZING.value
            )
            processed_status = "开始解析文件...\n"
            # 执行处理步骤
            await self._update_status(file_hash, knowledge_name, processed_status)
            document = await self._parse_document(file_info)
            processed_status += "开始分块...\n"
            await self._update_status(file_hash, knowledge_name, processed_status)
            logger.info(f"开始分块: {file_info.minio_name}")
            chunks = await self._chunk_document(document)
            processed_status += "开始处理分块...\n"
            await self._update_status(file_hash, knowledge_name, processed_status)
            logger.info(f"共有{len(chunks)} 片")
            points, summary_chunks, processed = await self._process_chunks(file_hash, chunks, processed_status, knowledge_name)
            processed_status = processed
            processed_status += "存储向量...\n"
            await self._update_status(file_hash, knowledge_name, processed_status)
            logger.info(f"向量化开始")
            await self._store_vectors(file_info, points)
            processed_status += "生成摘要...\n"
            await self._update_status(file_hash, knowledge_name, processed_status)
            logger.info(f"摘要生成开始")
            summary = await self._generate_summary(summary_chunks)
            
            # 完成处理
            await self._finalize_processing(file_info, summary, processed_status)
            
            logger.info(f"文档处理完成: {file_info.minio_name}")
            return True
            
        except Exception as e:
            logger.error(f"文档处理失败 {file_hash}: {e}")
            await FileInfo.filter(file_hash=file_hash, knowledge_name=knowledge_name).update(
                analysis_status=AnalysisStatus.FAILED.value,
                processed_status=f"处理失败: {str(e)}"
            )
            return False
            
    async def _update_status(self, file_hash: str, knowledge_name: str, status: str):
        """更新处理状态"""
        await FileInfo.filter(file_hash=file_hash, knowledge_name=knowledge_name).update(
            processed_status=status
        )
        
    async def _process_chunks(self, file_hash: str, chunks: List, processed_status: str, knowledge_name: str) -> tuple:
        """并发处理分块"""
        points = []
        summary_chunks = []
        processed = processed_status
        # 分批处理避免内存溢出
        batch_size = 5
        for i in range(0, len(chunks), batch_size):
            batch = chunks[i:i + batch_size]
            batch_tasks = [
                self._process_single_chunk(file_hash, idx + i, chunk)
                for idx, chunk in enumerate(batch)
            ]
            
            batch_results = await asyncio.gather(*batch_tasks)
            for point, summary_chunk in batch_results:
                points.append(point)
                summary_chunks.append(summary_chunk)
                
            # 更新进度
            processed += f"已处理 {min(i + batch_size, len(chunks))}/{len(chunks)} 个分块\n"
            await self._update_status(file_hash, knowledge_name, processed)
            
        return points, summary_chunks, processed
        
    async def _process_single_chunk(self, file_hash: str, idx: int, chunk) -> tuple:
        """处理单个分块"""
        ser_txt = self.chunker.contextualize(chunk=chunk)
        ser_tokens = self.tokenizer.count_tokens(ser_txt)
        
        summary_chunk = {
            "text": ser_txt,
            "tokens": ser_tokens,
        }
        
        embedding_list = await model_client.get_embeddings(ser_txt)
        uid = uuid.uuid4()
        
        point = PointStruct(
            id=str(uid),
            vector=embedding_list[0],
            payload=DocumentEmbedding(
                id=idx,
                text=ser_txt,
                file_hash=file_hash,
                embedding=embedding_list[0],
            ).to_dict()
        )
        
        return point, summary_chunk

    async def _parse_document(self, file_info: FileInfo)->ConversionResult:
        """解析文档"""
        file_content = await minio_manager.get_file(bucket_name=file_info.bucket_name,
                                                    object_name=file_info.minio_name)
        logger.info(f"开始解析文件: {file_info.minio_name}")
        content: bytes = file_content.read()
        # document_stream = DocumentStream(
        #     name=file_info.minio_name,  # 文件名字段
        #     stream=BytesIO(content)  # 将 bytes 转为 BytesIO 流
        # )
        # document = converter.convert(document_stream)
        # 将同步的文档解析操作移到线程池中执行
        loop = asyncio.get_event_loop()
        document = await loop.run_in_executor(
            None,  # 使用默认线程池
            self._convert_document_sync,
            file_info.minio_name,
            content
        )
        return document

    def _convert_document_sync(self, filename: str, content: bytes) -> ConversionResult:
        """同步执行文档转换（在线程池中运行）"""
        document_stream = DocumentStream(
            name=filename,
            stream=BytesIO(content)
        )
        return converter.convert(document_stream)
    async def _chunk_document(self, document: ConversionResult) -> list:
        chunk_iter = self.chunker.chunk(dl_doc=document.document)
        chunks = list(chunk_iter)
        return  chunks

    async def _store_vectors(self, file_info: FileInfo, points: List[PointStruct]):
        """存储向量"""
        qdrant_manager.store_vectors(points=points, collection_name=file_info.bucket_name, vector_size=len(points[0].payload.get("embedding")))

    async def _generate_summary(self, summary_chunks: List[dict]):
        """生成摘要"""
        summary = await model_client.generate_summary(texts=summary_chunks)
        return summary

    async def _finalize_processing(self, file_info: FileInfo, summary: str, processed_status: str):
        """完成处理"""
        file_info.summary = summary
        file_info.analysis_status = AnalysisStatus.COMPLETED.value
        processed_status += f"任务完成。。。\n"
        file_info.processed_status = processed_status
        await file_info.save()
        logger.info(f"摘要生成完成: {file_info.minio_name}")

