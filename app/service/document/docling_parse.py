from typing import Any, Optional

from docling_core.transforms.chunker.hierarchical_chunker import ChunkingSerializer<PERSON>rovider, ChunkingDocSerializer
from docling_core.transforms.serializer.base import (
    BaseDocSerializer,
    SerializationResult,
)
from docling_core.transforms.serializer.common import create_ser_result
from docling_core.transforms.serializer.html import HTMLTextSerializer, HTMLTableSerializer
from docling_core.transforms.serializer.markdown import (
    MarkdownParams,
    MarkdownPictureSerializer, MarkdownTableSerializer, MarkdownTextSerializer,
)
from docling_core.types.doc.document import (
    DoclingDocument,
    ImageRefMode,
    PictureDescriptionData,
    PictureItem,
)
from typing_extensions import override

# 图片自定义
class AnnotationPictureSerializer(MarkdownPictureSerializer):
    @override
    def serialize(
        self,
        *,
        item: PictureItem,
        doc_serializer: BaseDocSerializer,
        doc: DoclingDocument,
        separator: Optional[str] = None,
        **kwargs: Any,
    ) -> SerializationResult:
        text_parts: list[str] = []

        # reusing the existing result:
        parent_res = super().serialize(
            item=item,
            doc_serializer=doc_serializer,
            doc=doc,
            **kwargs,
        )
        text_parts.append(parent_res.text)

        # appending annotations:
        for annotation in item.annotations:
            if isinstance(annotation, PictureDescriptionData):
                text_parts.append(f"")

        text_res = (separator or "\n").join(text_parts)
        return create_ser_result(text=text_res, span_source=item)

class ImgPlaceholderSerializerProvider(ChunkingSerializerProvider):
    def get_serializer(self, doc):
        return ChunkingDocSerializer(
            doc=doc,
            params=MarkdownParams(
                image_placeholder="<!-- image -->",
            ),
        )

# 表格自定义
# class MDTableSerializerProvider(ChunkingSerializerProvider):
#     def get_serializer(self, doc):
#         return ChunkingDocSerializer(
#             doc=doc,
#             table_serializer=MarkdownTableSerializer(),  # configuring a different table serializer
#         )


class CombinedSerializerProvider(ChunkingSerializerProvider):
    def get_serializer(self, doc):
        return ChunkingDocSerializer(
            doc=doc,
            table_serializer=HTMLTableSerializer(),  # 配置表格序列化为Markdown
            picture_serializer=AnnotationPictureSerializer(),  # 配置图片序列化
        )