import asyncio
import uuid
from io import Bytes<PERSON>
from typing import Callable

from docling.datamodel.accelerator_options import AcceleratorOptions, AcceleratorDevice
from docling.datamodel.base_models import InputFormat
from docling.datamodel.pipeline_options import PdfPipelineOptions, \
    TesseractCliOcrOptions, TableStructureOptions
from docling.pipeline.simple_pipeline import SimplePipeline
from docling.pipeline.standard_pdf_pipeline import StandardPdfPipeline
from docling_core.transforms.chunker.tokenizer.huggingface import HuggingFaceTokenizer
from docling_core.types.io import DocumentStream

from docling.chunking import HybridChunker
from qdrant_client.http.models import PointStruct
from transformers import AutoTokenizer

from app.config.settings import settings
from app.database.minio_manager import minio_manager
from docling.document_converter import DocumentConverter, PdfFormatOption, WordFormatOption

from app.database.qdrant_manager import qdrant_manager
from app.models.database import FileInfo
from app.models.database.document import AnalysisStatus
from app.models.database.qdrant import DocumentEmbedding
from app.models.document_upload import ParseDocumentRequest, ParseDocumentResponse
from app.service.document.docling_parse import CombinedSerializerProvider
from app.service.model_client import model_client
from app.utils.logger import setup_logger
from app.service.document.document_processor import DocumentProcessor

logger = setup_logger(__name__)
# accelerator_options = AcceleratorOptions(
#     num_threads=8,
#     device=AcceleratorDevice.AUTO,  # 可换 AUTO / CUDA / MPS
#     # cuda_use_flash_attention2=True
# )
# ✅ 2. 配置 PDF 解析管道
# pipeline_options = PdfPipelineOptions(
#     accelerator_options=accelerator_options,
#     do_ocr=True,
#     do_table_structure=True,
#     table_structure_options=TableStructureOptions(
#         do_cell_matching=True,
#     ),
#     ocr_options=TesseractCliOcrOptions(force_full_page_ocr=True, lang=["rus"]),
#     # images_scale=IMAGE_SCN_SCALE_INDEX,
#     # generate_page_images=True,
#     # generate_picture_images=True,
# )
# vlm_pipeline_options = VlmPipelineOptions(
#     accelerator_options=accelerator_options,
# )


# converter = DocumentConverter(
#     allowed_formats=[
#         InputFormat.PDF,
#         InputFormat.IMAGE,
#         InputFormat.DOCX,
#         InputFormat.HTML,
#         InputFormat.PPTX,
#         InputFormat.XML_USPTO,
#         InputFormat.CSV,
#         InputFormat.MD,
#         InputFormat.XLSX,
#
#     ],
#     format_options={
#         InputFormat.PDF: PdfFormatOption(
#             pipeline_cls=StandardPdfPipeline,
#             pipeline_options=pipeline_options,
#         ),
#         InputFormat.DOCX: WordFormatOption(
#             pipeline_cls=SimplePipeline,
#         ),
#     },
# )


# async def parse_document_service(request: ParseDocumentRequest) -> ParseDocumentResponse:
#     # 遍历文件hash
#     for file_hash in request.file_hash:
#         try:
#
#             # 从数据库中获取文件信息
#             file_info = await FileInfo.get(file_hash=file_hash)
#             if not file_info:
#                 logger.warning(f"文件 {file_hash} 不存在")
#                 continue
#             if file_info.analysis_status == AnalysisStatus.COMPLETED.value:
#                 logger.warning(f"文件 {file_hash} 已经解析完成")
#                 continue
#             await FileInfo.filter(file_hash=file_hash).update(analysis_status=AnalysisStatus.ANALYZING.value)
#             # 从minio中获取文件
#             file_content = await minio_manager.get_file(bucket_name=file_info.bucket_name,
#                                                         object_name=file_info.minio_name)
#             logger.info(f"开始解析文件: {file_info.minio_name}")
#
#             processed_status = f"开始解析文件。。。\n"
#             await FileInfo.filter(file_hash=file_hash).update(processed_status=processed_status)
#             # 使用Docling解析
#             content: bytes = file_content.read()
#             document_stream = DocumentStream(
#                 name=file_info.minio_name,  # 文件名字段
#                 stream=BytesIO(content)  # 将 bytes 转为 BytesIO 流
#             )
#             document = converter.convert(document_stream)
#             # logger.info(f"简报生成完成: {file_info.minio_name}")
#             # print(document.document.export_to_markdown()) 对整个文档做一个简报
#
#             tokenizer = HuggingFaceTokenizer(
#                 tokenizer=AutoTokenizer.from_pretrained(settings.docling_config.embedding_model),
#                 max_tokens=512,  # optional, by default derived from `tokenizer` for HF case
#             )
#             processed_status += f"开始分块。。。\n"
#             await FileInfo.filter(file_hash=file_hash).update(processed_status=processed_status)
#             chunker = HybridChunker(tokenizer=tokenizer, serializer_provider=CombinedSerializerProvider(),
#                                     merge_peers=True)
#             chunk_iter = chunker.chunk(dl_doc=document.document)
#             chunks = list(chunk_iter)
#             logger.info(f"共有{len(chunks)} 片")
#             processed_status += f"开始处理分块。。。\n"
#             points: list[PointStruct] = []
#             vector_size = 0
#             summary_chunks: list[dict] = []
#             for i, chunk in enumerate(chunks):
#                 processed_status += f"开始处理第{i}片。。。\n"
#                 await FileInfo.filter(file_hash=file_hash).update(processed_status=processed_status)
#                 ser_txt = chunker.contextualize(chunk=chunk)
#                 ser_tokens = tokenizer.count_tokens(ser_txt)
#                 summary_chunks.append(
#                     {
#                         "text": ser_txt,
#                         "tokens": ser_tokens,
#                     }
#                 )
#                 embedding_list = await model_client.get_embeddings(ser_txt)
#                 vector_size = len(embedding_list[0])
#                 uid = uuid.uuid4()
#                 points.append(
#                     PointStruct(
#                         id=str(uid),
#                         vector=embedding_list[0],
#                         payload=DocumentEmbedding(
#                             id=i,
#                             text=ser_txt,
#                             file_hash=file_info.file_hash,
#                             embedding=embedding_list[0],
#                         ).to_dict()
#
#                     )
#                 )
#             processed_status += f"分片处理结束。。。，存储向量\n"
#             await FileInfo.filter(file_hash=file_hash).update(processed_status=processed_status)
#             qdrant_manager.store_vectors(points=points, collection_name=file_info.bucket_name, vector_size=vector_size)
#             logger.info(f"向量保存完成: {file_info.minio_name}")
#             processed_status += f"开始生成摘要。。。\n"
#             await FileInfo.filter(file_hash=file_hash).update(processed_status=processed_status)
#             summary = await model_client.generate_summary(texts=summary_chunks)
#             # await FileInfo.filter(file_hash=file_hash).update(summary=summary).update(analysis_status=AnalysisStatus.COMPLETED.value)
#             file_info.summary = summary
#             file_info.analysis_status = AnalysisStatus.COMPLETED.value
#             processed_status += f"任务完成。。。\n"
#             file_info.processed_status = processed_status
#             await file_info.save()
#             logger.info(f"摘要生成完成: {file_info.minio_name}")
#
#
#
#         except Exception as e:
#             logger.error(f"文件 {file_info.minio_name} 解析失败: {e}")
#             await FileInfo.filter(file_hash=file_hash).update(analysis_status=AnalysisStatus.FAILED.value)
#
#
#
#         # 保存解析后的文件
#     return ParseDocumentResponse(code=0, message="success")


# 在应用启动时初始化队列

from app.service.document.async_parse_queue import document_queue
# 全局队列实例
async def async_parse_document(file_hash:str, func: Callable):
    try:
        loop = asyncio.get_running_loop()
        result = await loop.run_in_executor(
            None,
            lambda: func(file_hash)
        )
        return result
    except Exception as e:
        logger.error(f"异步调用解析文档失败: {str(e)}")
        return ParseDocumentResponse(code=-1, message=str(e))
async def parse_document_service(request: ParseDocumentRequest) -> ParseDocumentResponse:
    """提交文档解析任务到队列"""
    task_ids = []

    for file_hash in request.file_hash:
        try:
            # 检查文件是否存在 - 注意：这里缺少knowledge_name参数，需要从请求中获取
            # TODO: 需要在ParseDocumentRequest中添加knowledge_name字段
            try:
                file_info = await FileInfo.get(file_hash=file_hash)
            except Exception as get_error:
                # 如果有多个记录，使用filter().first()
                file_info = await FileInfo.filter(file_hash=file_hash).first()

            if not file_info:
                logger.warning(f"文件 {file_hash} 不存在")
                continue

            if file_info.analysis_status == AnalysisStatus.COMPLETED.value:
                logger.warning(f"文件 {file_hash} 已经解析完成")
                continue
            if file_info.analysis_status == AnalysisStatus.ANALYZING.value:
                logger.warning(f"文件 {file_hash} 正在处理中")
                continue
            # 添加到队列
            task_id = await document_queue.add_task(file_hash)
            task_ids.append(task_id)

            # 更新状态为排队中
            await FileInfo.filter(file_hash=file_hash).update(
                analysis_status=AnalysisStatus.ANALYZING.value,
                processed_status="任务已加入处理队列，等待执行...\n"
            )

        except Exception as e:
            logger.error(f"提交任务失败 {file_hash}: {e}")

    return ParseDocumentResponse(
        code=0,
        message=f"已提交 {len(task_ids)} 个解析任务到队列"
    )
async def initialize_document_queue():
    """初始化文档处理队列"""
    # await document_queue.initialize()
    await document_queue.start_workers()
    logger.info("文档处理队列已启动")