import asyncio
import uuid
from typing import Dict, List, Set
from datetime import datetime

from app.utils.logger import setup_logger
from app.models.database import FileInfo
from app.models.database.document import AnalysisStatus

logger = setup_logger(__name__)

class DocumentQueue:
    """文档处理队列，支持后台处理和并发控制"""
    
    def __init__(self, max_workers=2):
        """初始化队列
        
        Args:
            max_workers: 最大并发工作数量
        """
        self.queue = asyncio.Queue()
        self.max_workers = max_workers
        self.running_tasks: Set[str] = set()  # 正在处理的任务集合
        self.workers: List[asyncio.Task] = []
        self._running = False
        self._lock = asyncio.Lock()
        self._has_new_task = asyncio.Event()  # 新增：用于通知有新任务
    
    async def start(self):
        """启动工作进程"""
        if self._running:
            return
            
        self._running = True
        
        # 创建工作进程
        for i in range(self.max_workers):
            worker = asyncio.create_task(self._worker(f"worker-{i}"))
            self.workers.append(worker)
            
        logger.info(f"文档处理队列已启动，最大并发数: {self.max_workers}")
    
    async def stop(self):
        """停止工作进程"""
        self._running = False
        
        # 通知所有等待的工作进程
        self._has_new_task.set()
        
        # 取消所有工作进程
        for worker in self.workers:
            if not worker.done():
                worker.cancel()
                
        # 等待工作进程结束
        if self.workers:
            await asyncio.gather(*self.workers, return_exceptions=True)
            
        self.workers.clear()
        logger.info("文档处理队列已停止")
    
    async def add_task(self, file_hash: str) -> str:
        """添加文档处理任务
        
        Args:
            file_hash: 文件哈希值
            
        Returns:
            task_id: 任务ID
        """
        task_id = str(uuid.uuid4())
        
        # 添加到队列
        await self.queue.put((task_id, file_hash))
        logger.info(f"任务已添加到队列: {task_id}, 文件: {file_hash}")
        
        # 通知工作进程有新任务
        self._has_new_task.set()
        
        return task_id
    
    async def _worker(self, worker_id: str):
        """工作进程"""
        logger.info(f"工作进程 {worker_id} 已启动")
        
        while self._running:
            try:
                # 检查队列是否为空
                if self.queue.empty():
                    # 重置事件并等待新任务通知
                    self._has_new_task.clear()
                    # 使用带超时的等待，以便定期检查running状态
                    try:
                        await asyncio.wait_for(self._has_new_task.wait(), timeout=5.0)
                    except asyncio.TimeoutError:
                        # 超时只是为了检查running状态，不需要特殊处理
                        continue
                    
                    # 如果不再运行，退出循环
                    if not self._running:
                        break
                
                # 尝试立即获取任务，不会阻塞
                try:
                    task_id, file_hash = self.queue.get_nowait()
                except asyncio.QueueEmpty:
                    # 队列可能被其他工作进程消费空了，继续循环
                    continue
                
                # 记录正在处理的任务
                async with self._lock:
                    self.running_tasks.add(task_id)
                
                try:
                    logger.info(f"工作进程 {worker_id} 开始处理任务: {task_id}, 文件: {file_hash}")
                    
                    # 调用文档处理器
                    from app.service.document.document_processor import document_processor
                    await document_processor.initialize()
                    await document_processor.process_document(file_hash)
                    
                    logger.info(f"任务处理完成: {task_id}")
                except Exception as e:
                    logger.error(f"任务处理失败: {task_id}, 错误: {str(e)}")
                    # 更新文件状态为失败
                    await FileInfo.filter(file_hash=file_hash).update(
                        analysis_status=AnalysisStatus.FAILED.value,
                        processed_status=f"处理失败: {str(e)}"
                    )
                finally:
                    # 从正在处理的任务集合中移除
                    async with self._lock:
                        self.running_tasks.discard(task_id)
                    
                    # 标记队列任务完成
                    self.queue.task_done()
            
            except asyncio.CancelledError:
                logger.info(f"工作进程 {worker_id} 被取消")
                break
            except Exception as e:
                logger.error(f"工作进程 {worker_id} 异常: {str(e)}")
                await asyncio.sleep(1)  # 出错后短暂等待
        
        logger.info(f"工作进程 {worker_id} 已停止")
    
    def get_status(self) -> Dict:
        """获取队列状态"""
        return {
            "running": self._running,
            "queue_size": self.queue.qsize(),
            "running_tasks": len(self.running_tasks),
            "max_workers": self.max_workers
        }

# 全局队列实例
document_queue = DocumentQueue(max_workers=2)