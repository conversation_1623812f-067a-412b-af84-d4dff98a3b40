
import asyncio
import uuid
from typing import Dict, Any, Optional
from datetime import datetime
from dataclasses import dataclass

from app.utils.logger import setup_logger

logger = setup_logger(__name__)


@dataclass
class TaskItem:
    """任务项，支持优先级排序"""
    priority: int
    created_at: datetime
    task_data: Dict[str, Any]

    def __lt__(self, other):
        if self.priority != other.priority:
            return self.priority > other.priority
        return self.created_at < other.created_at


class AsyncDocumentQueueWork:
    def __init__(self):
        self.processor = None
        self.task_queue: asyncio.Queue = None
        self.workers: Dict[str, asyncio.Task] = {}
        self.max_concurrent_tasks = 1
        self._running = False

        # 任务状态管理
        self.processing_tasks: Dict[str, Dict[str, Any]] = {}
        self.completed_tasks: Dict[str, Dict[str, Any]] = {}
        self._lock = asyncio.Lock()

    async def initialize(self):
        """初始化队列和处理器"""
        if self.task_queue is None:
            self.task_queue = asyncio.Queue(maxsize=1000)

        if self.processor is None:
            from app.service.document.document_processor import DocumentProcessor
            self.processor = DocumentProcessor()
            await self.processor.initialize()

        logger.info("AsyncDocumentQueue 初始化完成")

    async def add_task(self, file_hash: str,knowledge_name: str, priority: int = 0) -> str:

        """添加解析任务到队列"""
        task_id = str(uuid.uuid4())
        task_data = {
            "task_id": task_id,
            "file_hash": file_hash,
            "knowledge_name": knowledge_name,
            "priority": priority,
            "created_at": datetime.now().isoformat(),
            "status": "queued"
        }

        task_item = TaskItem(
            priority=priority,
            created_at=datetime.now(),
            task_data=task_data
        )

        try:
            self.task_queue.put_nowait(task_item)
            logger.info(f"任务已加入队列: {task_id} (file_hash: {file_hash})")
            return task_id
        except asyncio.QueueFull:
            logger.error(f"队列已满，无法添加任务: {task_id}")
            raise Exception("任务队列已满")

    async def start_workers(self):
        """启动工作进程"""
        if self._running:
            logger.warning("工作进程已在运行中")
            return

        self._running = True

        # 确保初始化完成
        await self.initialize()

        # 启动工作进程
        for i in range(self.max_concurrent_tasks):
            worker_id = f"worker_{i}"
            if worker_id not in self.workers or self.workers[worker_id].done():
                self.workers[worker_id] = asyncio.create_task(
                    self._worker_loop(worker_id)
                )

        logger.info(f"启动了 {self.max_concurrent_tasks} 个工作进程")

    async def _worker_loop(self, worker_id: str):
        """工作进程主循环"""
        logger.info(f"工作进程 {worker_id} 开始运行")

        while self._running:
            try:
                # 获取任务，设置超时避免无限等待
                try:
                    task_item = await asyncio.wait_for(
                        self.task_queue.get(),
                        timeout=1.0
                    )
                except asyncio.TimeoutError:
                    continue

                # 处理任务
                await self._process_task(worker_id, task_item.task_data)

                # 标记任务完成
                self.task_queue.task_done()

            except asyncio.CancelledError:
                logger.info(f"工作进程 {worker_id} 被取消")
                break
            except Exception as e:
                logger.error(f"工作进程 {worker_id} 异常: {e}")
                await asyncio.sleep(1)  # 短暂等待后继续

        logger.info(f"工作进程 {worker_id} 已停止")

    async def _process_task(self, worker_id: str, task_data: Dict[str, Any]):
        """处理单个任务"""
        task_id = task_data["task_id"]
        file_hash = task_data["file_hash"]
        knowledge_name = task_data["knowledge_name"]

        # 添加到处理中集合
        async with self._lock:
            self.processing_tasks[task_id] = {
                "task_id": task_id,
                "file_hash": file_hash,
                "worker_id": worker_id,
                "started_at": datetime.now().isoformat(),
                "status": "processing"
            }

        try:
            logger.info(f"工作进程 {worker_id} 开始处理任务: {task_id}")

            # 直接调用处理器，不使用线程池
            success = await self.processor.process_document(file_hash, knowledge_name)
            # 等待任务完成，但允许其他协程运行
            await self._complete_task(task_id, success)

        except Exception as e:
            logger.error(f"任务执行异常 {task_id}: {e}")
            await self._complete_task(task_id, False, str(e))
        finally:
            # 从处理中集合移除
            async with self._lock:
                self.processing_tasks.pop(task_id, None)


    async def _complete_task(self, task_id: str, success: bool, error_msg: str = None):
        """完成任务处理"""
        completion_data = {
            "task_id": task_id,
            "completed_at": datetime.now().isoformat(),
            "success": success,
            "error": error_msg
        }

        async with self._lock:
            self.completed_tasks[task_id] = completion_data

        if success:
            logger.info(f"任务处理成功: {task_id}")
        else:
            logger.error(f"任务处理失败: {task_id}, 错误: {error_msg}")

    async def stop_workers(self):
        """停止所有工作进程"""
        logger.info("开始停止工作进程...")
        self._running = False

        # 等待队列中的任务完成
        if self.task_queue and not self.task_queue.empty():
            logger.info("等待队列中的任务完成...")
            await self.task_queue.join()

        # 取消所有工作进程
        for worker_id, task in self.workers.items():
            if not task.done():
                task.cancel()

        # 等待所有工作进程完成
        if self.workers:
            await asyncio.gather(*self.workers.values(), return_exceptions=True)

        self.workers.clear()
        logger.info("所有工作进程已停止")

    def get_status(self) -> Dict[str, Any]:
        """获取队列状态"""
        return {
            "running": self._running,
            "queue_size": self.task_queue.qsize() if self.task_queue else 0,
            "processing_count": len(self.processing_tasks),
            "completed_count": len(self.completed_tasks),
            "workers_count": len([w for w in self.workers.values() if not w.done()]),
            "max_concurrent_tasks": self.max_concurrent_tasks
        }


document_queue = AsyncDocumentQueueWork()