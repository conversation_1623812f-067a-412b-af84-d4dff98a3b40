from typing import List, Dict, Any

from app.models.database import FileInfo
from app.models.document_upload import DocumentListResponse


async def list_documents(knowledge_hash: str, page: int = 1, page_size: int = 10) -> DocumentListResponse:
    # 参数验证
    if page < 1:
        page = 1
    
    if page_size < 1 or page_size > 100:
        page_size = 10
    
    # 从数据库中获取文件信息
    offset = (page - 1) * page_size
    
    # 获取当前页的文档
    documents = await FileInfo.filter(knowledge_name=knowledge_hash).offset(offset).limit(page_size).all()
    
    # 获取总记录数
    total = await FileInfo.filter(knowledge_name=knowledge_hash).count()
    
    # 转换为字典并构建响应
    return DocumentListResponse(
        code=0,
        message="success",
        documents=[doc.to_dict() for doc in documents],
        total=total
    )
