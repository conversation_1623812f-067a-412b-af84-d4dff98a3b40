import re
from typing import Union, List, Optional

import httpx
import asyncio

from app.config.settings import settings
from app.utils.exceptions import handle_async_exceptions, ModelClientError
from app.utils.logger import setup_logger
from openai import AsyncOpenAI
import instructor

logger = setup_logger(__name__)





class ModelClient:
    """统一模型客户端"""

    def __init__(self):
        self.chat_config = settings.get_chat_config()
        self.embedding_config = settings.get_embedding_config()
        self.rerank_config = settings.get_rerank_config()
        self.logger = logger

        # 初始化多个API客户端
        self._init_clients()

    def _init_clients(self):
        """初始化多个API客户端"""
        # LLM客户端
        self.llm_client = AsyncOpenAI(
            api_key=self.chat_config.api_key,
            base_url=self.chat_config.base_url,
        )
        # Embedding客户端
        self.embedding_client = AsyncOpenAI(
            api_key=self.embedding_config.api_key,
            base_url=self.embedding_config.base_url,
        )
        # Reranker客户端
        self.reranker_client = AsyncOpenAI(
            api_key=self.rerank_config.api_key,
            base_url=self.rerank_config.base_url
        )
        # 初始化Instructor客户端 (用于结构化输出)
        self.instructor_client = instructor.from_openai(self.llm_client)
        # HTTP客户端用于直接API调用
        self.http_client = httpx.AsyncClient(
            timeout=30,
        )

        self.logger.info(f"模型客户端初始化完成:")
        self.logger.info(f"  - LLM: {self.chat_config.base_url} ({self.chat_config.model})")
        self.logger.info(f"  - Embedding: {self.embedding_config.base_url} ({self.embedding_config.model})")
        self.logger.info(f"  - Reranker: {self.rerank_config.base_url} ({self.rerank_config.model})")

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.http_client.aclose()

    @handle_async_exceptions
    async def check_health(self):
        """检查所有模型服务健康状态"""
        try:
            # 1. 检查LLM模型服务
            self.http_client.headers.update({"Authorization": f"Bearer {self.chat_config.api_key}"})
            llm_response = await self.http_client.get(f"{self.chat_config.base_url}/models")
            if llm_response.status_code != 200:
                self.logger.warning("LLM 模型服务不可用")
            else:
                self.logger.info("LLM 模型服务正常")


            # 2. 检查VLM模型服务
            self.http_client.headers.update({"Authorization": f"Bearer {self.embedding_config.api_key}"})
            vlm_response = await self.http_client.get(f"{self.embedding_config.base_url}/models")
            if vlm_response.status_code != 200:
                self.logger.warning("Embedding 模型服务不可用")
            else:
                self.logger.info("Embedding 模型服务正常")

            # 3. 检查Embedding模型服务
            self.http_client.headers.update({"Authorization": f"Bearer {self.rerank_config.api_key}"})
            emb_response = await self.http_client.get(f"{self.rerank_config.base_url}/models")
            if emb_response.status_code != 200:
                self.logger.warning("Rerank 模型服务不可用")
            else:
                self.logger.info("Rerank 模型服务正常")


        except Exception as e:
            self.logger.error(f"模型服务健康检查失败: {e}")
            raise ModelClientError(f"模型服务健康检查失败: {str(e)}")

    @handle_async_exceptions
    async def get_embeddings(
            self,
            texts: Union[str, List[str]],
            model: Optional[str] = None
    ) -> List[List[float]]:
        """
        获取文本嵌入向量

        Args:
            texts: 文本或文本列表
            model: 嵌入模型名称

        Returns:
            嵌入向量列表
        """
        try:
            # 确保输入是列表
            if isinstance(texts, str):
                texts = [texts]

            # 调用嵌入API（使用专用的embedding客户端）
            response = await self.embedding_client.embeddings.create(
                model=model or self.embedding_config.model,
                input=texts
            )

            embeddings = [data.embedding for data in response.data]
            # 输出嵌入维度信息
            self.logger.info(f"嵌入生成成功，文本数量: {len(texts)}")
            return embeddings

        except Exception as e:
            self.logger.error(f"嵌入生成失败: {e}")
            raise ModelClientError(f"嵌入生成失败: {str(e)}")

    @handle_async_exceptions
    async def generate_summary(
            self,
            texts: list[dict],
            model: Optional[str] = None
    ) -> str:
        """
        生成文档摘要，支持超长文档的分片处理和整合
        
        Args:
            texts: 文档分片列表，每个分片包含 tokens 数量和文本
            model: 使用的模型名称

        Returns:
            整合后的文档摘要
        """
        try:
            # 存储分片文本
            chunked_texts = []
            current_chunk_tokens = 0
            current_chunk_texts = []

            # 遍历所有分片，按 token 数量分组处理
            for i, item in enumerate(texts):
                tokens = item["tokens"]
                text = item["text"]

                # 如果当前分片加上新文本超过 20000 tokens，处理当前分片并保留重叠内容
                if current_chunk_tokens + tokens > 20000 and current_chunk_texts:
                    # 保存当前分片
                    chunked_texts.append(current_chunk_texts)

                    # 从当前分片末尾取重叠内容（取最后1-2个文本作为重叠）
                    overlap_count = min(2, len(current_chunk_texts))
                    if overlap_count > 0:
                        overlap_texts = current_chunk_texts[-overlap_count:]
                        overlap_tokens = sum(texts[j]["tokens"] for j in range(max(0, i - overlap_count), i))
                    else:
                        overlap_texts = []
                        overlap_tokens = 0

                    # 开始新的分片，包含重叠内容和当前文本
                    current_chunk_texts = overlap_texts + [text]
                    current_chunk_tokens = overlap_tokens + tokens
                else:
                    # 继续累积当前分片
                    current_chunk_tokens += tokens
                    current_chunk_texts.append(text)

            # 处理最后一个分片
            if current_chunk_texts:
                chunked_texts.append(current_chunk_texts)
            
            # 并发生成所有分片摘要
            tasks = [self._generate_chunk_summary(chunk, model) for chunk in chunked_texts]
            summaries = await asyncio.gather(*tasks)
            
            # 如果只有一个摘要，直接返回
            if len(summaries) == 1:
                return summaries[0]
            else:
                # 生成最终整合摘要
                final_summary = await self._generate_final_summary(list(summaries), model)
                return final_summary
        
        except Exception as e:
            self.logger.error(f"摘要生成失败: {e}")
            raise ModelClientError(f"摘要生成失败: {str(e)}")
    
    async def _generate_chunk_summary(self, texts: list[str], model: Optional[str]) -> str:
        """生成单个分片的摘要"""
        # 拼接分片文本
        chunk_text = "\n\n".join(texts)
        
        # 构建提示词
        prompt = f"""请根据以下文档内容生成一份知识库简报，要求：
使用简体中文回答。
核心信息提炼：准确提取文档中的主要观点、关键事实、时间、人物、数据等关键信息。

结构化表达：按照 【背景/主题】—【关键细节】—【结论或影响】 的逻辑组织内容，保证简洁清晰。

简报风格：语言简练、专业，可快速阅读，便于后续检索与索引。

字数控制：不超过 500 字，优先保留对理解文档最重要的信息。

禁止：不要加入推测性内容、个人观点或无关细节。

{chunk_text}
"""
        
        # 调用LLM生成摘要
        response = await self.llm_client.chat.completions.create(
            model=model or self.chat_config.model,
            messages=[{"role": "user", "content": prompt}],
            max_tokens=500,
            temperature=0.5,
            top_p=1.0,
            frequency_penalty=0.0,
            presence_penalty=0.0,
            extra_body = {
                "chat_template_kwargs": {"enable_thinking": False},
            },
        )
        
        # 记录分片摘要生成耗时
        self.logger.info(f"分片摘要生成成功，token 数量: {len(chunk_text.split())}")
        return self._clean_response_content(response.choices[0].message.content.strip())


    async def _generate_final_summary(self, summaries: list[str], model: Optional[str]) -> str:
        """整合所有分片摘要生成最终摘要"""
        # 拼接所有分片摘要
        combined_summary = "\n\n".join(summaries)
        
        # 构建提示词
        prompt = f"""请根据以下多段分片摘要，整合生成一份完整的文档摘要，要求：
使用简体中文回答。
整体连贯性：以文档原有逻辑为主线，合理衔接各分片内容，避免碎片化表述。

去重与合并：合并内容相似或重复的部分，保持信息唯一且简洁。

突出核心内容：优先保留文档中的主要观点、关键事实、重要数据及结论，弱化细枝末节。

逻辑结构清晰：建议按照 【背景/主题】—【关键细节】—【结论或影响】 的结构组织内容。

字数控制：全文不超过 1000 字，但保证信息完整、可快速阅读。

风格要求：简洁、专业，适合知识库存储与后续检索，不添加主观推断。

{combined_summary}
"""
        
        # 调用LLM生成最终摘要
        response = await self.llm_client.chat.completions.create(
            model=model or self.chat_config.model,
            messages=[{"role": "user", "content": prompt}],
            max_tokens=1000,
            temperature=0.7,
            top_p=1.0,
            frequency_penalty=0.0,
            presence_penalty=0.0,
            extra_body = {
                "chat_template_kwargs": {"enable_thinking": False},
            },
        )
        
        # 记录最终摘要生成耗时
        self.logger.info(f"最终摘要生成成功，总分片数: {len(summaries)}")
        return self._clean_response_content(response.choices[0].message.content.strip())

    @staticmethod
    def _clean_response_content(content: str) -> str:
        """清理响应内容，移除thinking标签"""

        if not content:
            return content
        # 移除 <think>...</think> 标签及其内容
        cleaned_content = re.sub(r'<think>.*?</think>', '', content, flags=re.DOTALL)
        return cleaned_content.strip()


model_client = ModelClient()