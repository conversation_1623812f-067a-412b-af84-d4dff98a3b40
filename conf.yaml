# 应用配置
app_config:
  name: RAG Document System
  version: 1.0.0
  debug: true
  log_level: INFO

# 大模型配置
vllm_config:
  chat:
    base_url: http://*************:15555/chat/v1
    api_key: iBYcdv999bDCSOyuNjorZvfaMEooeq1gRBHRwP5L7GGLGMqxBXrSntKsHTiP72vc
    model: Qwen3-30B-A3B-GPTQ-Int4
  embedding:
      base_url: http://*************:15555/embedding/v1
      api_key: iBYcdv999bDCSOyuNjorZvfaMEooeq1gRBHRwP5L7GGLGMqxBXrSntKsHTiP72vc
      model: Qwen3-Embedding-4B
  rerank:
      base_url: http://*************:15555/rerank/v1
      api_key: iBYcdv999bDCSOyuNjorZvfaMEooeq1gRBHRwP5L7GGLGMqxBXrSntKsHTiP72vc
      model: qwen3-8b-fp8

# 数据库配置
qdrant_config:
  host: localhost
  port: 16333
  vector_size: 2560
  collection_name: documents

postgres_config:
    host: localhost
    port: 15432
    dbname: rag_system
    user: postgres
    password: your_postgres_password

neo4j_config:
    uri: bolt://localhost:17687
    user: neo4j
    password: your_password
    database: neo4j
    max_connection_lifetime: 3600
    max_connection_pool_size: 50

minio_config:
  endpoint: localhost:19000
  access_key: minioadmin
  secret_key: minioadmin
  secure: false
  default_bucket: documents

redis_config:
  host: localhost
  port: 16379
  password: your_redis_password
  db: 0


docling_config:
  embedding_model: "./model/Qwen3-Embedding-8B"