#!/usr/bin/env python3
"""
检查和清理数据库中重复的文件记录
"""

import asyncio
import sys
import os
from collections import defaultdict

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.models.database import FileInfo
from app.database.postgres_manager import postgres_manager
from app.utils.logger import setup_logger

logger = setup_logger(__name__)

async def check_duplicate_file_hashes():
    """检查重复的file_hash记录"""
    print("🔍 检查数据库中的重复file_hash记录...")
    
    try:
        # 初始化数据库连接
        await postgres_manager.init()
        
        # 获取所有文件记录
        all_files = await FileInfo.all()
        
        # 按file_hash + knowledge_name组合分组
        composite_key_groups = defaultdict(list)
        file_hash_only_groups = defaultdict(list)

        for file_info in all_files:
            # 复合键分组
            composite_key = f"{file_info.file_hash}#{file_info.knowledge_name}"
            composite_key_groups[composite_key].append(file_info)

            # 仅file_hash分组（用于检查跨知识库的重复）
            file_hash_only_groups[file_info.file_hash].append(file_info)

        # 找出复合键重复的记录
        composite_duplicates = {
            key: files for key, files in composite_key_groups.items()
            if len(files) > 1
        }

        # 找出仅file_hash重复的记录（跨知识库）
        cross_kb_duplicates = {
            file_hash: files for file_hash, files in file_hash_only_groups.items()
            if len(files) > 1
        }
        
        if not composite_duplicates and not cross_kb_duplicates:
            print("✅ 未发现重复记录")
            return []

        duplicate_info = []

        # 报告复合键重复（同一知识库内的重复）
        if composite_duplicates:
            print(f"⚠️  发现 {len(composite_duplicates)} 个同知识库内的重复记录:")

            for composite_key, files in composite_duplicates.items():
                file_hash, knowledge_name = composite_key.split('#', 1)
                print(f"\n📄 file_hash: {file_hash}, knowledge_name: {knowledge_name}")
                print(f"   重复数量: {len(files)}")

                for i, file_info in enumerate(files):
                    print(f"   记录 {i+1}: ID={file_info.id}, 文件名={file_info.original_filename}")
                    print(f"           创建时间={file_info.created_at}")

                duplicate_info.append({
                    "type": "same_kb_duplicate",
                    "file_hash": file_hash,
                    "knowledge_name": knowledge_name,
                    "count": len(files),
                    "records": [
                        {
                            "id": f.id,
                            "filename": f.original_filename,
                            "knowledge_name": f.knowledge_name,
                            "created_at": f.created_at
                        } for f in files
                    ]
                })

        # 报告跨知识库重复（仅供参考，通常不需要清理）
        if cross_kb_duplicates:
            print(f"\n📊 发现 {len(cross_kb_duplicates)} 个跨知识库的file_hash重复（仅供参考）:")

            for file_hash, files in cross_kb_duplicates.items():
                if len(set(f.knowledge_name for f in files)) > 1:  # 确实是跨知识库的
                    print(f"\n📄 file_hash: {file_hash}")
                    print(f"   涉及知识库: {set(f.knowledge_name for f in files)}")
                    print(f"   总记录数: {len(files)}")

        return duplicate_info
        
    except Exception as e:
        logger.error(f"检查重复记录失败: {e}")
        return []

async def cleanup_duplicate_records(duplicate_info: list, dry_run: bool = True):
    """清理重复记录，保留最新的一条"""
    if not duplicate_info:
        print("📝 没有需要清理的重复记录")
        return
    
    print(f"\n🧹 开始清理重复记录 (dry_run={dry_run})...")
    
    total_to_delete = 0
    
    for dup in duplicate_info:
        file_hash = dup["file_hash"]
        records = dup["records"]
        
        # 按创建时间排序，保留最新的
        records.sort(key=lambda x: x["created_at"], reverse=True)
        keep_record = records[0]
        delete_records = records[1:]
        
        print(f"\n📄 处理 file_hash: {file_hash}")
        print(f"   保留记录: ID={keep_record['id']}, 文件名={keep_record['filename']}")
        print(f"   删除记录数量: {len(delete_records)}")
        
        for record in delete_records:
            print(f"   将删除: ID={record['id']}, 文件名={record['filename']}")
            total_to_delete += 1
            
            if not dry_run:
                try:
                    # 实际删除记录
                    file_to_delete = await FileInfo.get(id=record['id'])
                    await file_to_delete.delete()
                    print(f"   ✅ 已删除记录 ID={record['id']}")
                except Exception as e:
                    print(f"   ❌ 删除记录 ID={record['id']} 失败: {e}")
    
    if dry_run:
        print(f"\n📊 预览结果: 将删除 {total_to_delete} 条重复记录")
        print("💡 使用 --execute 参数执行实际清理")
    else:
        print(f"\n✅ 清理完成: 删除了 {total_to_delete} 条重复记录")

async def verify_file_hash_uniqueness():
    """验证清理后的file_hash唯一性"""
    print("\n🔍 验证file_hash唯一性...")
    
    try:
        # 重新检查重复记录
        duplicate_info = await check_duplicate_file_hashes()
        
        if not duplicate_info:
            print("✅ file_hash唯一性验证通过")
            return True
        else:
            print(f"❌ 仍存在 {len(duplicate_info)} 个重复的file_hash")
            return False
            
    except Exception as e:
        logger.error(f"验证唯一性失败: {e}")
        return False

async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="检查和清理数据库中的重复文件记录")
    parser.add_argument("--execute", action="store_true", help="执行实际清理（默认为预览模式）")
    parser.add_argument("--verify-only", action="store_true", help="仅验证唯一性，不执行清理")
    
    args = parser.parse_args()
    
    print("🚀 开始检查数据库重复记录...\n")
    
    try:
        if args.verify_only:
            # 仅验证
            await verify_file_hash_uniqueness()
        else:
            # 检查重复记录
            duplicate_info = await check_duplicate_file_hashes()
            
            if duplicate_info:
                # 清理重复记录
                await cleanup_duplicate_records(duplicate_info, dry_run=not args.execute)
                
                if args.execute:
                    # 验证清理结果
                    await verify_file_hash_uniqueness()
            
        print("\n🎉 操作完成!")
        
    except Exception as e:
        logger.error(f"操作失败: {e}")
        print(f"❌ 操作失败: {e}")
    finally:
        # 关闭数据库连接
        await postgres_manager.close()

if __name__ == "__main__":
    asyncio.run(main())
